name: Deploy to Production

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy (e.g., v1.0.0)'
        required: true
        type: string
      skip_staging_check:
        description: 'Skip staging verification'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  PRODUCTION_APP_NAME: sanad-paim

jobs:
  # Pre-production validation
  pre-production-validation:
    name: Pre-production Validation
    runs-on: ubuntu-latest
    
    outputs:
      staging-healthy: ${{ steps.staging-check.outputs.healthy }}
      production-ready: ${{ steps.validation.outputs.ready }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.version }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run comprehensive tests
      run: |
        npm run test:quality
        npm run test:e2e
        
    - name: Security scan
      run: |
        npm audit --audit-level=high
        
    - name: Check staging health
      id: staging-check
      if: github.event.inputs.skip_staging_check != 'true'
      run: |
        if curl -f -s "https://staging-sanad-paim.ondigitalocean.app/health"; then
          echo "healthy=true" >> $GITHUB_OUTPUT
          echo "✅ Staging environment is healthy"
        else
          echo "healthy=false" >> $GITHUB_OUTPUT
          echo "❌ Staging environment is not healthy"
        fi
        
    - name: Production readiness check
      id: validation
      run: |
        staging_ok="${{ steps.staging-check.outputs.healthy }}"
        skip_staging="${{ github.event.inputs.skip_staging_check }}"
        
        if [ "$staging_ok" = "true" ] || [ "$skip_staging" = "true" ]; then
          echo "ready=true" >> $GITHUB_OUTPUT
          echo "✅ Production deployment approved"
        else
          echo "ready=false" >> $GITHUB_OUTPUT
          echo "❌ Production deployment blocked"
        fi

  # Build production image
  build-production:
    name: Build Production Image
    runs-on: ubuntu-latest
    needs: pre-production-validation
    if: needs.pre-production-validation.outputs.production-ready == 'true'
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.version }}
        
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=raw,value=${{ github.event.inputs.version }}
          type=raw,value=production-latest
          type=raw,value=latest
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.digitalocean
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64
        build-args: |
          NODE_ENV=production

  # Manual approval gate
  approval:
    name: Manual Approval
    runs-on: ubuntu-latest
    needs: [pre-production-validation, build-production]
    if: needs.pre-production-validation.outputs.production-ready == 'true'
    
    environment:
      name: production-approval
      
    steps:
    - name: Manual approval checkpoint
      run: |
        echo "🚨 Production deployment requires manual approval"
        echo "Version: ${{ github.event.inputs.version }}"
        echo "Image: ${{ needs.build-production.outputs.image-tag }}"
        echo "Staging health: ${{ needs.pre-production-validation.outputs.staging-healthy }}"

  # Deploy to production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [pre-production-validation, build-production, approval]
    
    environment:
      name: production
      url: https://${{ env.PRODUCTION_APP_NAME }}.ondigitalocean.app
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.version }}
        
    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        
    - name: Backup current deployment
      run: |
        echo "Creating deployment backup..."
        timestamp=$(date +%Y%m%d_%H%M%S)
        
        # Get current app spec
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "${{ env.PRODUCTION_APP_NAME }}" | awk '{print $1}')
        doctl apps spec get $APP_ID > "backup_${timestamp}.yaml"
        
        echo "Backup created: backup_${timestamp}.yaml"
        
    - name: Update production app
      run: |
        # Update the existing production app with new image
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "${{ env.PRODUCTION_APP_NAME }}" | awk '{print $1}')
        
        # The production app is already configured in Digital Ocean
        # This will trigger a new deployment with the latest code from master branch
        doctl apps create-deployment $APP_ID --wait
        
    - name: Wait for deployment
      run: |
        echo "Waiting for deployment to complete..."
        sleep 120

  # Post-deployment verification
  verify-production:
    name: Verify Production Deployment
    runs-on: ubuntu-latest
    needs: deploy-production
    
    steps:
    - name: Health check
      run: |
        max_attempts=15
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
          echo "Health check attempt $attempt/$max_attempts..."
          
          if curl -f -s "https://${{ env.PRODUCTION_APP_NAME }}.ondigitalocean.app/health"; then
            echo "✅ Production deployment healthy"
            break
          fi
          
          if [ $attempt -eq $max_attempts ]; then
            echo "❌ Production deployment failed health checks"
            exit 1
          fi
          
          echo "❌ Health check failed, retrying in 30 seconds..."
          sleep 30
          attempt=$((attempt + 1))
        done
        
    - name: Detailed health check
      run: |
        response=$(curl -s "https://${{ env.PRODUCTION_APP_NAME }}.ondigitalocean.app/health/detailed")
        echo "Detailed health check response:"
        echo "$response" | jq '.' || echo "$response"
        
    - name: Performance verification
      run: |
        echo "Running performance verification..."
        
        for i in {1..5}; do
          start_time=$(date +%s%N)
          curl -s "https://${{ env.PRODUCTION_APP_NAME }}.ondigitalocean.app/health" > /dev/null
          end_time=$(date +%s%N)
          response_time=$(( (end_time - start_time) / 1000000 ))
          
          echo "Request $i: ${response_time}ms"
          
          if [ $response_time -gt 5000 ]; then
            echo "⚠️ Slow response detected: ${response_time}ms"
          fi
        done

  # Rollback capability
  rollback:
    name: Rollback if Needed
    runs-on: ubuntu-latest
    needs: [deploy-production, verify-production]
    if: failure() && needs.deploy-production.result == 'success'
    
    steps:
    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        
    - name: Rollback deployment
      run: |
        echo "🔄 Initiating rollback..."
        
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "${{ env.PRODUCTION_APP_NAME }}" | awk '{print $1}')
        
        # Get previous deployment
        PREV_DEPLOYMENT=$(doctl apps list-deployments $APP_ID --format ID --no-header | sed -n '2p')
        
        if [ -n "$PREV_DEPLOYMENT" ]; then
          echo "Rolling back to deployment: $PREV_DEPLOYMENT"
          # Note: Digital Ocean doesn't support direct rollback via CLI
          # This would require manual intervention or API calls
          echo "❌ Automatic rollback not available. Manual intervention required."
        else
          echo "❌ No previous deployment found for rollback"
        fi

  # Final notification
  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [pre-production-validation, build-production, deploy-production, verify-production]
    if: always()
    
    steps:
    - name: Success notification
      if: needs.verify-production.result == 'success'
      run: |
        echo "🎉 Production deployment successful!"
        echo "🔗 URL: https://${{ env.PRODUCTION_APP_NAME }}.ondigitalocean.app"
        echo "📦 Version: ${{ github.event.inputs.version }}"
        echo "🏷️ Image: ${{ needs.build-production.outputs.image-tag }}"
        
    - name: Failure notification
      if: needs.verify-production.result == 'failure' || needs.deploy-production.result == 'failure'
      run: |
        echo "❌ Production deployment failed"
        echo "🔄 Consider running rollback procedures"
        echo "📋 Check deployment logs for details"
        exit 1

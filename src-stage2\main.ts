import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Enable CORS
  app.enableCors();
  
  // Get port from environment
  const port = process.env.PORT || 3000;
  
  console.log(`🚀 Sanad PAIM Stage 2 - NestJS Core starting on port ${port}`);
  console.log(`📍 Health check: http://localhost:${port}/health`);
  console.log(`📍 Status: http://localhost:${port}/status`);
  console.log(`🎯 Stage: Stage 2 - NestJS Core Dependencies`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  
  await app.listen(port);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

@Injectable()
export class OpenaiService {
  private openai: OpenAI | null = null;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (apiKey) {
      this.openai = new OpenAI({
        apiKey: apiKey,
      });
    }
  }

  async testConnection() {
    if (!this.openai) {
      return {
        status: 'error',
        message: 'OpenAI API key not configured',
        configured: false,
        timestamp: new Date().toISOString()
      };
    }

    try {
      // Simple test to verify connection
      const response = await this.openai.models.list();
      return {
        status: 'success',
        message: 'OpenAI connection successful',
        configured: true,
        modelsAvailable: response.data.length,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        message: 'OpenAI connection failed',
        error: error.message,
        configured: true,
        timestamp: new Date().toISOString()
      };
    }
  }

  async chat(message: string) {
    if (!this.openai) {
      throw new Error('OpenAI not configured');
    }

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant for the Sanad PAIM application. Keep responses brief and helpful.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 150,
        temperature: 0.7,
      });

      return {
        status: 'success',
        message: response.choices[0]?.message?.content || 'No response',
        usage: response.usage,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`OpenAI chat failed: ${error.message}`);
    }
  }
}

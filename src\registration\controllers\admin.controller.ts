import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  Logger,
  UseGuards,
  Headers,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { RegistrationService } from '../services/registration.service';
import { WhitelistService } from '../services/whitelist.service';
import { RegistrationStatus } from '../../interfaces/registration.interface';

@ApiTags('admin')
@Controller('admin')
@UseGuards(ThrottlerGuard)
export class AdminController {
  private readonly logger = new Logger(AdminController.name);

  constructor(
    private readonly registrationService: RegistrationService,
    private readonly whitelistService: WhitelistService,
  ) {}

  @Get('registrations')
  @ApiOperation({ summary: 'Get all registration requests' })
  @ApiResponse({
    status: 200,
    description: 'Registration requests retrieved successfully',
  })
  @ApiHeader({
    name: 'x-admin-key',
    description: 'Admin API key',
    required: true,
  })
  async getAllRegistrations(
    @Headers('x-admin-key') adminKey: string,
    @Query('status') status?: RegistrationStatus,
  ) {
    if (!this.validateAdminKey(adminKey)) {
      return { error: 'Unauthorized', statusCode: HttpStatus.UNAUTHORIZED };
    }

    try {
      let registrations;

      if (status) {
        registrations =
          await this.registrationService.getRegistrationsByStatus(status);
      } else {
        registrations =
          await this.registrationService.getAllRegistrationRequests();
      }

      return {
        registrations,
        total: registrations.length,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to get registrations: ${errorMessage}`,
        errorStack,
      );
      return {
        error: 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  @Get('registrations/stats')
  @ApiOperation({ summary: 'Get registration statistics' })
  @ApiResponse({
    status: 200,
    description: 'Registration statistics retrieved successfully',
  })
  @ApiHeader({
    name: 'x-admin-key',
    description: 'Admin API key',
    required: true,
  })
  async getRegistrationStats(@Headers('x-admin-key') adminKey: string) {
    if (!this.validateAdminKey(adminKey)) {
      return { error: 'Unauthorized', statusCode: HttpStatus.UNAUTHORIZED };
    }

    try {
      const registrationStats =
        await this.registrationService.getRegistrationStats();
      const whitelistStats = await this.whitelistService.getWhitelistStats();

      return {
        registration: registrationStats,
        whitelist: whitelistStats,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to get stats: ${errorMessage}`, errorStack);
      return {
        error: 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  @Get('whitelist')
  @ApiOperation({ summary: 'Get all whitelisted users' })
  @ApiResponse({
    status: 200,
    description: 'Whitelisted users retrieved successfully',
  })
  @ApiHeader({
    name: 'x-admin-key',
    description: 'Admin API key',
    required: true,
  })
  async getWhitelistUsers(
    @Headers('x-admin-key') adminKey: string,
    @Query('early-adopters-only') earlyAdoptersOnly?: string,
  ) {
    if (!this.validateAdminKey(adminKey)) {
      return { error: 'Unauthorized', statusCode: HttpStatus.UNAUTHORIZED };
    }

    try {
      let users;

      if (earlyAdoptersOnly === 'true') {
        users = await this.whitelistService.getEarlyAdopters();
      } else {
        users = await this.whitelistService.getAllWhitelistUsers();
      }

      return {
        users,
        total: users.length,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to get whitelist users: ${errorMessage}`,
        errorStack,
      );
      return {
        error: 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  @Post('early-adopters/:registrationId')
  @ApiOperation({ summary: 'Select user as early adopter' })
  @ApiResponse({
    status: 200,
    description: 'User selected as early adopter successfully',
  })
  @ApiResponse({ status: 404, description: 'Registration request not found' })
  @ApiHeader({
    name: 'x-admin-key',
    description: 'Admin API key',
    required: true,
  })
  async selectEarlyAdopter(
    @Headers('x-admin-key') adminKey: string,
    @Param('registrationId') registrationId: string,
    @Body() selectDto: { adminUserId: string; notes?: string },
  ) {
    if (!this.validateAdminKey(adminKey)) {
      return { error: 'Unauthorized', statusCode: HttpStatus.UNAUTHORIZED };
    }

    try {
      const result = await this.registrationService.selectAsEarlyAdopter(
        registrationId,
        selectDto.adminUserId,
      );

      if (result.success) {
        this.logger.log(
          `Early adopter selected: ${result.registrationRequest.email} by ${selectDto.adminUserId}`,
        );

        return {
          success: true,
          message: 'User selected as early adopter successfully',
          registrationRequest: result.registrationRequest,
        };
      } else {
        return {
          success: false,
          error: result.message,
          statusCode: HttpStatus.BAD_REQUEST,
        };
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to select early adopter: ${errorMessage}`,
        errorStack,
      );
      return {
        error: 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Get admin dashboard data' })
  @ApiResponse({
    status: 200,
    description: 'Dashboard data retrieved successfully',
  })
  @ApiHeader({
    name: 'x-admin-key',
    description: 'Admin API key',
    required: true,
  })
  async getDashboardData(@Headers('x-admin-key') adminKey: string) {
    if (!this.validateAdminKey(adminKey)) {
      return { error: 'Unauthorized', statusCode: HttpStatus.UNAUTHORIZED };
    }

    try {
      const [
        registrationStats,
        whitelistStats,
        recentRegistrations,
        earlyAdopters,
      ] = await Promise.all([
        this.registrationService.getRegistrationStats(),
        this.whitelistService.getWhitelistStats(),
        this.registrationService.getRegistrationsByStatus(
          RegistrationStatus.EMAIL_CONFIRMED,
        ),
        this.whitelistService.getEarlyAdopters(),
      ]);

      return {
        stats: {
          registration: registrationStats,
          whitelist: whitelistStats,
        },
        recentConfirmedRegistrations: recentRegistrations.slice(0, 10), // Last 10
        earlyAdopters: earlyAdopters.slice(0, 10), // Last 10
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to get dashboard data: ${errorMessage}`,
        errorStack,
      );
      return {
        error: 'Internal server error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      };
    }
  }

  private validateAdminKey(adminKey: string): boolean {
    // In production, this should be a proper admin authentication system
    // For now, we'll use a simple admin key from environment variables
    const validAdminKey = process.env.ADMIN_API_KEY || 'admin-key-change-me';
    return adminKey === validAdminKey;
  }
}

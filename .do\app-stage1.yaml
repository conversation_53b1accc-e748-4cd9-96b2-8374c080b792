name: sanad-paim-stage1
region: nyc

services:
  - name: api-stage1
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
      deploy_on_push: false
    
    # Minimal build configuration
    build_command: ls -la && cp package-stage1.json package.json && cp server-stage1.js server.js && npm install
    run_command: npm start
    
    # Environment configuration
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    
    # Health check configuration
    health_check:
      http_path: /health
      initial_delay_seconds: 30
      period_seconds: 10
      timeout_seconds: 5
      success_threshold: 1
      failure_threshold: 3
    
    # HTTP configuration
    http_port: 3000
    
    # Minimal environment variables
    envs:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "3000"

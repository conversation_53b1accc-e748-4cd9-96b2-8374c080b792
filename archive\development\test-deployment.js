#!/usr/bin/env node

/**
 * Quick deployment test script
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Deployment Configuration...\n');

// Test 1: Check if .env.digitalocean exists
const envFile = '.env.digitalocean';
if (fs.existsSync(envFile)) {
  console.log('✅ .env.digitalocean file found');
  
  // Read and parse environment variables
  const envContent = fs.readFileSync(envFile, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      envVars[key] = valueParts.join('=');
    }
  });
  
  // Test 2: Check critical environment variables
  const criticalVars = [
    'APPWRITE_ENDPOINT',
    'APPWRITE_PROJECT_ID',
    'APPWRITE_API_KEY',
    'APPWRITE_DATABASE_ID',
    'OPENAI_API_KEY',
    'TWILIO_ACCOUNT_SID'
  ];
  
  console.log('\n📋 Checking critical environment variables:');
  let missingVars = [];
  
  criticalVars.forEach(varName => {
    if (envVars[varName] && envVars[varName].trim() !== '') {
      console.log(`✅ ${varName}: ${envVars[varName].substring(0, 20)}...`);
    } else {
      console.log(`❌ ${varName}: MISSING`);
      missingVars.push(varName);
    }
  });
  
  // Test 3: Check Appwrite collections
  console.log('\n🗂️ Checking Appwrite collection IDs:');
  const collections = [
    'APPWRITE_USERS_COLLECTION_ID',
    'APPWRITE_REGISTRATIONS_COLLECTION_ID',
    'APPWRITE_WHITELIST_COLLECTION_ID',
    'APPWRITE_SESSIONS_COLLECTION_ID',
    'APPWRITE_MEMORIES_COLLECTION_ID',
    'APPWRITE_REMINDERS_COLLECTION_ID',
    'APPWRITE_CONVERSATIONS_COLLECTION_ID',
    'APPWRITE_EMAILS_COLLECTION_ID',
    'APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID'
  ];
  
  collections.forEach(collectionVar => {
    if (envVars[collectionVar] && envVars[collectionVar].trim() !== '') {
      console.log(`✅ ${collectionVar}: ${envVars[collectionVar]}`);
    } else {
      console.log(`❌ ${collectionVar}: MISSING`);
      missingVars.push(collectionVar);
    }
  });
  
  // Test 4: Check build files
  console.log('\n🏗️ Checking build status:');
  if (fs.existsSync('dist')) {
    console.log('✅ dist directory exists');
    if (fs.existsSync('dist/main.js')) {
      console.log('✅ main.js built successfully');
    } else {
      console.log('❌ main.js not found in dist');
    }
  } else {
    console.log('❌ dist directory not found - run npm run build');
  }
  
  // Test 5: Check Digital Ocean configuration
  console.log('\n⚙️ Checking Digital Ocean configuration:');
  if (fs.existsSync('.do/app.yaml')) {
    console.log('✅ .do/app.yaml exists');
  } else {
    console.log('❌ .do/app.yaml not found');
  }
  
  // Summary
  console.log('\n📊 Summary:');
  if (missingVars.length === 0) {
    console.log('🎉 All environment variables are configured!');
    console.log('✅ Ready for deployment');
  } else {
    console.log(`⚠️  ${missingVars.length} environment variables need attention:`);
    missingVars.forEach(varName => console.log(`   - ${varName}`));
  }
  
} else {
  console.log('❌ .env.digitalocean file not found');
  console.log('Please create this file with your environment variables');
}

console.log('\n🚀 Deployment test completed!');

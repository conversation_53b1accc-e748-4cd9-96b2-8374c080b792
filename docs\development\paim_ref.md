# PAIM Reference Architecture (Personal AI Manager)

## 🧠 PAIM Phase 1 Skillset

> *Core Skills that make PAIM useful, personal, and extensible from Day 1.*

| Skill ID           | Skill Name                   | Purpose / Value                                                         | Trigger Examples                                           |
| ------------------ | ---------------------------- | ----------------------------------------------------------------------- | ---------------------------------------------------------- |
| `onboarding`       | Identity + tone setting      | Ask for user's preferred name for PAIM, tone, goals                     | “Call yourself Noura”, “be friendly”, “help me with focus” |
| `memory.capture`   | Thought capture              | Save a note, idea, or voice memo with tags and timestamps               | “Remember this”, “Idea: launch checklist”                  |
| `memory.search`    | Find saved thoughts          | Retrieve stored memory by keyword, time, or context                     | “What did I say about content strategy last week?”         |
| `remind.me`        | Personal reminders           | Set conversational reminders (stored in DB or local calendar)           | “Remind me to post on LinkedIn tomorrow at 9am”            |
| `roadmap`          | Show what PAIM is working on | Share upcoming features, skills, and invite feedback                    | “What are you building?”, “roadmap”                        |
| `tone.switch`      | Adjust how PAIM responds     | Let user shift from friendly ↔ professional ↔ witty                     | “Be more direct”, “Talk like a mentor”                     |
| `voice.transcribe` | Voice-to-text transcription  | Converts WhatsApp audio notes to clean, formatted memory entries        | Audio note upload                                          |
| `check.in`         | Daily or weekly reflection   | PAIM checks in with focus prompt or micro-retrospective                 | “Start daily check-in”, scheduled message                  |
| `idea.sort`        | Tag and categorize thoughts  | Automatically tag inputs as idea/task/reflection, and route accordingly | “I just had a thought about…”                              |
| `assistant.intro`  | First-time experience        | If user hasn't spoken to PAIM yet, walk through quick intro onboarding  | "Hey", or first interaction                                |

---

## 🔐 Optional MVP Add-ons (Encrypted / Customizable)

| Skill ID            | Purpose                                       | Note                                                  |
| ------------------- | --------------------------------------------- | ----------------------------------------------------- |
| `memory.export`     | Sync memory to GDrive, Dropbox, etc.          | MVP = local JSON export; Phase 2 = OAuth integrations |
| `doc.ingest`        | Use Docext to process PDFs/images into memory | Based on your chosen doc ingestion approach           |
| `group.role.assign` | Allow setting admin/user in WhatsApp group    | Future-proof for team usage—light rules engine        |

---

## 🧠 Why These?

* They're **all conversational**: no dashboard, no menu, no UI required.
* They're **memory-focused**: the core promise of PAIM is “I remember, so you don’t have to.”
* They're **expansion-friendly**: each one is a stub to more advanced features later (e.g., `remind.me` evolves into full project tracking).

---

## 🧩 Each Skill Should Be...

* Modular (`/skills/skill-name.ts`)
* Registered in a **router** (text-based + optional AI matcher)
* Tone-aware (`toneService.format(response, user.tone)`)
* Logged (`memoryService.logInteraction(userId, { skill, input, output })`)

---



## ✅ 1. **NanoNets/docext**

* **What it does**: On-prem, OCR-free document intelligence converting image/PDF → structured markdown using VLMs ([reddit.com][1], [github.com][2]).
* **Why it’s valuable**:

  * Great for building citation memory: process invoices, PDFs, whiteboard photos into searchable chunks.
  * Secure & private: aligns with your no lock-in, personal instance stance.
* **Verdict**: ✅ Keep it. Use it as an optional PAIM skill (“Document Brain”) to ingest and recall structured content.

---

## ✅ 2. **ruxakK/friday\_jarvis**

* **What it is**: A multi-skill “Jarvis Friday” voice assistant built in Node, with distinct skills you can extend.
* **Why it’s valuable**:

  * Shows actual skill organization and routing logic you want.
  * A perfect example of modular skill dispatch and how new skills are discovered.
* **Verdict**: ✅ Highly relevant. Steal patterns for your skill loader and modular team/group modules.

---

## ✅ 3. **livekit-examples/android-voice-assistant**

* **What it is**: LiveKit’s voice-agent starter kit using their Android SDK—turns device into voice assistant ([github.com][3]).
* **Why it’s valuable**:

  * Demonstrates real-time audio streaming, turn detection, and interruption.
  * Ideal reference for later “Living UI” voice-first interfaces, or even companion mobile apps.
* **Verdict**: ✅ Archive and study voice patterns and real-time logic; perfect foundation for future voice PAIM layers.

---

## 🧠 Integration Strategy

| Repo                  | PAIM Use-Case                          | Next Step                                    |
| --------------------- | -------------------------------------- | -------------------------------------------- |
| **docext**            | Document ingestion → memory ingestion  | Build a `/skills/docext.ts` function         |
| **friday\_jarvis**    | Modular skill dispatch + voice pattern | Adapt its skill loader into PAIM router      |
| **livekit-assistant** | Real-time voice interactions           | Save as library for Living UI / mobile layer |

---

## 🚀 Next Steps

1. **Create `docext` skill** – ingest docs into PAIM’s memory DB (whisper → markdown → vector store).
2. **Refactor skill router** using ideas from `friday_jarvis` (dynamic registration, config-based triggers).
3. **Mock a voice skill** using LiveKit examples as soon as you build voice UI—serve as prototype mode.
4. **Test each in phases**:

   * Phase 1: Document ingestion in WhatsApp text.
   * Phase 2: Voice interaction simulation using LiveKit.
   * Phase 3: Add skill dynamically and re-deploy.

---

## 🧭 TL;DR

* ✅ **docext** → Document memory powerhouse
* ✅ **friday\_jarvis** → Modular skill architecture blueprint
* ✅ **livekit-voice** → Voice UI foundation for Living UI

[1]: https://www.reddit.com/r/LocalLLaMA/comments/1jybxfb/vision_and_voice_enabled_realtime_ai_assistant/?utm_source=chatgpt.com "Vision and voice enabled real-time AI assistant using livekit - Reddit"
[2]: https://github.com/NanoNets/docext?utm_source=chatgpt.com "NanoNets/docext: An on-premises, OCR-free unstructured ... - GitHub"
[3]: https://github.com/livekit-examples/android-voice-assistant?utm_source=chatgpt.com "livekit-examples/android-voice-assistant - GitHub"

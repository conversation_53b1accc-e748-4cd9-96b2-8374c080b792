export const skill = {
  id: "onboarding",
  trigger: ["hello", "start", "setup", "onboard"],
  handler: async (_, context) => {
    const config = await context.getUserConfig();
    if (config.name) return `You're already set up as ${config.name}. Say 'restart onboarding' to redo.`;

    await context.setState("onboarding:step", 1);
    return `Hey, I'm your PAIM. What would you like to call me?`;
  }
};
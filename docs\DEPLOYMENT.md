# 🚀 Sanad Deployment Guide

This guide covers deploying <PERSON><PERSON> (Personal AI Manager) to Digital Ocean App Platform with comprehensive deployment strategies.

## 🎯 Recommended Deployment Strategy

### Primary Deployment: Digital Ocean App Platform (RECOMMENDED)

**Deploy to Digital Ocean App Platform for optimal performance, reliability, and cost-effectiveness.**

#### Why Digital Ocean App Platform?
- ✅ **Optimal Performance**: No cold starts, persistent connections for WhatsApp webhooks
- ✅ **Cost Predictable**: Fixed pricing vs. variable serverless costs
- ✅ **Long-Running Processes**: Perfect for AI processing and real-time conversations
- ✅ **Native Integration**: Seamless Digital Ocean Spaces storage integration
- ✅ **Production Ready**: Built-in monitoring, scaling, and reliability features
- ✅ **Developer Friendly**: Simple deployment with Git integration

#### Current Capabilities
- **Web Registration**: Beautiful registration form with email confirmation
- **WhatsApp Integration**: Full conversational AI with skill system
- **Admin Interface**: User management and early adopter selection
- **Access Control**: Whitelist-based access protection
- **File Storage**: Digital Ocean Spaces integration
- **Production Security**: Rate limiting, validation, and secure tokens

## 📋 Prerequisites

### Required Services

1. **Twilio Account**
   - WhatsApp Business API access
   - Account SID and Auth Token
   - WhatsApp phone number

2. **OpenAI Account**
   - API key with GPT-4 access
   - Sufficient credits/quota

3. **Hosting Platform**
   - Node.js 18+ support
   - HTTPS endpoint for webhooks
   - Environment variable support

### Environment Variables

Create a `.env` file with the following required variables:

```env
# Application
NODE_ENV=production
PORT=3000

# OpenAI
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your-twilio-auth-token-here
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
TWILIO_WEBHOOK_URL=https://your-domain.com/api/v1/webhook/whatsapp

# Security
JWT_SECRET=your-super-secret-jwt-key-min-32-characters-long
ENCRYPTION_KEY=your-super-secret-encryption-key-min-32-characters

# Storage
STORAGE_PATH=./storage

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# CORS
CORS_ORIGIN=*
```

## 🌐 Primary Deployment: Digital Ocean App Platform

### Quick Start Deployment

1. **Install doctl CLI**
   ```bash
   # Windows (using Chocolatey)
   choco install doctl

   # macOS (using Homebrew)
   brew install doctl

   # Linux
   curl -sL https://github.com/digitalocean/doctl/releases/download/v1.94.0/doctl-1.94.0-linux-amd64.tar.gz | tar -xzv
   ```

2. **Authenticate with Digital Ocean**
   ```bash
   doctl auth init
   ```

3. **Deploy the Application**
   ```bash
   # Build and deploy
   npm run deploy:do

   # Or manually
   npm run build
   doctl apps create --spec .do/app.yaml
   ```

4. **Configure Environment Variables**
   - Go to Digital Ocean Dashboard > Apps > Your App > Settings
   - Add all required environment variables from `.env.digitalocean`

5. **Update Twilio Webhook**
   - Set webhook URL: `https://your-app-name.ondigitalocean.app/api/v1/webhook/whatsapp`

### Digital Ocean App Platform Configuration

The app is configured via `.do/app.yaml`:

```yaml
name: sanad-paim
region: nyc

services:
  - name: api
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
      deploy_on_push: true

    build_command: npm run build
    run_command: npm run start:prod
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs

    health_check:
      http_path: /health
      initial_delay_seconds: 30
      period_seconds: 10
```

## 🌐 Alternative Deployment Options

### Heroku (Legacy Option)

1. **Create Heroku App**
   ```bash
   heroku create paim-core
   ```

2. **Set Environment Variables**
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set OPENAI_API_KEY=sk-your-key-here
   heroku config:set TWILIO_ACCOUNT_SID=ACxxxxxxxx
   heroku config:set TWILIO_AUTH_TOKEN=your-token
   heroku config:set TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
   heroku config:set TWILIO_WEBHOOK_URL=https://paim-core.herokuapp.com/api/v1/webhook/whatsapp
   heroku config:set JWT_SECRET=your-jwt-secret
   heroku config:set ENCRYPTION_KEY=your-encryption-key
   ```

3. **Deploy**
   ```bash
   git push heroku main
   ```

4. **Configure Twilio Webhook**
   - Go to Twilio Console > WhatsApp > Senders
   - Set webhook URL: `https://paim-core.herokuapp.com/api/v1/webhook/whatsapp`

### Vercel

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Create vercel.json**
   ```json
   {
     "version": 2,
     "builds": [
       {
         "src": "dist/main.js",
         "use": "@vercel/node"
       }
     ],
     "routes": [
       {
         "src": "/(.*)",
         "dest": "dist/main.js"
       }
     ],
     "env": {
       "NODE_ENV": "production"
     }
   }
   ```

3. **Deploy**
   ```bash
   npm run build
   vercel --prod
   ```

4. **Set Environment Variables**
   ```bash
   vercel env add OPENAI_API_KEY
   vercel env add TWILIO_ACCOUNT_SID
   # ... add all required variables
   ```

### Railway

1. **Connect Repository**
   - Go to Railway.app
   - Connect your GitHub repository

2. **Set Environment Variables**
   - Add all required environment variables in Railway dashboard

3. **Deploy**
   - Railway automatically deploys on git push

### DigitalOcean App Platform

1. **Create App**
   ```yaml
   # .do/app.yaml
   name: paim-core
   services:
   - name: api
     source_dir: /
     github:
       repo: your-username/paim-core
       branch: main
     run_command: npm run start:prod
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: NODE_ENV
       value: production
     - key: OPENAI_API_KEY
       value: ${OPENAI_API_KEY}
     # ... other environment variables
   ```

2. **Deploy**
   ```bash
   doctl apps create --spec .do/app.yaml
   ```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY dist/ ./dist/
COPY logs/ ./logs/
COPY storage/ ./storage/

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S paim -u 1001

# Change ownership
RUN chown -R paim:nodejs /app
USER paim

EXPOSE 3000

CMD ["node", "dist/main.js"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  paim:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_WHATSAPP_NUMBER=${TWILIO_WHATSAPP_NUMBER}
      - TWILIO_WEBHOOK_URL=${TWILIO_WEBHOOK_URL}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - paim
    restart: unless-stopped
```

### Build and Deploy

```bash
# Build the application
npm run build

# Build Docker image
docker build -t paim-core .

# Run with Docker Compose
docker-compose up -d
```

## 🔧 Production Configuration

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    location / {
        proxy_pass http://paim:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### PM2 Configuration

```json
{
  "apps": [{
    "name": "paim-core",
    "script": "dist/main.js",
    "instances": "max",
    "exec_mode": "cluster",
    "env": {
      "NODE_ENV": "production",
      "PORT": 3000
    },
    "log_file": "./logs/pm2.log",
    "out_file": "./logs/pm2-out.log",
    "error_file": "./logs/pm2-error.log",
    "log_date_format": "YYYY-MM-DD HH:mm:ss Z"
  }]
}
```

Start with PM2:
```bash
pm2 start ecosystem.config.json
pm2 save
pm2 startup
```

## 🔒 Security Considerations

### HTTPS/SSL

- **Required**: Twilio webhooks require HTTPS
- **Certificate**: Use Let's Encrypt or commercial SSL
- **Redirect**: Force HTTP to HTTPS redirect

### Environment Security

- **Never commit**: `.env` files to version control
- **Use secrets**: Platform-specific secret management
- **Rotate keys**: Regularly rotate API keys and secrets

### Rate Limiting

- **Application Level**: Built-in rate limiting (10 req/min)
- **Reverse Proxy**: Additional rate limiting at nginx/cloudflare
- **DDoS Protection**: Use services like Cloudflare

### Monitoring

- **Health Checks**: Implement `/health` endpoint
- **Logging**: Centralized logging (ELK stack, Datadog)
- **Alerts**: Set up alerts for errors and downtime

## 📊 Monitoring & Logging

### Application Monitoring

```javascript
// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version
  });
});
```

### Log Management

- **Structured Logging**: JSON format for easy parsing
- **Log Levels**: Use appropriate log levels (error, warn, info, debug)
- **Log Rotation**: Prevent log files from growing too large
- **Centralized Logs**: Send logs to external service (Papertrail, Loggly)

### Performance Monitoring

- **Response Times**: Track API response times
- **Error Rates**: Monitor error frequency
- **Memory Usage**: Track memory consumption
- **Queue Length**: Monitor message processing queue

## 🔄 CI/CD Pipeline

### GitHub Actions

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Heroku
        uses: akhileshns/heroku-deploy@v3.12.12
        with:
          heroku_api_key: ${{secrets.HEROKU_API_KEY}}
          heroku_app_name: "paim-core"
          heroku_email: "<EMAIL>"
```

## 🆘 Troubleshooting

### Common Issues

1. **Webhook Not Receiving Messages**
   - Check HTTPS certificate
   - Verify webhook URL in Twilio console
   - Check firewall/security groups

2. **High Memory Usage**
   - Monitor session cleanup
   - Check for memory leaks
   - Implement proper garbage collection

3. **Slow Response Times**
   - Check OpenAI API latency
   - Monitor database queries
   - Implement caching

### Debug Commands

```bash
# Check application logs
docker logs paim-core

# Monitor resource usage
docker stats paim-core

# Test webhook endpoint
curl -X POST https://your-domain.com/api/v1/webhook/whatsapp \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp%3A%2Btest&To=whatsapp%3A%2Btest&Body=test&MessageSid=test&AccountSid=test&NumMedia=0"
```

## 📞 Support

For deployment issues:
1. Check the logs first
2. Verify all environment variables
3. Test webhook connectivity
4. Contact support with specific error messages

Remember to never share sensitive information like API keys or tokens when seeking support.

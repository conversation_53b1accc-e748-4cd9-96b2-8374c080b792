#!/usr/bin/env node

/**
 * Simple deployment script for PAIM
 * Bypasses CI/CD issues and deploys directly to Digital Ocean
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 Starting PAIM deployment...');

// Check if doctl is available
try {
  execSync('doctl version', { stdio: 'pipe' });
  console.log('✅ Digital Ocean CLI (doctl) is available');
} catch (error) {
  console.log('❌ Digital Ocean CLI (doctl) not found. Please install it first.');
  console.log('   Visit: https://docs.digitalocean.com/reference/doctl/how-to/install/');
  process.exit(1);
}

// Check if app.yaml exists
if (!fs.existsSync('.do/app.yaml')) {
  console.log('❌ Digital Ocean app.yaml not found at .do/app.yaml');
  process.exit(1);
}

console.log('✅ Digital Ocean app.yaml found');

try {
  // Get current apps
  console.log('📋 Checking existing Digital Ocean apps...');
  const apps = execSync('doctl apps list --format ID,Name,Status', { encoding: 'utf8' });
  console.log(apps);

  // Check if PAIM app already exists
  const lines = apps.split('\n');
  let appId = null;
  
  for (const line of lines) {
    if (line.includes('sanad') || line.includes('paim')) {
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 3) {
        appId = parts[0];
        console.log(`✅ Found existing app: ${appId}`);
        break;
      }
    }
  }

  if (appId) {
    // Update existing app
    console.log('🔄 Updating existing app...');
    execSync(`doctl apps update ${appId} --spec .do/app.yaml`, { stdio: 'inherit' });
    console.log('✅ App updated successfully');
    
    // Get app info
    const appInfo = execSync(`doctl apps get ${appId} --format Name,Status,LiveURL`, { encoding: 'utf8' });
    console.log('\n📊 App Status:');
    console.log(appInfo);
    
  } else {
    // Create new app
    console.log('🆕 Creating new app...');
    const result = execSync('doctl apps create --spec .do/app.yaml --format ID,Name,Status', { encoding: 'utf8' });
    console.log('✅ App created successfully');
    console.log(result);
    
    // Extract app ID from result
    const resultLines = result.split('\n');
    if (resultLines.length > 1) {
      const parts = resultLines[1].trim().split(/\s+/);
      if (parts.length >= 1) {
        appId = parts[0];
      }
    }
  }

  if (appId) {
    console.log('\n🔍 Monitoring deployment...');
    console.log('You can monitor the deployment progress at:');
    console.log(`https://cloud.digitalocean.com/apps/${appId}`);
    
    // Wait a bit and check status
    console.log('\n⏳ Waiting for deployment to start...');
    setTimeout(() => {
      try {
        const status = execSync(`doctl apps get ${appId} --format Name,Status,LiveURL`, { encoding: 'utf8' });
        console.log('\n📊 Current Status:');
        console.log(status);
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Monitor deployment in Digital Ocean dashboard');
        console.log('2. Once deployed, test the application endpoints');
        console.log('3. Set up monitoring and alerting');
        console.log('4. Configure custom domain if needed');
        
      } catch (error) {
        console.log('⚠️ Could not get current status, but deployment is in progress');
      }
    }, 5000);
  }

} catch (error) {
  console.error('❌ Deployment failed:', error.message);
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Ensure you are logged in to Digital Ocean: doctl auth init');
  console.log('2. Check your app.yaml configuration');
  console.log('3. Verify your account has sufficient resources');
  process.exit(1);
}

console.log('\n✅ Deployment script completed!');

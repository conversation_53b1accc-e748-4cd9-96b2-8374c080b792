#!/bin/bash

# PAIM Alerting Setup Script
# Configures comprehensive alerting for the PAIM system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MONITORING_DIR="monitoring"
ALERTING_DIR="$MONITORING_DIR/alerting"
COMPOSE_FILE="docker-compose.monitoring.yml"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    success "Prerequisites check passed"
}

# Create alerting directories
create_directories() {
    log "Creating alerting directories..."
    
    mkdir -p "$ALERTING_DIR/templates"
    mkdir -p "$ALERTING_DIR/configs"
    mkdir -p "$ALERTING_DIR/runbooks"
    
    success "Directories created"
}

# Validate alert rules
validate_alert_rules() {
    log "Validating alert rules..."
    
    if [ ! -f "$MONITORING_DIR/alert_rules.yml" ]; then
        error "Alert rules file not found: $MONITORING_DIR/alert_rules.yml"
    fi
    
    # Use promtool to validate rules if available
    if command -v promtool &> /dev/null; then
        if promtool check rules "$MONITORING_DIR/alert_rules.yml"; then
            success "Alert rules validation passed"
        else
            error "Alert rules validation failed"
        fi
    else
        warning "promtool not available, skipping rule validation"
    fi
}

# Validate AlertManager configuration
validate_alertmanager_config() {
    log "Validating AlertManager configuration..."
    
    if [ ! -f "$MONITORING_DIR/alertmanager.yml" ]; then
        error "AlertManager config file not found: $MONITORING_DIR/alertmanager.yml"
    fi
    
    # Use amtool to validate config if available
    if command -v amtool &> /dev/null; then
        if amtool config check "$MONITORING_DIR/alertmanager.yml"; then
            success "AlertManager configuration validation passed"
        else
            error "AlertManager configuration validation failed"
        fi
    else
        warning "amtool not available, skipping config validation"
    fi
}

# Setup notification templates
setup_notification_templates() {
    log "Setting up notification templates..."
    
    # Create Slack notification template
    cat > "$ALERTING_DIR/templates/slack.tmpl" << 'EOF'
{{ define "slack.title" }}
{{ if eq .Status "firing" }}🚨 {{ .GroupLabels.severity | upper }} ALERT{{ else }}✅ RESOLVED{{ end }}
{{ end }}

{{ define "slack.text" }}
{{ range .Alerts }}
**Alert:** {{ .Annotations.summary }}
**Description:** {{ .Annotations.description }}
**Severity:** {{ .Labels.severity }}
**Service:** {{ .Labels.service }}
{{ if .Labels.runbook_url }}**Runbook:** {{ .Labels.runbook_url }}{{ end }}
**Started:** {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}
{{ if ne .Status "firing" }}**Ended:** {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}{{ end }}
{{ end }}
EOF

    # Create email notification template
    cat > "$ALERTING_DIR/templates/email.tmpl" << 'EOF'
{{ define "email.subject" }}
[PAIM] {{ .GroupLabels.severity | upper }} Alert: {{ .GroupLabels.alertname }}
{{ end }}

{{ define "email.html" }}
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; }
        .alert { margin: 20px 0; padding: 15px; border-left: 4px solid; }
        .critical { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        .resolved { border-color: #28a745; background: #d4edda; }
    </style>
</head>
<body>
    <h2>PAIM Alert Notification</h2>
    {{ range .Alerts }}
    <div class="alert {{ if eq .Status "resolved" }}resolved{{ else }}{{ .Labels.severity }}{{ end }}">
        <h3>{{ .Annotations.summary }}</h3>
        <p><strong>Description:</strong> {{ .Annotations.description }}</p>
        <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
        <p><strong>Service:</strong> {{ .Labels.service }}</p>
        {{ if .Labels.runbook_url }}<p><strong>Runbook:</strong> <a href="{{ .Labels.runbook_url }}">{{ .Labels.runbook_url }}</a></p>{{ end }}
        <p><strong>Started:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05 UTC" }}</p>
        {{ if ne .Status "firing" }}<p><strong>Ended:</strong> {{ .EndsAt.Format "2006-01-02 15:04:05 UTC" }}</p>{{ end }}
    </div>
    {{ end }}
</body>
</html>
{{ end }}
EOF

    success "Notification templates created"
}

# Setup webhook endpoints
setup_webhook_endpoints() {
    log "Setting up webhook endpoints..."
    
    # Create a simple webhook receiver script
    cat > "$ALERTING_DIR/webhook-receiver.js" << 'EOF'
const express = require('express');
const app = express();
const port = 5001;

app.use(express.json());

// Basic webhook endpoint
app.post('/webhook', (req, res) => {
    console.log('Received webhook:', JSON.stringify(req.body, null, 2));
    res.status(200).send('OK');
});

// Critical alerts webhook
app.post('/webhook/critical', (req, res) => {
    console.log('CRITICAL ALERT:', JSON.stringify(req.body, null, 2));
    // Add custom logic for critical alerts
    res.status(200).send('OK');
});

// Warning alerts webhook
app.post('/webhook/warning', (req, res) => {
    console.log('WARNING ALERT:', JSON.stringify(req.body, null, 2));
    // Add custom logic for warning alerts
    res.status(200).send('OK');
});

app.listen(port, () => {
    console.log(`Webhook receiver listening on port ${port}`);
});
EOF

    success "Webhook endpoints configured"
}

# Test alerting configuration
test_alerting() {
    log "Testing alerting configuration..."
    
    # Check if AlertManager is running
    if ! curl -s http://localhost:9093/-/healthy > /dev/null; then
        warning "AlertManager is not running. Start monitoring stack first."
        return 1
    fi
    
    # Test alert rule evaluation
    if curl -s http://localhost:9090/api/v1/rules | grep -q "paim_alerts"; then
        success "Alert rules are loaded in Prometheus"
    else
        warning "Alert rules not found in Prometheus"
    fi
    
    # Test AlertManager configuration
    if curl -s http://localhost:9093/api/v1/status | grep -q "success"; then
        success "AlertManager is healthy"
    else
        warning "AlertManager health check failed"
    fi
}

# Create test alert
create_test_alert() {
    log "Creating test alert..."
    
    # Create a test metric that will trigger an alert
    cat > "$ALERTING_DIR/test-alert.sh" << 'EOF'
#!/bin/bash
# Test alert script - creates a metric that triggers an alert

echo "Creating test metric..."
curl -X POST http://localhost:9090/api/v1/admin/tsdb/delete_series?match[]={__name__="test_metric"}
curl -X POST http://localhost:9090/api/v1/admin/tsdb/clean_tombstones

# Push a test metric that will trigger an alert
curl -X POST http://localhost:9091/metrics/job/test/instance/localhost << 'METRICS'
# TYPE test_metric gauge
test_metric{severity="critical"} 1
METRICS

echo "Test metric created. Check AlertManager in a few minutes."
EOF

    chmod +x "$ALERTING_DIR/test-alert.sh"
    success "Test alert script created"
}

# Setup alert silencing
setup_alert_silencing() {
    log "Setting up alert silencing..."
    
    # Create silence management script
    cat > "$ALERTING_DIR/manage-silences.sh" << 'EOF'
#!/bin/bash
# Alert silence management script

ALERTMANAGER_URL="http://localhost:9093"

case "$1" in
    "list")
        curl -s "$ALERTMANAGER_URL/api/v1/silences" | jq '.data[] | {id: .id, comment: .comment, status: .status.state, matchers: .matchers}'
        ;;
    "create")
        if [ -z "$2" ] || [ -z "$3" ]; then
            echo "Usage: $0 create <duration> <comment> [matcher_name=matcher_value]"
            exit 1
        fi
        
        DURATION="$2"
        COMMENT="$3"
        MATCHER="${4:-alertname=.*}"
        
        END_TIME=$(date -d "+$DURATION" -u +"%Y-%m-%dT%H:%M:%S.000Z")
        
        curl -X POST "$ALERTMANAGER_URL/api/v1/silences" \
            -H "Content-Type: application/json" \
            -d "{
                \"matchers\": [{\"name\": \"${MATCHER%=*}\", \"value\": \"${MATCHER#*=}\", \"isRegex\": true}],
                \"startsAt\": \"$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")\",
                \"endsAt\": \"$END_TIME\",
                \"comment\": \"$COMMENT\",
                \"createdBy\": \"$(whoami)\"
            }"
        ;;
    "delete")
        if [ -z "$2" ]; then
            echo "Usage: $0 delete <silence_id>"
            exit 1
        fi
        
        curl -X DELETE "$ALERTMANAGER_URL/api/v1/silence/$2"
        ;;
    *)
        echo "Usage: $0 {list|create|delete}"
        echo "Examples:"
        echo "  $0 list"
        echo "  $0 create 1h 'Maintenance window' alertname=PAIMApplicationDown"
        echo "  $0 delete <silence_id>"
        ;;
esac
EOF

    chmod +x "$ALERTING_DIR/manage-silences.sh"
    success "Alert silencing tools created"
}

# Display access information
display_access_info() {
    log "Alerting setup completed!"
    
    echo ""
    echo "🔗 Alerting URLs:"
    echo "  📊 AlertManager:     http://localhost:9093"
    echo "  📈 Prometheus Rules: http://localhost:9090/rules"
    echo "  🔔 Webhook Receiver: http://localhost:5001"
    echo ""
    echo "📋 Management Commands:"
    echo "  Test Alert:     bash $ALERTING_DIR/test-alert.sh"
    echo "  Manage Silences: bash $ALERTING_DIR/manage-silences.sh"
    echo "  Start Webhook:  node $ALERTING_DIR/webhook-receiver.js"
    echo ""
    echo "📚 Documentation:"
    echo "  Alert Response: docs/ALERT_RESPONSE.md"
    echo "  Notification Channels: $ALERTING_DIR/notification-channels.yml"
    echo ""
    echo "⚠️ Next Steps:"
    echo "  1. Configure notification channels (Slack, email, etc.)"
    echo "  2. Set up on-call rotation"
    echo "  3. Test alert delivery"
    echo "  4. Train team on response procedures"
    echo ""
}

# Main execution
main() {
    log "🚀 Setting up PAIM alerting system..."
    
    check_prerequisites
    create_directories
    validate_alert_rules
    validate_alertmanager_config
    setup_notification_templates
    setup_webhook_endpoints
    setup_alert_silencing
    create_test_alert
    test_alerting
    display_access_info
    
    success "Alerting setup completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "test")
        log "Running alerting tests..."
        test_alerting
        ;;
    "validate")
        log "Validating alerting configuration..."
        validate_alert_rules
        validate_alertmanager_config
        ;;
    "silence")
        shift
        bash "$ALERTING_DIR/manage-silences.sh" "$@"
        ;;
    *)
        main
        ;;
esac

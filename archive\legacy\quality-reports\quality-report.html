
<!DOCTYPE html>
<html>
<head>
    <title>Code Quality Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        .success { color: #28a745; }
        .failure { color: #dc3545; }
        .warning { color: #ffc107; }
        .result { margin: 10px 0; padding: 15px; border-left: 4px solid #ddd; }
        .result.success { border-color: #28a745; background: #f8fff9; }
        .result.failure { border-color: #dc3545; background: #fff8f8; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Code Quality Report</h1>
        <p>Generated: 2025-06-30T09:14:58.497Z</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div>7</div>
        </div>
        <div class="metric">
            <h3 class="success">Passed</h3>
            <div>1</div>
        </div>
        <div class="metric">
            <h3 class="failure">Failed</h3>
            <div>6</div>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <div>14%</div>
        </div>
    </div>
    
    <h2>Detailed Results</h2>
    
        <div class="result failure">
            <h3>❌ TypeScript Compilation</h3>
            <p>Checking TypeScript compilation</p>
            <pre>
> paim-core@0.1.0 prebuild
> npm run clean


> paim-core@0.1.0 clean
> rimraf dist


> paim-core@0.1.0 build
> nest build

Found 7 error(s).

</pre>
            <pre class="failure">Command failed: npm run build
[96msrc/cache/cache.interceptor.ts[0m:[93m29[0m:[93m11[0m - [91merror[0m[90m TS6133: [0m'target' is declared but its value is never read.

[7m29[0m   return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
[7m  [0m [91m          ~~~~~~[0m
[96msrc/cache/cache.interceptor.ts[0m:[93m29[0m:[93m24[0m - [91merror[0m[90m TS6133: [0m'propertyKey' is declared but its value is never read.

[7m29[0m   return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
[7m  [0m [91m                       ~~~~~~~~~~~[0m
[96msrc/cache/cache.service.ts[0m:[93m71[0m:[93m68[0m - [91merror[0m[90m TS2322: [0mType 'string' is not assignable to type 'T'.
  'T' could be instantiated with an arbitrary type which could be unrelated to 'string'.

[7m71[0m           return options.serialize !== false ? JSON.parse(value) : value;
[7m  [0m [91m                                                                   ~~~~~[0m
[96msrc/cache/redis.service.ts[0m:[93m14[0m:[93m47[0m - [91merror[0m[90m TS6138: [0mProperty 'redisOptions' is declared but its value is never read.

[7m14[0m     @Inject('REDIS_OPTIONS') private readonly redisOptions: any,
[7m  [0m [91m                                              ~~~~~~~~~~~~[0m
[96msrc/cache/redis.service.ts[0m:[93m15[0m:[93m22[0m - [91merror[0m[90m TS6138: [0mProperty 'configService' is declared but its value is never read.

[7m15[0m     private readonly configService: ConfigService,
[7m  [0m [91m                     ~~~~~~~~~~~~~[0m
[96msrc/cache/redis.service.ts[0m:[93m71[0m:[93m16[0m - [91merror[0m[90m TS2304: [0mCannot find name 'Redis'.

[7m71[0m   getClient(): Redis {
[7m  [0m [91m               ~~~~~[0m
[96msrc/skills/services/memory-appwrite.service.ts[0m:[93m164[0m:[93m11[0m - [91merror[0m[90m TS6133: [0m'mapDocumentToReminder' is declared but its value is never read.

[7m164[0m   private mapDocumentToReminder(document: any): IReminder {
[7m   [0m [91m          ~~~~~~~~~~~~~~~~~~~~~[0m

</pre>
        </div>
    
        <div class="result failure">
            <h3>❌ ESLint</h3>
            <p>Running ESLint code analysis</p>
            <pre>
> paim-core@0.1.0 lint
> eslint "{src,apps,libs,test}/**/*.ts" --fix

</pre>
            <pre class="failure">Command failed: npm run lint

Oops! Something went wrong! :(

ESLint: 8.57.1

ESLint couldn't find the config "@typescript-eslint/recommended" to extend from. Please check that the name of the config is correct.

The config "@typescript-eslint/recommended" was referenced from the config file in "D:\Projects\sanad\.eslintrc.js".

If you still have problems, please stop by https://eslint.org/chat/help to chat with the team.

</pre>
        </div>
    
        <div class="result failure">
            <h3>❌ Unit Tests</h3>
            <p>Running unit tests</p>
            <pre>
> paim-core@0.1.0 test
> jest

</pre>
            <pre class="failure">Command failed: npm run test
● Multiple configurations found:

    * D:/Projects/sanad/jest.config.js
    * `jest` key in D:/Projects/sanad/package.json

  Implicit config resolution does not allow multiple configuration files.
  Either remove unused config files or select one explicitly with `--config`.

  Configuration Documentation:
  https://jestjs.io/docs/configuration

</pre>
        </div>
    
        <div class="result failure">
            <h3>❌ Test Coverage</h3>
            <p>Generating test coverage report</p>
            <pre>
> paim-core@0.1.0 test:cov
> jest --coverage

</pre>
            <pre class="failure">Command failed: npm run test:cov
● Multiple configurations found:

    * D:/Projects/sanad/jest.config.js
    * `jest` key in D:/Projects/sanad/package.json

  Implicit config resolution does not allow multiple configuration files.
  Either remove unused config files or select one explicitly with `--config`.

  Configuration Documentation:
  https://jestjs.io/docs/configuration

</pre>
        </div>
    
        <div class="result failure">
            <h3>❌ E2E Tests</h3>
            <p>Running end-to-end tests</p>
            <pre>
> paim-core@0.1.0 test:e2e
> jest --config ./test/jest-e2e.json

</pre>
            <pre class="failure">Command failed: npm run test:e2e
● Validation Warning:

  Unknown option "moduleNameMapping" with value {"^@/(.*)$": "<rootDir>/../src/$1"} was found.
  This is probably a typing mistake. Fixing it will remove this message.

  Configuration Documentation:
  https://jestjs.io/docs/configuration

● Validation Warning:

  Unknown option "moduleNameMapping" with value {"^@/(.*)$": "<rootDir>/../src/$1"} was found.
  This is probably a typing mistake. Fixing it will remove this message.

  Configuration Documentation:
  https://jestjs.io/docs/configuration

FAIL test/webhook.e2e-spec.ts
  ● Test suite failed to run

    [96msrc/skills/services/memory-appwrite.service.ts[0m:[93m164[0m:[93m11[0m - [91merror[0m[90m TS6133: [0m'mapDocumentToReminder' is declared but its value is never read.

    [7m164[0m   private mapDocumentToReminder(document: any): IReminder {
    [7m   [0m [91m          ~~~~~~~~~~~~~~~~~~~~~[0m

Test Suites: 1 failed, 1 total
Tests:       0 total
Snapshots:   0 total
Time:        18.161 s
Ran all test suites.
</pre>
        </div>
    
        <div class="result success">
            <h3>✅ Security Audit</h3>
            <p>Running npm security audit</p>
            <pre>found 0 vulnerabilities
</pre>
            
        </div>
    
        <div class="result failure">
            <h3>❌ Dependency Check</h3>
            <p>Checking for outdated dependencies</p>
            <pre>Package                           Current   Wanted  Latest  Location                                       Depended by
@nestjs/cli                        10.4.9   10.4.9  11.0.7  node_modules/@nestjs/cli                       sanad
@nestjs/common                    10.4.19  10.4.19  11.1.3  node_modules/@nestjs/common                    sanad
@nestjs/config                      3.3.0    3.3.0   4.0.2  node_modules/@nestjs/config                    sanad
@nestjs/core                      10.4.19  10.4.19  11.1.3  node_modules/@nestjs/core                      sanad
@nestjs/platform-express          10.4.19  10.4.19  11.1.3  node_modules/@nestjs/platform-express          sanad
@nestjs/schedule                    4.1.2    4.1.2   6.0.0  node_modules/@nestjs/schedule                  sanad
@nestjs/schematics                 10.2.3   10.2.3  11.0.5  node_modules/@nestjs/schematics                sanad
@nestjs/swagger                     7.4.2    7.4.2  11.2.0  node_modules/@nestjs/swagger                   sanad
@nestjs/testing                   10.4.19  10.4.19  11.1.3  node_modules/@nestjs/testing                   sanad
@nestjs/throttler                   5.2.0    5.2.0   6.4.0  node_modules/@nestjs/throttler                 sanad
@types/express                    4.17.23  4.17.23   5.0.3  node_modules/@types/express                    sanad
@types/jest                       29.5.14  29.5.14  30.0.0  node_modules/@types/jest                       sanad
@types/node                       20.19.1  20.19.2  24.0.7  node_modules/@types/node                       sanad
@types/supertest                   2.0.16   2.0.16   6.0.3  node_modules/@types/supertest                  sanad
@types/uuid                         9.0.8    9.0.8  10.0.0  node_modules/@types/uuid                       sanad
@typescript-eslint/eslint-plugin   6.21.0   6.21.0  8.35.0  node_modules/@typescript-eslint/eslint-plugin  sanad
@typescript-eslint/parser          6.21.0   6.21.0  8.35.0  node_modules/@typescript-eslint/parser         sanad
eslint                             8.57.1   8.57.1  9.30.0  node_modules/eslint                            sanad
eslint-config-prettier              9.1.0    9.1.0  10.1.5  node_modules/eslint-config-prettier            sanad
eslint-plugin-prettier              5.5.0    5.5.1   5.5.1  node_modules/eslint-plugin-prettier            sanad
helmet                              7.2.0    7.2.0   8.1.0  node_modules/helmet                            sanad
husky                               8.0.3    8.0.3   9.1.7  node_modules/husky                             sanad
jest                               29.7.0   29.7.0  30.0.3  node_modules/jest                              sanad
lint-staged                        15.5.2   15.5.2  16.1.2  node_modules/lint-staged                       sanad
openai                            4.104.0  4.104.0   5.8.2  node_modules/openai                            sanad
prettier                            3.5.3    3.6.2   3.6.2  node_modules/prettier                          sanad
reflect-metadata                   0.1.14   0.1.14   0.2.2  node_modules/reflect-metadata                  sanad
rimraf                             5.0.10   5.0.10   6.0.1  node_modules/rimraf                            sanad
supertest                           6.3.4    6.3.4   7.1.1  node_modules/supertest                         sanad
twilio                             4.23.0   4.23.0   5.7.1  node_modules/twilio                            sanad
uuid                                9.0.1    9.0.1  11.1.0  node_modules/uuid                              sanad
</pre>
            <pre class="failure">Command failed: npm outdated</pre>
        </div>
    
</body>
</html>
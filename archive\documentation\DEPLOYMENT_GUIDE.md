# Sanad Deployment Guide - Vercel

This guide will help you deploy Sanad to Vercel with all the required configurations.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI**: Install globally with `npm install -g vercel`
3. **Environment Variables**: Have all your production values ready

## Quick Deployment Steps

### 1. Install Vercel CLI (if not already installed)
```bash
npm install -g vercel
```

### 2. Login to Vercel
```bash
vercel login
```

### 3. Build the Application
```bash
npm run build
```

### 4. Deploy to Vercel
```bash
vercel --prod
```

## Environment Variables Setup

You'll need to set these environment variables in Vercel. You can do this either:
- Through the Vercel dashboard (Project Settings > Environment Variables)
- Using the Vercel CLI: `vercel env add VARIABLE_NAME`

### Required Environment Variables

#### Core Application
- `NODE_ENV=production`
- `PORT=3000`

#### OpenAI Configuration
- `OPENAI_API_KEY=sk-your-openai-api-key`
- `OPENAI_MODEL=gpt-4`
- `OPENAI_MAX_TOKENS=1000`
- `OPENAI_TEMPERATURE=0.7`

#### Twilio WhatsApp Configuration
- `TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- `TWILIO_AUTH_TOKEN=your-twilio-auth-token`
- `TWILIO_WHATSAPP_NUMBER=whatsapp:+your-number`
- `TWILIO_WEBHOOK_URL=https://your-vercel-domain.vercel.app/api/v1/webhook/whatsapp`

#### Security
- `JWT_SECRET=your-super-secure-jwt-key-min-64-characters-long`
- `ENCRYPTION_KEY=your-super-secure-encryption-key-min-64-characters`

#### Digital Ocean Spaces Storage
- `STORAGE_PROVIDER=digitalocean`
- `DO_SPACES_ACCESS_KEY_ID=your-do-spaces-access-key`
- `DO_SPACES_SECRET_ACCESS_KEY=your-do-spaces-secret-key`
- `DO_SPACES_ENDPOINT=https://nyc3.digitaloceanspaces.com`
- `DO_SPACES_REGION=nyc3`
- `DO_SPACES_BUCKET=sanad-production-storage`
- `DO_SPACES_CDN_ENDPOINT=https://sanad-production-storage.nyc3.cdn.digitaloceanspaces.com`

#### Email & Registration
- `EMAIL_SERVICE_PROVIDER=sendgrid`
- `SENDGRID_API_KEY=your-sendgrid-api-key`
- `FROM_EMAIL=<EMAIL>`
- `ADMIN_EMAIL=<EMAIL>`
- `REGISTRATION_ENABLED=true`
- `REQUIRE_EMAIL_CONFIRMATION=true`
- `AUTO_APPROVE_WHITELIST=false`
- `ADMIN_API_KEY=your-super-secure-admin-api-key`

#### Rate Limiting & CORS
- `THROTTLE_TTL=60`
- `THROTTLE_LIMIT=5`
- `CORS_ORIGIN=https://your-domain.vercel.app`

#### Logging & Features
- `LOG_LEVEL=warn`
- `DETAILED_ERRORS=false`
- `HEALTH_CHECK_ENABLED=true`
- `FEATURE_VOICE_ENABLED=true`
- `FEATURE_MEDIA_ENABLED=true`
- `FEATURE_REMINDERS_ENABLED=true`
- `FEATURE_ANALYTICS_ENABLED=true`

## Post-Deployment Configuration

### 1. Update Twilio Webhook URL
1. Go to your Twilio Console
2. Navigate to WhatsApp > Senders
3. Update the webhook URL to: `https://your-vercel-domain.vercel.app/api/v1/webhook/whatsapp`

### 2. Test the Deployment

#### Test Registration Form
Visit: `https://your-vercel-domain.vercel.app/register`

#### Test Admin Dashboard
Visit: `https://your-vercel-domain.vercel.app/admin/dashboard`
(Use the `x-admin-key` header with your `ADMIN_API_KEY` value)

#### Test Health Check
Visit: `https://your-vercel-domain.vercel.app/health`

#### Test API Documentation
Visit: `https://your-vercel-domain.vercel.app/api/docs`

### 3. Test WhatsApp Integration
1. Send a message to your Twilio WhatsApp number
2. If not whitelisted, you should receive an access denied message with registration instructions
3. Register through the web form and confirm your email
4. Have an admin select you as an early adopter
5. Try WhatsApp again - you should now have access

## Useful Commands

### Deploy with automatic environment setup
```bash
npm run deploy:vercel
```

### Manual deployment
```bash
vercel --prod
```

### Check deployment status
```bash
vercel ls
```

### View logs
```bash
vercel logs your-deployment-url
```

## Troubleshooting

### Build Fails
- Ensure all dependencies are installed: `npm install`
- Check TypeScript compilation: `npm run typecheck`
- Verify build works locally: `npm run build`

### Environment Variables Not Working
- Check variable names match exactly (case-sensitive)
- Ensure all required variables are set
- Redeploy after adding new variables

### WhatsApp Webhook Not Working
- Verify webhook URL is correct in Twilio console
- Check that the URL is accessible: `curl https://your-domain.vercel.app/health`
- Review Vercel function logs for errors

### Registration Form Not Working
- Check email service configuration (SendGrid API key)
- Verify CORS settings allow your domain
- Test email confirmation flow

## Security Notes

1. **Never commit sensitive environment variables** to your repository
2. **Use strong, unique keys** for JWT_SECRET and ENCRYPTION_KEY (64+ characters)
3. **Regularly rotate API keys** and access tokens
4. **Monitor access logs** for suspicious activity
5. **Keep dependencies updated** for security patches

## Support

If you encounter issues:
1. Check Vercel function logs
2. Review the health check endpoint
3. Verify all environment variables are set correctly
4. Test individual components (registration, WhatsApp, admin panel)

## Next Steps

After successful deployment:
1. Set up monitoring and alerting
2. Configure custom domain (optional)
3. Set up SSL certificate (automatic with Vercel)
4. Plan for scaling and database migration
5. Implement backup strategies for user data

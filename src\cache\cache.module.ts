import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CacheService } from './cache.service';
import { RedisService } from './redis.service';
import { CacheInterceptor } from './cache.interceptor';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    CacheService,
    RedisService,
    CacheInterceptor,
    {
      provide: 'REDIS_OPTIONS',
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST', 'localhost'),
        port: configService.get('REDIS_PORT', 6379),
        password: configService.get('REDIS_PASSWORD'),
        db: configService.get('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        retryDelayOnClusterDown: 300,
        retryDelayOnFailoverAttempts: 3,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        connectTimeout: 10000,
        commandTimeout: 5000,
      }),
      inject: [ConfigService],
    },
  ],
  exports: [CacheService, RedisService, CacheInterceptor],
})
export class CacheModule {}

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../src/app.module';
import { ConfigService } from '@nestjs/config';

describe('Webhook (e2e)', () => {
  let app: INestApplication;

  const validWhatsAppPayload = {
    MessageSid: 'SM**********abcdef**********abcdef',
    AccountSid: 'AC**********abcdef**********abcdef',
    From: 'whatsapp:+**********',
    To: 'whatsapp:+**********',
    Body: 'Hello, this is a test message',
    NumMedia: '0',
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key: string) => {
          const config = {
            NODE_ENV: 'test',
            PORT: 3000,
            TWILIO_ACCOUNT_SID: 'test_account_sid',
            TWILIO_AUTH_TOKEN: 'test_auth_token',
            TWILIO_WHATSAPP_NUMBER: 'whatsapp:+**********',
            OPENAI_API_KEY: 'test_openai_key',
            LOG_LEVEL: 'error',
          };
          return config[key];
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/api/v1/webhook/whatsapp (POST)', () => {
    it('should accept valid WhatsApp webhook', () => {
      return request(app.getHttpServer())
        .post('/api/v1/webhook/whatsapp')
        .send(validWhatsAppPayload)
        .expect(200);
    });

    it('should handle missing required fields', () => {
      const invalidPayload = {
        MessageSid: 'SM**********abcdef**********abcdef',
        // Missing other required fields
      };

      return request(app.getHttpServer())
        .post('/api/v1/webhook/whatsapp')
        .send(invalidPayload)
        .expect(400);
    });

    it('should handle media messages', () => {
      const mediaPayload = {
        ...validWhatsAppPayload,
        NumMedia: '1',
        MediaUrl0:
          'https://api.twilio.com/2010-04-01/Accounts/test/Messages/test/Media/test',
        MediaContentType0: 'image/jpeg',
      };

      return request(app.getHttpServer())
        .post('/api/v1/webhook/whatsapp')
        .send(mediaPayload)
        .expect(200);
    });

    it('should handle voice messages', () => {
      const voicePayload = {
        ...validWhatsAppPayload,
        NumMedia: '1',
        MediaUrl0:
          'https://api.twilio.com/2010-04-01/Accounts/test/Messages/test/Media/test',
        MediaContentType0: 'audio/ogg',
      };

      return request(app.getHttpServer())
        .post('/api/v1/webhook/whatsapp')
        .send(voicePayload)
        .expect(200);
    });

    it('should handle empty messages', () => {
      const emptyPayload = {
        ...validWhatsAppPayload,
        Body: '',
        NumMedia: '0',
      };

      return request(app.getHttpServer())
        .post('/api/v1/webhook/whatsapp')
        .send(emptyPayload)
        .expect(400);
    });

    it('should handle rate limiting', async () => {
      // Send multiple requests rapidly
      const requests = Array(10)
        .fill(null)
        .map(() =>
          request(app.getHttpServer())
            .post('/api/v1/webhook/whatsapp')
            .send(validWhatsAppPayload),
        );

      const responses = await Promise.all(requests);

      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(
        (res) => res.status === 429,
      );
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('/health (GET)', () => {
    it('should return health status', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body).toHaveProperty('timestamp');
          expect(res.body).toHaveProperty('uptime');
          expect(res.body).toHaveProperty('version');
          expect(res.body).toHaveProperty('environment');
        });
    });

    it('should return detailed health status', () => {
      return request(app.getHttpServer())
        .get('/health/detailed')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body).toHaveProperty('dependencies');
        });
    });
  });

  describe('Error handling', () => {
    it('should handle 404 for unknown endpoints', () => {
      return request(app.getHttpServer()).get('/unknown-endpoint').expect(404);
    });

    it('should handle malformed JSON', () => {
      return request(app.getHttpServer())
        .post('/api/v1/webhook/whatsapp')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);
    });
  });

  describe('Security', () => {
    it('should include security headers', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect((res) => {
          expect(res.headers).toHaveProperty('x-frame-options');
          expect(res.headers).toHaveProperty('x-content-type-options');
        });
    });

    it('should reject requests with invalid content type for webhook', () => {
      return request(app.getHttpServer())
        .post('/api/v1/webhook/whatsapp')
        .send(validWhatsAppPayload)
        .set('Content-Type', 'text/plain')
        .expect(400);
    });
  });
});

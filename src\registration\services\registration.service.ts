import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { EmailService } from './email.service';
import { WhitelistService } from './whitelist.service';
import {
  IRegistrationRequest,
  IEmailConfirmation,
  CreateRegistrationRequestDto,
  RegistrationStatus,
} from '../../interfaces/registration.interface';

@Injectable()
export class RegistrationService {
  private readonly logger = new Logger(RegistrationService.name);
  private readonly registrationRequests = new Map<
    string,
    IRegistrationRequest
  >();
  private readonly emailConfirmations = new Map<string, IEmailConfirmation>();
  private readonly emailToRequestMap = new Map<string, string>();

  constructor(
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
    private readonly whitelistService: WhitelistService,
  ) {}

  async createRegistrationRequest(
    dto: CreateRegistrationRequestDto,
  ): Promise<IRegistrationRequest> {
    const id = uuidv4();
    const confirmationToken = uuidv4();
    const now = new Date();

    const registrationRequest: IRegistrationRequest = {
      id,
      email: dto.email.toLowerCase().trim(),
      firstName: dto.firstName.trim(),
      lastName: dto.lastName.trim(),
      phoneNumber: dto.phoneNumber?.trim(),
      reason: dto.reason?.trim(),
      status: RegistrationStatus.PENDING,
      confirmationToken,
      emailConfirmed: false,
      isEarlyAdopter: false,
      createdAt: now,
      updatedAt: now,
    };

    // Store registration request
    this.registrationRequests.set(id, registrationRequest);
    this.emailToRequestMap.set(registrationRequest.email, id);

    // Create email confirmation record
    const emailConfirmation: IEmailConfirmation = {
      id: uuidv4(),
      email: registrationRequest.email,
      token: confirmationToken,
      registrationRequestId: id,
      expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000), // 24 hours
      createdAt: now,
    };

    this.emailConfirmations.set(confirmationToken, emailConfirmation);

    this.logger.log(`Created registration request: ${id} for ${dto.email}`);
    return registrationRequest;
  }

  async findByEmail(email: string): Promise<IRegistrationRequest | null> {
    const requestId = this.emailToRequestMap.get(email.toLowerCase().trim());
    if (!requestId) {
      return null;
    }
    return this.registrationRequests.get(requestId) || null;
  }

  async findById(id: string): Promise<IRegistrationRequest | null> {
    return this.registrationRequests.get(id) || null;
  }

  async sendConfirmationEmail(
    registrationRequest: IRegistrationRequest,
  ): Promise<void> {
    try {
      const confirmationUrl = this.buildConfirmationUrl(
        registrationRequest.confirmationToken,
      );

      await this.emailService.sendConfirmationEmail(
        registrationRequest.email,
        registrationRequest.firstName,
        confirmationUrl,
      );

      this.logger.log(
        `Sent confirmation email to: ${registrationRequest.email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send confirmation email to ${registrationRequest.email}`,
        error instanceof Error ? error.stack : undefined,
        'RegistrationService',
      );
      throw error;
    }
  }

  async confirmEmail(token: string): Promise<{
    success: boolean;
    message?: string;
    registrationRequest?: IRegistrationRequest;
  }> {
    const emailConfirmation = this.emailConfirmations.get(token);

    if (!emailConfirmation) {
      return {
        success: false,
        message: 'Invalid confirmation token',
      };
    }

    if (emailConfirmation.confirmedAt) {
      return {
        success: false,
        message: 'Email has already been confirmed',
      };
    }

    if (new Date() > emailConfirmation.expiresAt) {
      return {
        success: false,
        message: 'Confirmation token has expired',
      };
    }

    const registrationRequest = this.registrationRequests.get(
      emailConfirmation.registrationRequestId,
    );

    if (!registrationRequest) {
      return {
        success: false,
        message: 'Registration request not found',
      };
    }

    // Update email confirmation
    emailConfirmation.confirmedAt = new Date();
    this.emailConfirmations.set(token, emailConfirmation);

    // Update registration request
    registrationRequest.emailConfirmed = true;
    registrationRequest.emailConfirmedAt = new Date();
    registrationRequest.status = RegistrationStatus.EMAIL_CONFIRMED;
    registrationRequest.updatedAt = new Date();
    this.registrationRequests.set(registrationRequest.id, registrationRequest);

    // Add to whitelist
    await this.whitelistService.addToWhitelist(registrationRequest);

    this.logger.log(`Email confirmed for: ${registrationRequest.email}`);

    return {
      success: true,
      registrationRequest,
    };
  }

  async selectAsEarlyAdopter(
    registrationRequestId: string,
    adminUserId: string,
  ): Promise<{
    success: boolean;
    message?: string;
    registrationRequest?: IRegistrationRequest;
  }> {
    const registrationRequest = this.registrationRequests.get(
      registrationRequestId,
    );

    if (!registrationRequest) {
      return {
        success: false,
        message: 'Registration request not found',
      };
    }

    if (!registrationRequest.emailConfirmed) {
      return {
        success: false,
        message: 'Email must be confirmed before selecting as early adopter',
      };
    }

    if (registrationRequest.isEarlyAdopter) {
      return {
        success: false,
        message: 'User is already an early adopter',
      };
    }

    // Update registration request
    registrationRequest.isEarlyAdopter = true;
    registrationRequest.earlyAdopterSelectedAt = new Date();
    registrationRequest.earlyAdopterSelectedBy = adminUserId;
    registrationRequest.status = RegistrationStatus.EARLY_ADOPTER;
    registrationRequest.updatedAt = new Date();
    this.registrationRequests.set(registrationRequestId, registrationRequest);

    // Update whitelist
    await this.whitelistService.markAsEarlyAdopter(
      registrationRequest.email,
      adminUserId,
    );

    // Send early adopter notification email
    await this.emailService.sendEarlyAdopterNotification(
      registrationRequest.email,
      registrationRequest.firstName,
    );

    this.logger.log(
      `Selected early adopter: ${registrationRequest.email} by admin: ${adminUserId}`,
    );

    return {
      success: true,
      registrationRequest,
    };
  }

  async getAllRegistrationRequests(): Promise<IRegistrationRequest[]> {
    return Array.from(this.registrationRequests.values());
  }

  async getRegistrationsByStatus(
    status: RegistrationStatus,
  ): Promise<IRegistrationRequest[]> {
    return Array.from(this.registrationRequests.values()).filter(
      (req) => req.status === status,
    );
  }

  async getRegistrationStats() {
    const allRequests = Array.from(this.registrationRequests.values());
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    return {
      totalRequests: allRequests.length,
      pendingRequests: allRequests.filter(
        (req) => req.status === RegistrationStatus.PENDING,
      ).length,
      confirmedEmails: allRequests.filter((req) => req.emailConfirmed).length,
      earlyAdopters: allRequests.filter((req) => req.isEarlyAdopter).length,
      registrationsToday: allRequests.filter((req) => req.createdAt >= today)
        .length,
      registrationsThisWeek: allRequests.filter(
        (req) => req.createdAt >= weekAgo,
      ).length,
      registrationsThisMonth: allRequests.filter(
        (req) => req.createdAt >= monthAgo,
      ).length,
    };
  }

  private buildConfirmationUrl(token: string): string {
    const baseUrl = this.configService.get<string>(
      'TWILIO_WEBHOOK_URL',
      'https://sanad.kanousai.com',
    );
    // Remove the webhook path and use the base domain
    const domain = baseUrl.replace('/api/v1/webhook/whatsapp', '');
    return `${domain}/register/confirm?token=${token}`;
  }
}

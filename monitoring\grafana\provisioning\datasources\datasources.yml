apiVersion: 1

datasources:
  # Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "5s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}

  # Loki datasource for logs
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: "jaeger"
          matcherRegex: "traceID=(\\w+)"
          name: "TraceID"
          url: "$${__value.raw}"

  # Jaeger datasource for tracing
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    uid: jaeger
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: "loki"
        tags: ["job", "instance", "pod", "namespace"]
        mappedTags: [{"key": "service.name", "value": "service"}]
        mapTagNamesEnabled: false
        spanStartTimeShift: "1h"
        spanEndTimeShift: "1h"
        filterByTraceID: false
        filterBySpanID: false

  # TestData datasource for testing
  - name: TestData
    type: testdata
    access: proxy
    editable: true

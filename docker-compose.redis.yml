version: '3.8'

# Redis Caching Stack for PAIM
# Provides Redis caching with monitoring and management tools

services:
  # Redis primary instance
  redis:
    image: redis:7-alpine
    container_name: paim-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_REPLICATION_MODE=master
    networks:
      - redis-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    labels:
      - "traefik.enable=false"

  # Redis Sentinel for high availability (optional)
  redis-sentinel:
    image: redis:7-alpine
    container_name: paim-redis-sentinel
    restart: unless-stopped
    ports:
      - "26379:26379"
    volumes:
      - ./redis/sentinel.conf:/usr/local/etc/redis/sentinel.conf:ro
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis
    networks:
      - redis-network
    profiles:
      - ha # Only start with high availability profile

  # Redis Commander - Web UI for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: paim-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    depends_on:
      - redis
    networks:
      - redis-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.redis-commander.rule=Host(`redis.localhost`)"
      - "traefik.http.services.redis-commander.loadbalancer.server.port=8081"

  # Redis Exporter for Prometheus monitoring
  redis-exporter:
    image: oliver006/redis_exporter:v1.52.0
    container_name: paim-redis-exporter
    restart: unless-stopped
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_EXPORTER_LOG_FORMAT=txt
    depends_on:
      - redis
    networks:
      - redis-network
    labels:
      - "traefik.enable=false"

  # Redis Insight - Advanced Redis GUI
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: paim-redis-insight
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    depends_on:
      - redis
    networks:
      - redis-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.redis-insight.rule=Host(`redis-insight.localhost`)"
      - "traefik.http.services.redis-insight.loadbalancer.server.port=8001"

  # Redis Backup service
  redis-backup:
    image: redis:7-alpine
    container_name: paim-redis-backup
    restart: "no"
    volumes:
      - redis_data:/data:ro
      - ./backups:/backups
      - ./scripts/redis-backup.sh:/backup.sh:ro
    command: /backup.sh
    depends_on:
      - redis
    networks:
      - redis-network
    profiles:
      - backup # Only start when backup is needed

  # Redis Cluster (for scaling - optional)
  redis-cluster-1:
    image: redis:7-alpine
    container_name: paim-redis-cluster-1
    restart: unless-stopped
    ports:
      - "7001:7001"
      - "17001:17001"
    volumes:
      - redis_cluster_1_data:/data
      - ./redis/cluster.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --port 7001 --cluster-announce-port 7001 --cluster-announce-bus-port 17001
    networks:
      - redis-network
    profiles:
      - cluster

  redis-cluster-2:
    image: redis:7-alpine
    container_name: paim-redis-cluster-2
    restart: unless-stopped
    ports:
      - "7002:7002"
      - "17002:17002"
    volumes:
      - redis_cluster_2_data:/data
      - ./redis/cluster.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --port 7002 --cluster-announce-port 7002 --cluster-announce-bus-port 17002
    networks:
      - redis-network
    profiles:
      - cluster

  redis-cluster-3:
    image: redis:7-alpine
    container_name: paim-redis-cluster-3
    restart: unless-stopped
    ports:
      - "7003:7003"
      - "17003:17003"
    volumes:
      - redis_cluster_3_data:/data
      - ./redis/cluster.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --port 7003 --cluster-announce-port 7003 --cluster-announce-bus-port 17003
    networks:
      - redis-network
    profiles:
      - cluster

volumes:
  redis_data:
    driver: local
  redis_insight_data:
    driver: local
  redis_cluster_1_data:
    driver: local
  redis_cluster_2_data:
    driver: local
  redis_cluster_3_data:
    driver: local

networks:
  redis-network:
    driver: bridge
    name: paim-redis

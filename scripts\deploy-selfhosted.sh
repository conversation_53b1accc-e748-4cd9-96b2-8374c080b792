#!/bin/bash

# =============================================================================
# Complete Self-Hosted Deployment Script for Sanad
# =============================================================================
# This script deploys both Appwrite and the Sanad application

set -e

echo "🚀 Starting complete self-hosted deployment for Sanad..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
APPWRITE_DROPLET_NAME="sanad-appwrite"
APPWRITE_DOMAIN="appwrite.sanad.kanousai.com"
APP_NAME="sanad-paim"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
        error "doctl CLI not found. Please install it first."
    fi
    
    # Check if doctl is authenticated
    if ! doctl account get &> /dev/null; then
        error "doctl not authenticated. Please run: doctl auth init"
    fi
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js not found. Please install Node.js first."
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        error "npm not found. Please install npm first."
    fi
    
    log "✅ Prerequisites check passed"
}

# Deploy Appwrite infrastructure
deploy_appwrite() {
    log "Deploying self-hosted Appwrite..."
    
    # Check if setup script exists
    if [ ! -f "scripts/setup-appwrite-selfhosted.sh" ]; then
        error "Appwrite setup script not found"
    fi
    
    # Make script executable
    chmod +x scripts/setup-appwrite-selfhosted.sh
    
    # Run Appwrite setup
    ./scripts/setup-appwrite-selfhosted.sh
    
    log "✅ Appwrite deployment completed"
}

# Setup Appwrite database schema
setup_appwrite_schema() {
    log "Setting up Appwrite database schema..."
    
    # Check if schema script exists
    if [ ! -f "scripts/setup-appwrite-schema.js" ]; then
        error "Appwrite schema script not found"
    fi
    
    # Get Appwrite droplet IP
    APPWRITE_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$APPWRITE_DROPLET_NAME" | awk '{print $2}')
    
    if [ -z "$APPWRITE_IP" ]; then
        error "Could not find Appwrite droplet IP"
    fi
    
    # Set environment variables for schema setup
    export APPWRITE_ENDPOINT="https://$APPWRITE_DOMAIN/v1"
    
    # Prompt for project details
    echo -e "${CYAN}Please provide your Appwrite project details:${NC}"
    read -p "Project ID: " APPWRITE_PROJECT_ID
    read -p "API Key: " APPWRITE_API_KEY
    
    export APPWRITE_PROJECT_ID
    export APPWRITE_API_KEY
    export APPWRITE_DATABASE_ID="sanad-production"
    export APPWRITE_STORAGE_BUCKET_ID="sanad-files"
    
    # Run schema setup
    node scripts/setup-appwrite-schema.js
    
    log "✅ Appwrite schema setup completed"
}

# Update Digital Ocean app configuration
update_app_config() {
    log "Updating Digital Ocean app configuration..."
    
    # Get Appwrite droplet IP
    APPWRITE_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$APPWRITE_DROPLET_NAME" | awk '{print $2}')
    
    # Update .do/app.yaml with self-hosted Appwrite endpoint
    if [ -f ".do/app.yaml" ]; then
        # Create backup
        cp .do/app.yaml .do/app.yaml.backup
        
        # Update Appwrite endpoint in the YAML file
        sed -i "s|https://cloud.appwrite.io/v1|https://$APPWRITE_DOMAIN/v1|g" .do/app.yaml
        
        log "✅ App configuration updated"
    else
        warn "App configuration file not found"
    fi
}

# Deploy application to Digital Ocean
deploy_application() {
    log "Deploying application to Digital Ocean..."
    
    # Build the application
    log "Building application..."
    npm run build
    
    # Check if app exists
    APP_EXISTS=false
    APP_ID=""
    
    # Get list of apps and check if sanad-paim exists
    APPS=$(doctl apps list --format ID,Spec.Name --no-header)
    while IFS=$'\t' read -r id name; do
        if [ "$name" = "$APP_NAME" ]; then
            APP_EXISTS=true
            APP_ID="$id"
            log "Found existing app with ID: $APP_ID"
            break
        fi
    done <<< "$APPS"
    
    if [ "$APP_EXISTS" = true ]; then
        # Update existing app
        log "Updating existing app..."
        doctl apps update "$APP_ID" --spec .do/app.yaml
        
        if [ $? -eq 0 ]; then
            log "✅ App updated successfully!"
        else
            error "App update failed"
        fi
    else
        # Create new app
        log "Creating new app..."
        doctl apps create --spec .do/app.yaml
        
        if [ $? -eq 0 ]; then
            log "✅ App created successfully!"
        else
            error "App creation failed"
        fi
    fi
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    # Get app URL
    APP_URL=$(doctl apps list --format Spec.Name,DefaultIngress --no-header | grep "$APP_NAME" | awk '{print $2}')
    
    if [ -n "$APP_URL" ]; then
        log "🌐 Application URL: https://$APP_URL"
        
        # Test health endpoint
        log "Testing health endpoint..."
        if curl -f -s "https://$APP_URL/health" > /dev/null; then
            log "✅ Health check passed"
        else
            warn "Health check failed - app may still be starting"
        fi
    else
        warn "Could not determine app URL"
    fi
    
    # Test Appwrite endpoint
    log "Testing Appwrite endpoint..."
    if curl -f -s "https://$APPWRITE_DOMAIN/health" > /dev/null; then
        log "✅ Appwrite health check passed"
    else
        warn "Appwrite health check failed"
    fi
}

# Main execution
main() {
    log "Starting complete self-hosted deployment..."
    
    check_prerequisites
    
    # Show deployment plan
    echo -e "${BLUE}Deployment Plan:${NC}"
    echo "1. Deploy self-hosted Appwrite on Digital Ocean Droplet"
    echo "2. Set up Appwrite database schema and collections"
    echo "3. Update application configuration for self-hosted Appwrite"
    echo "4. Deploy application to Digital Ocean App Platform"
    echo "5. Verify deployment"
    echo ""
    echo -e "${YELLOW}Estimated time: 15-30 minutes${NC}"
    echo -e "${YELLOW}Estimated cost: ~$24/month for Appwrite droplet${NC}"
    echo ""
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "Deployment cancelled by user"
        exit 0
    fi
    
    # Execute deployment steps
    deploy_appwrite
    setup_appwrite_schema
    update_app_config
    deploy_application
    verify_deployment
    
    log "🎉 Complete self-hosted deployment finished!"
    log ""
    log "📋 Deployment Summary:"
    log "✅ Self-hosted Appwrite: https://$APPWRITE_DOMAIN"
    log "✅ Appwrite Console: https://$APPWRITE_DOMAIN/console"
    
    # Get app URL
    APP_URL=$(doctl apps list --format Spec.Name,DefaultIngress --no-header | grep "$APP_NAME" | awk '{print $2}')
    if [ -n "$APP_URL" ]; then
        log "✅ Sanad Application: https://$APP_URL"
    fi
    
    log ""
    log "📝 Important Notes:"
    log "- Your data is now stored on your own infrastructure"
    log "- Appwrite console access is restricted to whitelisted emails"
    log "- Regular backups are recommended for the Appwrite droplet"
    log "- Monitor both services for performance and security"
    log ""
    log "🔗 Useful Commands:"
    log "  doctl apps list                    # List all apps"
    log "  doctl compute droplet list         # List all droplets"
    log "  doctl apps logs <app-id>           # View app logs"
    log "  ssh root@$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$APPWRITE_DROPLET_NAME" | awk '{print $2}')  # SSH to Appwrite server"
}

# Run main function
main "$@"

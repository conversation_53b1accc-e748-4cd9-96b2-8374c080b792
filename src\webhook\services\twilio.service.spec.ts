import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { TwilioService } from './twilio.service';
import { LoggerService, ErrorHandlerService } from '../../core';

// Mock Twilio
const mockTwilioClient = {
  messages: {
    create: jest.fn(),
    get: jest.fn(),
  },
  request: jest.fn(),
  validateRequest: jest.fn(),
};

jest.mock('twilio', () => {
  return jest.fn().mockImplementation(() => mockTwilioClient);
});

describe('TwilioService', () => {
  let service: TwilioService;
  let loggerService: LoggerService;

  const mockTwilioConfig = {
    TWILIO_ACCOUNT_SID: 'test-account-sid',
    TWILIO_AUTH_TOKEN: 'test-auth-token',
    TWILIO_WHATSAPP_NUMBER: 'whatsapp:+**********',
    TWILIO_WEBHOOK_URL: 'https://example.com/webhook',
  };

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TwilioService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => mockTwilioConfig[key]),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
        {
          provide: ErrorHandlerService,
          useValue: {
            handleError: jest.fn().mockReturnValue({ message: 'Handled error' }),
          },
        },
      ],
    }).compile();

    service = module.get<TwilioService>(TwilioService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendMessage', () => {
    it('should send message successfully', async () => {
      const mockMessage = {
        sid: 'test-message-sid',
        status: 'sent',
      };
      mockTwilioClient.messages.create.mockResolvedValue(mockMessage);

      const result = await service.sendMessage({
        to: 'whatsapp:+**********',
        body: 'Test message',
      });

      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        from: mockTwilioConfig.TWILIO_WHATSAPP_NUMBER,
        to: 'whatsapp:+**********',
        body: 'Test message',
      });

      expect(result).toEqual({
        messageSid: 'test-message-sid',
        status: 'sent',
        success: true,
      });

      expect(loggerService.log).toHaveBeenCalledWith(
        'Sending WhatsApp message to whatsapp:+**********',
        'TwilioService'
      );
    });

    it('should send message with media successfully', async () => {
      const mockMessage = {
        sid: 'test-message-sid',
        status: 'sent',
      };
      mockTwilioClient.messages.create.mockResolvedValue(mockMessage);

      const result = await service.sendMessage({
        to: 'whatsapp:+**********',
        body: 'Test message with media',
        mediaUrl: 'https://example.com/image.jpg',
      });

      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        from: mockTwilioConfig.TWILIO_WHATSAPP_NUMBER,
        to: 'whatsapp:+**********',
        body: 'Test message with media',
        mediaUrl: ['https://example.com/image.jpg'],
      });

      expect(result.success).toBe(true);
    });

    it('should handle send message error', async () => {
      const error = new Error('Twilio API error');
      mockTwilioClient.messages.create.mockRejectedValue(error);

      const result = await service.sendMessage({
        to: 'whatsapp:+**********',
        body: 'Test message',
      });

      expect(result).toEqual({
        messageSid: '',
        status: 'failed',
        success: false,
        error: 'Handled error',
      });

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to send WhatsApp message to whatsapp:+**********',
        error.stack,
        'TwilioService'
      );
    });
  });

  describe('validateWebhookSignature', () => {
    it('should validate webhook signature successfully', () => {
      mockTwilioClient.validateRequest.mockReturnValue(true);

      const result = service.validateWebhookSignature(
        'test-signature',
        'https://example.com/webhook',
        { Body: 'test' }
      );

      expect(result).toBe(true);
      expect(mockTwilioClient.validateRequest).toHaveBeenCalledWith(
        mockTwilioConfig.TWILIO_AUTH_TOKEN,
        'test-signature',
        'https://example.com/webhook',
        { Body: 'test' }
      );
    });

    it('should return false for invalid signature', () => {
      mockTwilioClient.validateRequest.mockReturnValue(false);

      const result = service.validateWebhookSignature(
        'invalid-signature',
        'https://example.com/webhook',
        { Body: 'test' }
      );

      expect(result).toBe(false);
    });
  });

  describe('downloadMedia', () => {
    it('should download media successfully', async () => {
      const mockBuffer = Buffer.from('test media content');
      mockTwilioClient.request.mockResolvedValue('test media content');

      const result = await service.downloadMedia('https://api.twilio.com/media/test');

      expect(mockTwilioClient.request).toHaveBeenCalledWith({
        method: 'GET',
        uri: 'https://api.twilio.com/media/test',
      });

      expect(result).toEqual(mockBuffer);
      expect(loggerService.log).toHaveBeenCalledWith('Media downloaded successfully', 'TwilioService');
    });

    it('should handle download media error', async () => {
      const error = new Error('Download failed');
      mockTwilioClient.request.mockRejectedValue(error);

      await expect(service.downloadMedia('https://api.twilio.com/media/test')).rejects.toThrow();

      expect(loggerService.error).toHaveBeenCalledWith(
        'Failed to download media from https://api.twilio.com/media/test',
        error.stack,
        'TwilioService'
      );
    });
  });
});

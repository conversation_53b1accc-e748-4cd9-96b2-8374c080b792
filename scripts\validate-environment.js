#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates all required environment variables and external service connections
 */

const https = require('https');
const http = require('http');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Required environment variables by category
const REQUIRED_VARS = {
  core: [
    'NODE_ENV',
    'PORT',
  ],
  twilio: [
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN',
    'TWILIO_WHATSAPP_NUMBER',
  ],
  openai: [
    'OPENAI_API_KEY',
  ],
  appwrite: [
    'APPWRITE_ENDPOINT',
    'APPWRITE_PROJECT_ID',
    'APPWRITE_API_KEY',
    'APPWRITE_DATABASE_ID',
  ],
};

// Optional environment variables with defaults
const OPTIONAL_VARS = {
  'LOG_LEVEL': 'info',
  'RATE_LIMIT_WINDOW': '900000',
  'RATE_LIMIT_MAX': '100',
  'SESSION_TIMEOUT': '1800000',
  'MAX_MESSAGE_LENGTH': '1600',
  'AI_MODEL': 'gpt-3.5-turbo',
  'AI_MAX_TOKENS': '150',
};

// Validation functions
function validateEnvironmentVariables() {
  log('\n🔍 Validating environment variables...', 'blue');
  
  const results = {
    missing: [],
    invalid: [],
    warnings: [],
  };

  // Check required variables
  Object.entries(REQUIRED_VARS).forEach(([category, vars]) => {
    log(`\n📋 Checking ${category} variables:`, 'cyan');
    
    vars.forEach(varName => {
      const value = process.env[varName];
      
      if (!value) {
        results.missing.push(varName);
        log(`  ❌ ${varName}: Missing`, 'red');
      } else {
        // Validate specific formats
        const validation = validateVariableFormat(varName, value);
        if (validation.valid) {
          log(`  ✅ ${varName}: Set`, 'green');
        } else {
          results.invalid.push({ var: varName, reason: validation.reason });
          log(`  ❌ ${varName}: ${validation.reason}`, 'red');
        }
      }
    });
  });

  // Check optional variables and set defaults
  log('\n📋 Checking optional variables:', 'cyan');
  Object.entries(OPTIONAL_VARS).forEach(([varName, defaultValue]) => {
    const value = process.env[varName];
    
    if (!value) {
      results.warnings.push(`${varName} not set, using default: ${defaultValue}`);
      log(`  ⚠️  ${varName}: Using default (${defaultValue})`, 'yellow');
    } else {
      log(`  ✅ ${varName}: ${value}`, 'green');
    }
  });

  return results;
}

function validateVariableFormat(varName, value) {
  switch (varName) {
    case 'TWILIO_ACCOUNT_SID':
      return {
        valid: value.startsWith('AC') && value.length === 34,
        reason: 'Must start with "AC" and be 34 characters long'
      };
      
    case 'TWILIO_WHATSAPP_NUMBER':
      return {
        valid: value.startsWith('whatsapp:+'),
        reason: 'Must start with "whatsapp:+" followed by phone number'
      };
      
    case 'OPENAI_API_KEY':
      return {
        valid: value.startsWith('sk-') && value.length > 20,
        reason: 'Must start with "sk-" and be at least 20 characters long'
      };
      
    case 'APPWRITE_ENDPOINT':
      return {
        valid: value.startsWith('http') && value.includes('/v1'),
        reason: 'Must be a valid HTTP URL ending with /v1'
      };
      
    case 'PORT':
      const port = parseInt(value);
      return {
        valid: !isNaN(port) && port > 0 && port < 65536,
        reason: 'Must be a valid port number (1-65535)'
      };
      
    default:
      return { valid: true };
  }
}

async function testExternalConnections() {
  log('\n🌐 Testing external service connections...', 'blue');
  
  const tests = [];

  // Test Appwrite connection
  if (process.env.APPWRITE_ENDPOINT) {
    tests.push(testAppwriteConnection());
  }

  // Test OpenAI connection (basic)
  if (process.env.OPENAI_API_KEY) {
    tests.push(testOpenAIConnection());
  }

  const results = await Promise.allSettled(tests);
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      log(`  ✅ ${result.value.service}: ${result.value.message}`, 'green');
    } else {
      log(`  ❌ Connection test failed: ${result.reason}`, 'red');
    }
  });

  return results;
}

function testAppwriteConnection() {
  return new Promise((resolve, reject) => {
    const endpoint = process.env.APPWRITE_ENDPOINT;
    const url = new URL('/health', endpoint);
    
    const request = https.get(url, (response) => {
      if (response.statusCode === 200) {
        resolve({
          service: 'Appwrite',
          message: 'Connection successful'
        });
      } else {
        reject(`Appwrite health check failed: ${response.statusCode}`);
      }
    });

    request.on('error', (error) => {
      reject(`Appwrite connection failed: ${error.message}`);
    });

    request.setTimeout(5000, () => {
      request.destroy();
      reject('Appwrite connection timeout');
    });
  });
}

function testOpenAIConnection() {
  return new Promise((resolve) => {
    // For OpenAI, we'll just validate the API key format
    // A real connection test would require making an API call
    const apiKey = process.env.OPENAI_API_KEY;
    
    if (apiKey && apiKey.startsWith('sk-') && apiKey.length > 20) {
      resolve({
        service: 'OpenAI',
        message: 'API key format valid'
      });
    } else {
      resolve({
        service: 'OpenAI',
        message: 'API key format invalid'
      });
    }
  });
}

function generateEnvironmentReport(validationResults, connectionResults) {
  log('\n📊 Environment Validation Report', 'magenta');
  log('='.repeat(50), 'magenta');

  // Summary
  const totalRequired = Object.values(REQUIRED_VARS).flat().length;
  const missingCount = validationResults.missing.length;
  const invalidCount = validationResults.invalid.length;
  const validCount = totalRequired - missingCount - invalidCount;

  log(`\n📈 Summary:`, 'cyan');
  log(`  Total required variables: ${totalRequired}`, 'blue');
  log(`  Valid: ${validCount}`, 'green');
  log(`  Missing: ${missingCount}`, missingCount > 0 ? 'red' : 'green');
  log(`  Invalid: ${invalidCount}`, invalidCount > 0 ? 'red' : 'green');

  // Missing variables
  if (validationResults.missing.length > 0) {
    log(`\n❌ Missing Variables:`, 'red');
    validationResults.missing.forEach(varName => {
      log(`  - ${varName}`, 'red');
    });
  }

  // Invalid variables
  if (validationResults.invalid.length > 0) {
    log(`\n❌ Invalid Variables:`, 'red');
    validationResults.invalid.forEach(({ var: varName, reason }) => {
      log(`  - ${varName}: ${reason}`, 'red');
    });
  }

  // Warnings
  if (validationResults.warnings.length > 0) {
    log(`\n⚠️  Warnings:`, 'yellow');
    validationResults.warnings.forEach(warning => {
      log(`  - ${warning}`, 'yellow');
    });
  }

  // Connection test results
  log(`\n🌐 Connection Tests:`, 'cyan');
  connectionResults.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      log(`  ✅ ${result.value.service}: ${result.value.message}`, 'green');
    } else {
      log(`  ❌ Connection test failed: ${result.reason}`, 'red');
    }
  });

  // Environment-specific recommendations
  const nodeEnv = process.env.NODE_ENV || 'development';
  log(`\n🎯 Environment: ${nodeEnv}`, 'cyan');
  
  if (nodeEnv === 'production') {
    log('  📋 Production checklist:', 'blue');
    log('    - All secrets properly configured', 'blue');
    log('    - LOG_LEVEL set to "error" or "warn"', 'blue');
    log('    - Rate limiting configured', 'blue');
    log('    - Monitoring enabled', 'blue');
  } else if (nodeEnv === 'staging') {
    log('  📋 Staging checklist:', 'blue');
    log('    - Using staging/test credentials', 'blue');
    log('    - LOG_LEVEL set to "debug" or "info"', 'blue');
    log('    - Test data configured', 'blue');
  }

  return {
    valid: missingCount === 0 && invalidCount === 0,
    summary: {
      total: totalRequired,
      valid: validCount,
      missing: missingCount,
      invalid: invalidCount,
    }
  };
}

async function main() {
  log('🚀 Starting environment validation...', 'magenta');
  
  try {
    // Validate environment variables
    const validationResults = validateEnvironmentVariables();
    
    // Test external connections
    const connectionResults = await testExternalConnections();
    
    // Generate report
    const report = generateEnvironmentReport(validationResults, connectionResults);
    
    // Exit with appropriate code
    if (report.valid) {
      log('\n✅ Environment validation passed!', 'green');
      process.exit(0);
    } else {
      log('\n❌ Environment validation failed!', 'red');
      log('Please fix the issues above before proceeding.', 'red');
      process.exit(1);
    }
    
  } catch (error) {
    log(`\n💥 Validation failed with error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`\n💥 Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    log(`\n💥 Script failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = {
  validateEnvironmentVariables,
  testExternalConnections,
  generateEnvironmentReport,
};

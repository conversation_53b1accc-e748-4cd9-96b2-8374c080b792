#!/usr/bin/env node

/**
 * Appwrite Restore Script for PAIM
 * Restores Appwrite databases, collections, and documents from backup
 */

const fs = require('fs');
const path = require('path');
const { Client, Databases, Users, Storage, ID } = require('node-appwrite');

// Configuration
const CONFIG = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.APPWRITE_PROJECT_ID,
  apiKey: process.env.APPWRITE_API_KEY,
  dryRun: process.env.DRY_RUN === 'true',
  skipExisting: process.env.SKIP_EXISTING === 'true',
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Initialize Appwrite client
function initializeClient() {
  if (!CONFIG.projectId || !CONFIG.apiKey) {
    throw new Error('Missing required Appwrite configuration. Please set APPWRITE_PROJECT_ID and APPWRITE_API_KEY environment variables.');
  }

  const client = new Client()
    .setEndpoint(CONFIG.endpoint)
    .setProject(CONFIG.projectId)
    .setKey(CONFIG.apiKey);

  return {
    databases: new Databases(client),
    users: new Users(client),
    storage: new Storage(client),
  };
}

// Load backup data
function loadBackup(backupPath) {
  log(`📁 Loading backup from: ${backupPath}`, 'blue');
  
  // Check if backup is compressed
  if (backupPath.endsWith('.zip')) {
    log('🗜️ Extracting compressed backup...', 'blue');
    // Extract backup (requires unzip utility or archiver)
    const extractPath = backupPath.replace('.zip', '');
    // Implementation would depend on available tools
    throw new Error('Compressed backup restoration not implemented. Please extract manually.');
  }
  
  // Load manifest
  const manifestPath = path.join(backupPath, 'manifest.json');
  if (!fs.existsSync(manifestPath)) {
    throw new Error('Backup manifest not found');
  }
  
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  log(`📊 Backup version: ${manifest.version}`, 'cyan');
  log(`📅 Backup timestamp: ${manifest.timestamp}`, 'cyan');
  
  // Load backup data
  const backup = {
    manifest,
    databases: null,
    users: null,
    storage: null,
  };
  
  // Load databases backup
  const databasesPath = path.join(backupPath, 'databases', 'databases.json');
  if (fs.existsSync(databasesPath)) {
    backup.databases = JSON.parse(fs.readFileSync(databasesPath, 'utf8'));
    log(`📊 Loaded databases backup: ${backup.databases.databases.length} databases`, 'green');
  }
  
  // Load users backup
  const usersPath = path.join(backupPath, 'users', 'users.json');
  if (fs.existsSync(usersPath)) {
    backup.users = JSON.parse(fs.readFileSync(usersPath, 'utf8'));
    log(`👥 Loaded users backup: ${backup.users.total} users`, 'green');
  }
  
  // Load storage backup
  const storagePath = path.join(backupPath, 'storage', 'storage.json');
  if (fs.existsSync(storagePath)) {
    backup.storage = JSON.parse(fs.readFileSync(storagePath, 'utf8'));
    log(`📁 Loaded storage backup: ${backup.storage.buckets.length} buckets`, 'green');
  }
  
  return backup;
}

// Restore databases
async function restoreDatabases(databases, backupData) {
  if (!backupData.databases) {
    log('⚠️ No database backup data found', 'yellow');
    return;
  }
  
  log('📊 Restoring databases...', 'blue');
  
  for (const databaseBackup of backupData.databases.databases) {
    log(`  📁 Restoring database: ${databaseBackup.name} (${databaseBackup.id})`, 'cyan');
    
    if (CONFIG.dryRun) {
      log(`    [DRY RUN] Would restore database: ${databaseBackup.name}`, 'yellow');
      continue;
    }
    
    try {
      // Check if database exists
      let databaseExists = false;
      try {
        await databases.get(databaseBackup.id);
        databaseExists = true;
      } catch (error) {
        // Database doesn't exist
      }
      
      if (databaseExists && CONFIG.skipExisting) {
        log(`    ⏭️ Database exists, skipping: ${databaseBackup.name}`, 'yellow');
        continue;
      }
      
      // Create database if it doesn't exist
      if (!databaseExists) {
        await databases.create(databaseBackup.id, databaseBackup.name);
        log(`    ✅ Created database: ${databaseBackup.name}`, 'green');
      }
      
      // Restore collections
      for (const collectionBackup of databaseBackup.collections) {
        log(`    📋 Restoring collection: ${collectionBackup.name} (${collectionBackup.id})`, 'cyan');
        
        try {
          // Check if collection exists
          let collectionExists = false;
          try {
            await databases.getCollection(databaseBackup.id, collectionBackup.id);
            collectionExists = true;
          } catch (error) {
            // Collection doesn't exist
          }
          
          if (collectionExists && CONFIG.skipExisting) {
            log(`      ⏭️ Collection exists, skipping: ${collectionBackup.name}`, 'yellow');
            continue;
          }
          
          // Create collection if it doesn't exist
          if (!collectionExists) {
            await databases.createCollection(
              databaseBackup.id,
              collectionBackup.id,
              collectionBackup.name
            );
            
            // Create attributes
            for (const attribute of collectionBackup.attributes) {
              try {
                switch (attribute.type) {
                  case 'string':
                    await databases.createStringAttribute(
                      databaseBackup.id,
                      collectionBackup.id,
                      attribute.key,
                      attribute.size,
                      attribute.required,
                      attribute.default,
                      attribute.array
                    );
                    break;
                  case 'integer':
                    await databases.createIntegerAttribute(
                      databaseBackup.id,
                      collectionBackup.id,
                      attribute.key,
                      attribute.required,
                      attribute.min,
                      attribute.max,
                      attribute.default,
                      attribute.array
                    );
                    break;
                  case 'boolean':
                    await databases.createBooleanAttribute(
                      databaseBackup.id,
                      collectionBackup.id,
                      attribute.key,
                      attribute.required,
                      attribute.default,
                      attribute.array
                    );
                    break;
                  case 'datetime':
                    await databases.createDatetimeAttribute(
                      databaseBackup.id,
                      collectionBackup.id,
                      attribute.key,
                      attribute.required,
                      attribute.default,
                      attribute.array
                    );
                    break;
                  // Add more attribute types as needed
                }
              } catch (error) {
                log(`      ⚠️ Failed to create attribute ${attribute.key}: ${error.message}`, 'yellow');
              }
            }
            
            // Create indexes
            for (const index of collectionBackup.indexes) {
              try {
                await databases.createIndex(
                  databaseBackup.id,
                  collectionBackup.id,
                  index.key,
                  index.type,
                  index.attributes,
                  index.orders
                );
              } catch (error) {
                log(`      ⚠️ Failed to create index ${index.key}: ${error.message}`, 'yellow');
              }
            }
            
            log(`      ✅ Created collection: ${collectionBackup.name}`, 'green');
          }
          
          // Restore documents
          let restoredCount = 0;
          for (const document of collectionBackup.documents) {
            try {
              await databases.createDocument(
                databaseBackup.id,
                collectionBackup.id,
                document.$id,
                document
              );
              restoredCount++;
            } catch (error) {
              if (error.message.includes('already exists') && CONFIG.skipExisting) {
                // Document exists, skip
              } else {
                log(`      ⚠️ Failed to restore document ${document.$id}: ${error.message}`, 'yellow');
              }
            }
          }
          
          log(`      ✅ Restored ${restoredCount} documents`, 'green');
        } catch (error) {
          log(`      ❌ Failed to restore collection: ${error.message}`, 'red');
        }
      }
    } catch (error) {
      log(`    ❌ Failed to restore database: ${error.message}`, 'red');
    }
  }
  
  log('✅ Database restoration completed', 'green');
}

// Restore users
async function restoreUsers(users, backupData) {
  if (!backupData.users) {
    log('⚠️ No users backup data found', 'yellow');
    return;
  }
  
  log('👥 Restoring users...', 'blue');
  
  if (CONFIG.dryRun) {
    log(`[DRY RUN] Would restore ${backupData.users.total} users`, 'yellow');
    return;
  }
  
  let restoredCount = 0;
  for (const user of backupData.users.users) {
    try {
      // Check if user exists
      let userExists = false;
      try {
        await users.get(user.$id);
        userExists = true;
      } catch (error) {
        // User doesn't exist
      }
      
      if (userExists && CONFIG.skipExisting) {
        log(`  ⏭️ User exists, skipping: ${user.email}`, 'yellow');
        continue;
      }
      
      if (!userExists) {
        await users.create(
          user.$id,
          user.email,
          user.phone,
          user.password, // This will be hashed
          user.name
        );
        restoredCount++;
        log(`  ✅ Restored user: ${user.email}`, 'green');
      }
    } catch (error) {
      log(`  ⚠️ Failed to restore user ${user.email}: ${error.message}`, 'yellow');
    }
  }
  
  log(`✅ Users restoration completed: ${restoredCount} users restored`, 'green');
}

// Restore storage buckets (metadata only)
async function restoreStorage(storage, backupData) {
  if (!backupData.storage) {
    log('⚠️ No storage backup data found', 'yellow');
    return;
  }
  
  log('📁 Restoring storage buckets...', 'blue');
  
  if (CONFIG.dryRun) {
    log(`[DRY RUN] Would restore ${backupData.storage.buckets.length} buckets`, 'yellow');
    return;
  }
  
  for (const bucketBackup of backupData.storage.buckets) {
    try {
      // Check if bucket exists
      let bucketExists = false;
      try {
        await storage.getBucket(bucketBackup.id);
        bucketExists = true;
      } catch (error) {
        // Bucket doesn't exist
      }
      
      if (bucketExists && CONFIG.skipExisting) {
        log(`  ⏭️ Bucket exists, skipping: ${bucketBackup.name}`, 'yellow');
        continue;
      }
      
      if (!bucketExists) {
        await storage.createBucket(
          bucketBackup.id,
          bucketBackup.name,
          bucketBackup.permissions,
          bucketBackup.fileSecurity,
          bucketBackup.enabled,
          bucketBackup.maximumFileSize,
          bucketBackup.allowedFileExtensions,
          bucketBackup.compression,
          bucketBackup.encryption,
          bucketBackup.antivirus
        );
        log(`  ✅ Restored bucket: ${bucketBackup.name}`, 'green');
      }
    } catch (error) {
      log(`  ⚠️ Failed to restore bucket ${bucketBackup.name}: ${error.message}`, 'yellow');
    }
  }
  
  log('✅ Storage restoration completed (metadata only)', 'green');
  log('⚠️ Note: File contents are not restored. Files must be restored separately.', 'yellow');
}

// Main restore function
async function performRestore(backupPath) {
  log('🔄 Starting Appwrite restore...', 'magenta');
  
  if (CONFIG.dryRun) {
    log('🧪 DRY RUN MODE - No changes will be made', 'yellow');
  }
  
  try {
    // Load backup data
    const backup = loadBackup(backupPath);
    
    // Initialize Appwrite client
    const { databases, users, storage } = initializeClient();
    
    // Perform restoration
    await restoreDatabases(databases, backup);
    await restoreUsers(users, backup);
    await restoreStorage(storage, backup);
    
    log('\n✅ Restore completed successfully!', 'green');
    
    if (CONFIG.dryRun) {
      log('🧪 This was a dry run. Run without DRY_RUN=true to perform actual restore.', 'yellow');
    }
  } catch (error) {
    log(`\n❌ Restore failed: ${error.message}`, 'red');
    throw error;
  }
}

// Handle script arguments
async function main() {
  const backupPath = process.argv[2];
  
  if (!backupPath) {
    log('Usage: node restore-appwrite.js <backup_path>', 'red');
    log('Environment variables:', 'blue');
    log('  DRY_RUN=true          - Perform dry run without making changes', 'blue');
    log('  SKIP_EXISTING=true    - Skip existing items instead of overwriting', 'blue');
    process.exit(1);
  }
  
  if (!fs.existsSync(backupPath)) {
    log(`❌ Backup path not found: ${backupPath}`, 'red');
    process.exit(1);
  }
  
  await performRestore(backupPath);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`\n💥 Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    log(`\n💥 Script failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

import { Injectable } from '@nestjs/common';

@Injectable()
export class HealthService {
  getHealth() {
    return {
      status: 'OK',
      message: 'Sanad PAIM Stage 2 - NestJS Core Running',
      timestamp: new Date().toISOString(),
      version: '0.1.0-stage2',
      stage: 'Stage 2: NestJS Core Dependencies',
      framework: {
        name: 'NestJS',
        status: 'Operational',
        modules: ['AppModule', 'HealthModule']
      },
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      checks: {
        nestjs: 'OK',
        express: 'OK',
        typescript: 'OK',
        dependencies: 'OK'
      }
    };
  }
}

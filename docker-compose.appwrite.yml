version: '3.8'

# Self-Hosted Appwrite Stack for Sanad
# Complete Appwrite backend infrastructure with all required services

services:
  # Appwrite main service
  appwrite:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite
    restart: unless-stopped
    networks:
      - appwrite
    labels:
      - "traefik.enable=true"
      - "traefik.constraint-label-stack=appwrite"
      - "traefik.http.routers.appwrite_api.rule=Host(`appwrite.${_APP_DOMAIN_TARGET}`)"
      - "traefik.http.routers.appwrite_api.service=appwrite_api"
      - "traefik.http.routers.appwrite_api.tls=true"
      - "traefik.http.routers.appwrite_api.tls.certresolver=dns"
      - "traefik.http.services.appwrite_api.loadbalancer.server.port=80"
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - mariadb
      - redis
      - clamav
      - influxdb
    environment:
      - _APP_ENV=production
      - _APP_WORKER_PER_CORE=6
      - _APP_LOCALE=en
      - _APP_CONSOLE_WHITELIST_ROOT=enabled
      - _APP_CONSOLE_WHITELIST_EMAILS
      - _APP_CONSOLE_WHITELIST_IPS
      - _APP_SYSTEM_EMAIL_NAME=Sanad
      - _APP_SYSTEM_EMAIL_ADDRESS=${_APP_SYSTEM_EMAIL_ADDRESS}
      - _APP_SYSTEM_SECURITY_EMAIL_ADDRESS=${_APP_SYSTEM_SECURITY_EMAIL_ADDRESS}
      - _APP_SYSTEM_RESPONSE_FORMAT
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_ROUTER_PROTECTION=disabled
      - _APP_OPTIONS_FORCE_HTTPS=enabled
      - _APP_OPTIONS_FUNCTIONS_FORCE_HTTPS=enabled
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_DOMAIN=${_APP_DOMAIN}
      - _APP_DOMAIN_TARGET=${_APP_DOMAIN_TARGET}
      - _APP_DOMAIN_FUNCTIONS=${_APP_DOMAIN_FUNCTIONS}
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER
      - _APP_REDIS_PASS
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_INFLUXDB_HOST=influxdb
      - _APP_INFLUXDB_PORT=8086
      - _APP_STORAGE_LIMIT=30000000
      - _APP_STORAGE_PREVIEW_LIMIT=20000000
      - _APP_STORAGE_ANTIVIRUS=enabled
      - _APP_STORAGE_ANTIVIRUS_HOST=clamav
      - _APP_STORAGE_ANTIVIRUS_PORT=3310
      - _APP_STORAGE_DEVICE=local
      - _APP_STORAGE_S3_ACCESS_KEY
      - _APP_STORAGE_S3_SECRET
      - _APP_STORAGE_S3_REGION
      - _APP_STORAGE_S3_BUCKET
      - _APP_STORAGE_DO_SPACES_ACCESS_KEY
      - _APP_STORAGE_DO_SPACES_SECRET
      - _APP_STORAGE_DO_SPACES_REGION
      - _APP_STORAGE_DO_SPACES_BUCKET
      - _APP_FUNCTIONS_SIZE_LIMIT=30000000
      - _APP_FUNCTIONS_TIMEOUT=900
      - _APP_FUNCTIONS_BUILD_TIMEOUT=900
      - _APP_FUNCTIONS_CONTAINERS=10
      - _APP_FUNCTIONS_CPUS=0
      - _APP_FUNCTIONS_MEMORY=0
      - _APP_FUNCTIONS_MEMORY_SWAP=0
      - _APP_FUNCTIONS_RUNTIMES=node-16.0,php-8.0,python-3.9,ruby-3.0
      - _APP_EXECUTOR_SECRET=${_APP_EXECUTOR_SECRET}
      - _APP_EXECUTOR_HOST=http://appwrite-executor/v1
      - _APP_LOGGING_PROVIDER
      - _APP_LOGGING_CONFIG
      - _APP_STATSD_HOST=telegraf
      - _APP_STATSD_PORT=8125
      - _APP_MAINTENANCE_INTERVAL=86400
      - _APP_MAINTENANCE_RETENTION_EXECUTION=1209600
      - _APP_MAINTENANCE_RETENTION_CACHE=2592000
      - _APP_MAINTENANCE_RETENTION_ABUSE=86400
      - _APP_MAINTENANCE_RETENTION_AUDIT=1209600
      - _APP_MAINTENANCE_RETENTION_USAGE_HOURLY=8640000
      - _APP_SMS_PROVIDER
      - _APP_SMS_FROM
      - _APP_SMTP_HOST
      - _APP_SMTP_PORT
      - _APP_SMTP_SECURE
      - _APP_SMTP_USERNAME
      - _APP_SMTP_PASSWORD

  # Appwrite worker for background tasks
  appwrite-realtime:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite-realtime
    restart: unless-stopped
    networks:
      - appwrite
    labels:
      - "traefik.enable=true"
      - "traefik.constraint-label-stack=appwrite"
      - "traefik.http.routers.appwrite_realtime.rule=Host(`appwrite.${_APP_DOMAIN_TARGET}`) && PathPrefix(`/v1/realtime`)"
      - "traefik.http.routers.appwrite_realtime.service=appwrite_realtime"
      - "traefik.http.routers.appwrite_realtime.tls=true"
      - "traefik.http.routers.appwrite_realtime.tls.certresolver=dns"
      - "traefik.http.services.appwrite_realtime.loadbalancer.server.port=80"
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - mariadb
      - redis
    environment:
      - _APP_ENV=production
      - _APP_WORKER_PER_CORE=6
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_ROUTER_PROTECTION=disabled
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER
      - _APP_REDIS_PASS
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_USAGE_STATS=enabled
      - _APP_LOGGING_PROVIDER
      - _APP_LOGGING_CONFIG
    command: realtime

  # Appwrite worker for background tasks
  appwrite-worker-audits:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite-worker-audits
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - redis
      - mariadb
    environment:
      - _APP_ENV=production
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER
      - _APP_REDIS_PASS
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_LOGGING_PROVIDER
      - _APP_LOGGING_CONFIG
    command: worker-audits

  # Appwrite worker for webhooks
  appwrite-worker-webhooks:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite-worker-webhooks
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - redis
      - mariadb
    environment:
      - _APP_ENV=production
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER
      - _APP_REDIS_PASS
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_LOGGING_PROVIDER
      - _APP_LOGGING_CONFIG
    command: worker-webhooks

  # Appwrite worker for deletes
  appwrite-worker-deletes:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite-worker-deletes
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - redis
      - mariadb
    environment:
      - _APP_ENV=production
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_REDIS_USER
      - _APP_REDIS_PASS
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_LOGGING_PROVIDER
      - _APP_LOGGING_CONFIG
    command: worker-deletes

  # MariaDB database
  mariadb:
    image: mariadb:10.7
    container_name: sanad-appwrite-mariadb
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-mariadb:/var/lib/mysql:rw
    environment:
      - MYSQL_ROOT_PASSWORD=${_APP_DB_ROOT_PASS}
      - MYSQL_DATABASE=appwrite
      - MYSQL_USER=${_APP_DB_USER}
      - MYSQL_PASSWORD=${_APP_DB_PASS}
    command: 'mysqld --innodb-flush-method=fsync'

  # Redis cache
  redis:
    image: redis:7.0-alpine
    container_name: sanad-appwrite-redis
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-redis:/data:rw
    command: redis-server --save 60 1 --loglevel warning

  # ClamAV antivirus
  clamav:
    image: appwrite/clamav:1.2.0
    container_name: sanad-appwrite-clamav
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-uploads:/storage/uploads

  # InfluxDB for usage stats
  influxdb:
    image: appwrite/influxdb:1.5.0
    container_name: sanad-appwrite-influxdb
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-influxdb:/var/lib/influxdb:rw

  # Telegraf for metrics collection
  telegraf:
    image: appwrite/telegraf:1.4.0
    container_name: sanad-appwrite-telegraf
    restart: unless-stopped
    networks:
      - appwrite

networks:
  appwrite:
    name: appwrite

volumes:
  appwrite-mariadb:
  appwrite-redis:
  appwrite-cache:
  appwrite-uploads:
  appwrite-certificates:
  appwrite-functions:
  appwrite-builds:
  appwrite-influxdb:
  appwrite-config:

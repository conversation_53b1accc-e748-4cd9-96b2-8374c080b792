# Environment Management & Secrets Guide

**Project:** <PERSON><PERSON> (Personal AI Manager) - Sanad  
**Date:** 2025-06-30  
**Purpose:** Comprehensive guide for environment configuration and secrets management

---

## 🔐 GitHub Secrets Configuration

### Required Repository Secrets

**Digital Ocean Deployment:**
```
DIGITALOCEAN_ACCESS_TOKEN    # DO API token for doctl CLI
```

**Container Registry:**
```
GITHUB_TOKEN                 # Automatically provided by GitHub
```

**Application Secrets (for CI/CD):**
```
OPENAI_API_KEY              # OpenAI API key for testing
TWILIO_ACCOUNT_SID          # Twilio account SID
TWILIO_AUTH_TOKEN           # Twilio authentication token
APPWRITE_API_KEY            # Appwrite API key
JWT_SECRET                  # JWT signing secret
ENCRYPTION_KEY              # Data encryption key
```

**Optional Secrets:**
```
CODECOV_TOKEN               # Code coverage reporting
SENDGRID_API_KEY           # Email service (if using SendGrid)
```

### Setting Up GitHub Secrets

1. **Navigate to Repository Settings**
   ```
   GitHub Repository → Settings → Secrets and variables → Actions
   ```

2. **Add New Repository Secret**
   - Click "New repository secret"
   - Enter secret name and value
   - Click "Add secret"

3. **Environment-Specific Secrets**
   - Create environments: `staging`, `production`, `production-approval`
   - Add environment-specific secrets as needed

---

## 🌍 Environment Configuration

### Environment Files Structure

```
.env.example              # Template with all variables
.env.development         # Development environment (git-ignored)
.env.staging            # Staging environment (git-ignored)
.env.production         # Production environment (git-ignored)
```

### Environment Variables by Category

**Core Application:**
```bash
NODE_ENV=production
PORT=3000
LOG_LEVEL=info
CORS_ORIGIN=https://sanad.kanousai.com
```

**Security:**
```bash
JWT_SECRET=your-super-secret-jwt-key-min-32-characters-long
ENCRYPTION_KEY=your-super-secret-encryption-key-min-32-characters
```

**External Services:**
```bash
OPENAI_API_KEY=sk-your-openai-api-key-here
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your-twilio-auth-token-here
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=your-project-id
```

**Digital Ocean Spaces:**
```bash
STORAGE_PROVIDER=digitalocean
DO_SPACES_ACCESS_KEY_ID=your-do-spaces-access-key
DO_SPACES_SECRET_ACCESS_KEY=your-do-spaces-secret-key
DO_SPACES_BUCKET=sanad-storage
```

---

## 🔧 Environment Validation

### Validation Script

The project includes environment validation in CI/CD:

```yaml
- name: Validate environment
  run: npm run validate:env
```

### Manual Validation

```bash
# Check required environment variables
node scripts/validate-environment.js

# Test external service connections
node scripts/test-appwrite-connection.js
```

---

## 🏗️ Digital Ocean App Platform Configuration

### Environment Variables in DO App Platform

**Set in Digital Ocean Console:**
1. Navigate to your app in DO App Platform
2. Go to Settings → App-Level Environment Variables
3. Add all production environment variables
4. Enable "Encrypt" for sensitive values

**Required Variables:**
- All variables from `.env.example`
- Ensure `NODE_ENV=production`
- Set appropriate `CORS_ORIGIN`

### App Spec Configuration

```yaml
name: sanad-paim
services:
- name: api
  environment_slug: node-js
  github:
    repo: HDickenson/sanad
    branch: master
  envs:
  - key: NODE_ENV
    value: production
  - key: PORT
    value: "3000"
  # Additional environment variables set in DO console
```

---

## 🔄 Environment Management Procedures

### 1. Development Environment Setup

```bash
# Copy template
cp .env.example .env.development

# Edit with development values
nano .env.development

# Validate configuration
npm run validate:env
```

### 2. Staging Environment

```bash
# Staging uses environment variables set in DO App Platform
# Configure in Digital Ocean console for staging app
```

### 3. Production Environment

```bash
# Production uses environment variables set in DO App Platform
# Configure in Digital Ocean console for production app
# Ensure all secrets are properly encrypted
```

### 4. Secret Rotation Procedures

**Monthly Rotation:**
1. Generate new secrets
2. Update GitHub repository secrets
3. Update Digital Ocean environment variables
4. Deploy to staging for testing
5. Deploy to production
6. Verify all services working
7. Revoke old secrets

---

## 🛡️ Security Best Practices

### Secret Management

1. **Never commit secrets to git**
   - Use `.env.*` files (git-ignored)
   - Use GitHub Secrets for CI/CD
   - Use DO environment variables for deployment

2. **Use strong secrets**
   - Minimum 32 characters for keys
   - Use cryptographically secure random generation
   - Rotate secrets regularly

3. **Principle of least privilege**
   - Grant minimum required permissions
   - Use separate keys for different environments
   - Monitor secret usage

### Environment Isolation

1. **Separate environments**
   - Development: Local `.env.development`
   - Staging: DO App Platform staging app
   - Production: DO App Platform production app

2. **No cross-environment access**
   - Staging cannot access production data
   - Development uses test/mock services
   - Production uses dedicated service accounts

---

## 📋 Environment Checklist

### Pre-Deployment Checklist

- [ ] All required environment variables set
- [ ] Secrets properly encrypted in DO console
- [ ] Environment validation passes
- [ ] External service connections tested
- [ ] CORS origins configured correctly
- [ ] Log levels appropriate for environment
- [ ] Rate limiting configured
- [ ] Storage provider configured

### Post-Deployment Verification

- [ ] Health check endpoint responds
- [ ] External services accessible
- [ ] Logging working correctly
- [ ] Error handling appropriate
- [ ] Performance within acceptable limits

---

## 🚨 Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```bash
   # Check what's missing
   npm run validate:env
   ```

2. **Invalid API Keys**
   ```bash
   # Test individual services
   node scripts/test-appwrite-connection.js
   ```

3. **CORS Issues**
   ```bash
   # Check CORS_ORIGIN setting
   echo $CORS_ORIGIN
   ```

### Emergency Procedures

1. **Compromised Secrets**
   - Immediately rotate all affected secrets
   - Update GitHub and DO configurations
   - Deploy emergency patch
   - Monitor for unauthorized access

2. **Service Outages**
   - Check external service status
   - Verify environment variables
   - Review application logs
   - Implement fallback procedures

---

*This guide ensures secure and proper environment management across all deployment stages while maintaining security best practices.*

name: sanad-paim
region: blr1

services:
  - name: api
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
      deploy_on_push: true
    
    # Build configuration
    build_command: npm ci && npm run build
    run_command: npm run start:prod
    
    # Environment configuration
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    
    # Health check configuration
    health_check:
      http_path: /health
      initial_delay_seconds: 60
      period_seconds: 10
      timeout_seconds: 5
      success_threshold: 1
      failure_threshold: 3
    
    # HTTP configuration
    http_port: 3000
    
    # Environment variables
    envs:
      # Application Configuration
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "3000"
      
      # OpenAI Configuration
      - key: OPENAI_API_KEY
        value: ${OPENAI_API_KEY}
      - key: OPENAI_MODEL
        value: gpt-4
      
      # Twilio WhatsApp Configuration
      - key: TWILIO_ACCOUNT_SID
        value: ${TWILIO_ACCOUNT_SID}
      - key: TWILIO_AUTH_TOKEN
        value: ${TWILIO_AUTH_TOKEN}
      - key: TWILIO_WHATSAPP_NUMBER
        value: ${TWILIO_WHATSAPP_NUMBER}
      
      # Security Configuration
      - key: JWT_SECRET
        value: ${JWT_SECRET}
      - key: ENCRYPTION_KEY
        value: ${ENCRYPTION_KEY}
      
      # Appwrite Configuration (Self-Hosted)
      - key: APPWRITE_ENDPOINT
        value: https://appwrite.sanad.kanousai.com/v1
      - key: APPWRITE_PROJECT_ID
        value: ${APPWRITE_PROJECT_ID}
      - key: APPWRITE_API_KEY
        value: ${APPWRITE_API_KEY}
      - key: APPWRITE_DATABASE_ID
        value: sanad-production
      - key: APPWRITE_STORAGE_BUCKET_ID
        value: sanad-files
      
      # Collection IDs
      - key: APPWRITE_USERS_COLLECTION_ID
        value: users
      - key: APPWRITE_REGISTRATIONS_COLLECTION_ID
        value: registrations
      - key: APPWRITE_WHITELIST_COLLECTION_ID
        value: whitelist
      - key: APPWRITE_SESSIONS_COLLECTION_ID
        value: sessions
      - key: APPWRITE_MEMORIES_COLLECTION_ID
        value: memories
      - key: APPWRITE_REMINDERS_COLLECTION_ID
        value: reminders
      - key: APPWRITE_CONVERSATIONS_COLLECTION_ID
        value: conversations
      - key: APPWRITE_EMAILS_COLLECTION_ID
        value: emails
      - key: APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID
        value: email_confirmations
      
      # Basic Configuration
      - key: REGISTRATION_ENABLED
        value: "true"
      - key: LOG_LEVEL
        value: info

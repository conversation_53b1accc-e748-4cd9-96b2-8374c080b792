import { Module } from '@nestjs/common';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { WebhookModule } from '../webhook/webhook.module';
import { CoreModule } from '../core/core.module';

@Module({
  imports: [CoreModule, WebhookModule],
  controllers: [HealthController],
  providers: [HealthService],
  exports: [HealthService],
})
export class HealthModule {}

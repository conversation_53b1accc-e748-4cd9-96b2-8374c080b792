import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  Call<PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import { CacheService } from './cache.service';

export const CACHE_KEY_METADATA = 'cache_key';
export const CACHE_TTL_METADATA = 'cache_ttl';
export const CACHE_PREFIX_METADATA = 'cache_prefix';

export interface CacheKeyOptions {
  key?: string;
  ttl?: number;
  prefix?: string;
  includeQuery?: boolean;
  includeHeaders?: string[];
  excludeParams?: string[];
}

// Decorator for caching
export const CacheKey = (options: CacheKeyOptions | string) => {
  const opts = typeof options === 'string' ? { key: options } : options;
  return (
    _target: any,
    _propertyKey: string,
    descriptor: PropertyDescriptor,
  ) => {
    Reflect.defineMetadata(CACHE_KEY_METADATA, opts, descriptor.value);
    return descriptor;
  };
};

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const cacheOptions = this.reflector.get<CacheKeyOptions>(
      CACHE_KEY_METADATA,
      context.getHandler(),
    );

    if (!cacheOptions) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const cacheKey = this.generateCacheKey(request, cacheOptions);

    try {
      // Try to get from cache
      const cachedResult = await this.cacheService.get(cacheKey, {
        prefix: cacheOptions.prefix,
      });

      if (cachedResult !== null) {
        this.logger.debug(`Cache hit for key: ${cacheKey}`);
        return of(cachedResult);
      }

      this.logger.debug(`Cache miss for key: ${cacheKey}`);

      // Execute the handler and cache the result
      return next.handle().pipe(
        tap(async (result) => {
          if (result !== undefined && result !== null) {
            await this.cacheService.set(cacheKey, result, {
              ttl: cacheOptions.ttl,
              prefix: cacheOptions.prefix,
            });
            this.logger.debug(`Cached result for key: ${cacheKey}`);
          }
        }),
      );
    } catch (error) {
      this.logger.error(`Cache interceptor error for key ${cacheKey}:`, error);
      return next.handle();
    }
  }

  private generateCacheKey(request: any, options: CacheKeyOptions): string {
    const parts: string[] = [];

    // Base key
    if (options.key) {
      parts.push(options.key);
    } else {
      parts.push(request.route?.path || request.url);
    }

    // Include method
    parts.push(request.method);

    // Include query parameters
    if (options.includeQuery && request.query) {
      const queryParams = { ...request.query };

      // Exclude specified parameters
      if (options.excludeParams) {
        options.excludeParams.forEach((param) => {
          delete queryParams[param];
        });
      }

      if (Object.keys(queryParams).length > 0) {
        const sortedQuery = Object.keys(queryParams)
          .sort()
          .map((key) => `${key}=${queryParams[key]}`)
          .join('&');
        parts.push(sortedQuery);
      }
    }

    // Include specific headers
    if (options.includeHeaders && request.headers) {
      options.includeHeaders.forEach((headerName) => {
        const headerValue = request.headers[headerName.toLowerCase()];
        if (headerValue) {
          parts.push(`${headerName}:${headerValue}`);
        }
      });
    }

    // Include user ID if available
    if (request.user?.id) {
      parts.push(`user:${request.user.id}`);
    }

    return parts.join(':');
  }
}

// Additional decorators for common caching patterns
export const CacheResponse = (ttl: number = 3600, prefix?: string) => {
  return CacheKey({ ttl, prefix, includeQuery: true });
};

export const CacheUserResponse = (ttl: number = 1800) => {
  return CacheKey({
    ttl,
    prefix: 'user',
    includeQuery: true,
    includeHeaders: ['authorization'],
  });
};

export const CacheApiResponse = (key: string, ttl: number = 3600) => {
  return CacheKey({ key, ttl, prefix: 'api' });
};

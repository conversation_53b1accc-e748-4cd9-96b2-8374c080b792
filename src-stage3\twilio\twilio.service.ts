import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twilio } from 'twilio';

@Injectable()
export class TwilioService {
  private twilio: Twilio | null = null;
  private whatsappNumber: string;

  constructor(private configService: ConfigService) {
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
    this.whatsappNumber = this.configService.get<string>('TWILIO_WHATSAPP_NUMBER', '');

    if (accountSid && authToken) {
      this.twilio = new Twilio(accountSid, authToken);
    }
  }

  async testConnection() {
    if (!this.twilio) {
      return {
        status: 'error',
        message: 'Twilio credentials not configured',
        configured: false,
        timestamp: new Date().toISOString()
      };
    }

    try {
      // Test connection by fetching account info
      const account = await this.twilio.api.accounts.get();
      return {
        status: 'success',
        message: 'Twilio connection successful',
        configured: true,
        accountSid: account.sid,
        accountStatus: account.status,
        whatsappNumber: this.whatsappNumber,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        message: 'Twilio connection failed',
        error: error.message,
        configured: true,
        timestamp: new Date().toISOString()
      };
    }
  }

  async sendWhatsAppMessage(to: string, message: string) {
    if (!this.twilio) {
      throw new Error('Twilio not configured');
    }

    if (!this.whatsappNumber) {
      throw new Error('Twilio WhatsApp number not configured');
    }

    try {
      const result = await this.twilio.messages.create({
        body: message,
        from: `whatsapp:${this.whatsappNumber}`,
        to: `whatsapp:${to}`,
      });

      return {
        status: 'success',
        messageSid: result.sid,
        to: to,
        from: this.whatsappNumber,
        message: message,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`Twilio message failed: ${error.message}`);
    }
  }
}

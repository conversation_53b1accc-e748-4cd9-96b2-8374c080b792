# Notification Channels Configuration for PAIM Alerting
# Defines various notification channels for different alert types

# Slack Configuration
slack:
  # Critical alerts channel
  critical:
    webhook_url: "${SLACK_CRITICAL_WEBHOOK_URL}"
    channel: "#paim-critical-alerts"
    username: "PAIM Alert Bot"
    icon_emoji: ":rotating_light:"
    title: "🚨 CRITICAL ALERT"
    color: "danger"
    
  # Warning alerts channel
  warning:
    webhook_url: "${SLACK_WARNING_WEBHOOK_URL}"
    channel: "#paim-alerts"
    username: "PAIM Alert Bot"
    icon_emoji: ":warning:"
    title: "⚠️ WARNING ALERT"
    color: "warning"
    
  # Info alerts channel
  info:
    webhook_url: "${SLACK_INFO_WEBHOOK_URL}"
    channel: "#paim-monitoring"
    username: "PAIM Monitor Bot"
    icon_emoji: ":information_source:"
    title: "ℹ️ INFO ALERT"
    color: "good"

# Email Configuration
email:
  # SMTP settings
  smtp:
    host: "${SMTP_HOST}"
    port: 587
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"
    from: "<EMAIL>"
    
  # Distribution lists
  critical:
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    
  warning:
    - "<EMAIL>"
    - "<EMAIL>"
    
  info:
    - "<EMAIL>"

# PagerDuty Configuration
pagerduty:
  # Critical alerts - immediate escalation
  critical:
    integration_key: "${PAGERDUTY_CRITICAL_KEY}"
    severity: "critical"
    
  # Warning alerts - delayed escalation
  warning:
    integration_key: "${PAGERDUTY_WARNING_KEY}"
    severity: "warning"

# Microsoft Teams Configuration
teams:
  # Main team channel
  main:
    webhook_url: "${TEAMS_WEBHOOK_URL}"
    title: "PAIM Alert"
    
# Discord Configuration
discord:
  # Development team channel
  dev:
    webhook_url: "${DISCORD_DEV_WEBHOOK_URL}"
    username: "PAIM Bot"
    avatar_url: "https://paim.local/assets/bot-avatar.png"

# Webhook Configuration for Custom Integrations
webhooks:
  # Custom monitoring dashboard
  dashboard:
    url: "${DASHBOARD_WEBHOOK_URL}"
    method: "POST"
    headers:
      Authorization: "Bearer ${DASHBOARD_API_TOKEN}"
      Content-Type: "application/json"
      
  # ITSM integration
  itsm:
    url: "${ITSM_WEBHOOK_URL}"
    method: "POST"
    headers:
      X-API-Key: "${ITSM_API_KEY}"
      Content-Type: "application/json"

# SMS Configuration (for critical alerts)
sms:
  provider: "twilio"
  account_sid: "${TWILIO_ACCOUNT_SID}"
  auth_token: "${TWILIO_AUTH_TOKEN}"
  from_number: "${TWILIO_PHONE_NUMBER}"
  
  # Emergency contact numbers
  critical_contacts:
    - "+**********"  # On-call engineer
    - "+**********"  # Backup on-call
    - "+**********"  # Team lead

# Voice Call Configuration (for critical alerts)
voice:
  provider: "twilio"
  account_sid: "${TWILIO_ACCOUNT_SID}"
  auth_token: "${TWILIO_AUTH_TOKEN}"
  from_number: "${TWILIO_PHONE_NUMBER}"
  
  # Voice message template
  message: "This is an urgent alert from PAIM. Alert name: {alert_name}. Description: {description}. Please check the monitoring dashboard immediately."
  
  # Emergency contact numbers
  critical_contacts:
    - "+**********"  # On-call engineer
    - "+**********"  # Backup on-call

# Mobile Push Notifications
mobile:
  # Firebase Cloud Messaging
  fcm:
    server_key: "${FCM_SERVER_KEY}"
    
  # Apple Push Notification Service
  apns:
    key_id: "${APNS_KEY_ID}"
    team_id: "${APNS_TEAM_ID}"
    private_key: "${APNS_PRIVATE_KEY}"

# Escalation Policies
escalation:
  # Critical alert escalation
  critical:
    - level: 1
      delay: 0
      channels: ["slack.critical", "email.critical", "pagerduty.critical"]
      
    - level: 2
      delay: 300  # 5 minutes
      channels: ["sms.critical_contacts"]
      
    - level: 3
      delay: 900  # 15 minutes
      channels: ["voice.critical_contacts"]
      
  # Warning alert escalation
  warning:
    - level: 1
      delay: 0
      channels: ["slack.warning", "email.warning"]
      
    - level: 2
      delay: 1800  # 30 minutes
      channels: ["pagerduty.warning"]
      
  # Info alert escalation
  info:
    - level: 1
      delay: 0
      channels: ["slack.info", "email.info"]

# Business Hours Configuration
business_hours:
  timezone: "UTC"
  weekdays:
    start: "09:00"
    end: "17:00"
  weekend:
    enabled: false
    
  # Different escalation during business hours
  business_hours_escalation:
    critical:
      - level: 1
        delay: 0
        channels: ["slack.critical", "email.critical"]
        
    warning:
      - level: 1
        delay: 0
        channels: ["slack.warning"]
        
  # After hours escalation
  after_hours_escalation:
    critical:
      - level: 1
        delay: 0
        channels: ["slack.critical", "email.critical", "pagerduty.critical", "sms.critical_contacts"]

# Alert Grouping and Deduplication
grouping:
  # Group alerts by service
  by_service:
    group_by: ["service"]
    group_wait: "30s"
    group_interval: "5m"
    repeat_interval: "1h"
    
  # Group alerts by severity
  by_severity:
    group_by: ["severity"]
    group_wait: "10s"
    group_interval: "2m"
    repeat_interval: "30m"

# Silence Patterns
silences:
  # Maintenance windows
  maintenance:
    pattern: "maintenance"
    duration: "4h"
    
  # Known issues
  known_issues:
    pattern: "known_issue"
    duration: "24h"
    
  # Development environment
  development:
    pattern: "env=development"
    duration: "1h"

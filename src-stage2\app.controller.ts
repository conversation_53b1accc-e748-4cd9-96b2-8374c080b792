import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getRoot() {
    return {
      message: 'Welcome to Sanad PAIM - Stage 2',
      description: 'NestJS core dependencies integrated',
      stage: 'Stage 2: NestJS Core Dependencies',
      framework: 'NestJS',
      endpoints: {
        root: '/',
        health: '/health',
        status: '/status'
      },
      timestamp: new Date().toISOString()
    };
  }

  @Get('status')
  getStatus() {
    return this.appService.getStatus();
  }
}

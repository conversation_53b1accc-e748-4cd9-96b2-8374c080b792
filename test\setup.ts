/**
 * Jest Test Setup
 * Global test configuration and utilities
 */

import { ConfigService } from '@nestjs/config';

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';
process.env.PORT = '3001';

// Mock external services by default
jest.mock('twilio', () => ({
  Twilio: jest.fn().mockImplementation(() => ({
    messages: {
      create: jest.fn().mockResolvedValue({
        sid: 'test-message-sid',
        status: 'sent',
      }),
    },
    webhooks: {
      validateRequest: jest.fn().mockReturnValue(true),
    },
  })),
}));

jest.mock('openai', () => ({
  OpenAI: jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [
            {
              message: {
                content: 'Test AI response',
                role: 'assistant',
              },
            },
          ],
        }),
      },
    },
    audio: {
      transcriptions: {
        create: jest.fn().mockResolvedValue({
          text: 'Test transcription',
        }),
      },
    },
  })),
}));

jest.mock('node-appwrite', () => ({
  Client: jest.fn().mockImplementation(() => ({
    setEndpoint: jest.fn().mockReturnThis(),
    setProject: jest.fn().mockReturnThis(),
    setKey: jest.fn().mockReturnThis(),
  })),
  Databases: jest.fn().mockImplementation(() => ({
    createDocument: jest.fn().mockResolvedValue({
      $id: 'test-doc-id',
      $createdAt: new Date().toISOString(),
      $updatedAt: new Date().toISOString(),
    }),
    getDocument: jest.fn().mockResolvedValue({
      $id: 'test-doc-id',
      $createdAt: new Date().toISOString(),
      $updatedAt: new Date().toISOString(),
    }),
    updateDocument: jest.fn().mockResolvedValue({
      $id: 'test-doc-id',
      $createdAt: new Date().toISOString(),
      $updatedAt: new Date().toISOString(),
    }),
    deleteDocument: jest.fn().mockResolvedValue({}),
    listDocuments: jest.fn().mockResolvedValue({
      documents: [],
      total: 0,
    }),
  })),
  Users: jest.fn().mockImplementation(() => ({
    create: jest.fn().mockResolvedValue({
      $id: 'test-user-id',
      email: '<EMAIL>',
    }),
    get: jest.fn().mockResolvedValue({
      $id: 'test-user-id',
      email: '<EMAIL>',
    }),
  })),
  Storage: jest.fn().mockImplementation(() => ({
    createFile: jest.fn().mockResolvedValue({
      $id: 'test-file-id',
      name: 'test-file.txt',
    }),
    getFile: jest.fn().mockResolvedValue({
      $id: 'test-file-id',
      name: 'test-file.txt',
    }),
  })),
}));

// Global test utilities
global.testUtils = {
  // Mock ConfigService with common test values
  createMockConfigService: (): Partial<ConfigService> => ({
    get: jest.fn((key: string) => {
      const config = {
        NODE_ENV: 'test',
        PORT: 3001,
        LOG_LEVEL: 'error',
        TWILIO_ACCOUNT_SID: 'test_account_sid',
        TWILIO_AUTH_TOKEN: 'test_auth_token',
        TWILIO_WHATSAPP_NUMBER: 'whatsapp:+**********',
        OPENAI_API_KEY: 'test_openai_key',
        APPWRITE_ENDPOINT: 'https://test.appwrite.io/v1',
        APPWRITE_PROJECT_ID: 'test_project_id',
        APPWRITE_API_KEY: 'test_api_key',
        APPWRITE_DATABASE_ID: 'test_database_id',
      };
      return config[key];
    }),
  }),

  // Create mock WhatsApp message
  createMockWhatsAppMessage: (overrides = {}) => ({
    From: 'whatsapp:+**********',
    To: 'whatsapp:+**********',
    Body: 'Test message',
    MessageSid: 'test-message-sid',
    AccountSid: 'test-account-sid',
    NumMedia: '0',
    ...overrides,
  }),

  // Create mock user data
  createMockUser: (overrides = {}) => ({
    id: 'test-user-id',
    phoneNumber: '+**********',
    name: 'Test User',
    email: '<EMAIL>',
    isActive: true,
    preferences: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // Create mock conversation data
  createMockConversation: (overrides = {}) => ({
    id: 'test-conversation-id',
    userId: 'test-user-id',
    messages: [],
    context: {},
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // Wait for async operations
  waitFor: (ms: number) => new Promise((resolve) => setTimeout(resolve, ms)),

  // Mock Express request
  createMockRequest: (overrides = {}) => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    ...overrides,
  }),

  // Mock Express response
  createMockResponse: () => {
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      end: jest.fn().mockReturnThis(),
      setHeader: jest.fn().mockReturnThis(),
    };
    return res;
  },
};

// Global test timeout
jest.setTimeout(30000);

// Console suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Type declarations for global utilities
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        createMockConfigService: () => Partial<ConfigService>;
        createMockWhatsAppMessage: (overrides?: any) => any;
        createMockUser: (overrides?: any) => any;
        createMockConversation: (overrides?: any) => any;
        waitFor: (ms: number) => Promise<void>;
        createMockRequest: (overrides?: any) => any;
        createMockResponse: () => any;
      };
    }
  }
}

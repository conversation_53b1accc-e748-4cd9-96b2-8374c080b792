import {
  Controller,
  Post,
  Body,
  Headers,
  Req,
  Res,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { LoggerService } from '../../core';
import { TwilioService } from '../services/twilio.service';
import { MessageProcessingService } from '../services/message-processing.service';
import { WebhookValidationGuard } from '../guards/webhook-validation.guard';
import { IWhatsAppMessage } from '../../interfaces';

@ApiTags('webhook')
@Controller('webhook')
export class WhatsAppController {
  constructor(
    private twilioService: TwilioService,
    private messageProcessingService: MessageProcessingService,
    private logger: LoggerService,
  ) {}

  @Post('whatsapp')
  @UseGuards(WebhookValidationGuard)
  @ApiOperation({ summary: 'Receive WhatsApp webhook messages from Twilio' })
  @ApiResponse({ status: 200, description: 'Message processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook data' })
  @ApiResponse({ status: 401, description: 'Invalid webhook signature' })
  async receiveWhatsAppMessage(
    @Body() body: any,
    @Headers() _headers: Record<string, string>,
    @Req() _req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `Received WhatsApp webhook: ${JSON.stringify(body)}`,
        'WhatsAppController',
      );

      // Parse the incoming message
      const whatsappMessage = this.parseWhatsAppMessage(body);

      if (!whatsappMessage) {
        this.logger.warn(
          'Invalid WhatsApp message format',
          'WhatsAppController',
        );
        res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Invalid message format',
        });
        return;
      }

      // Process the message asynchronously
      this.processMessageAsync(whatsappMessage, startTime);

      // Respond immediately to Twilio (required within 15 seconds)
      res.status(HttpStatus.OK).json({
        status: 'received',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(
        `Webhook processing failed after ${duration}ms`,
        error instanceof Error ? error.stack : undefined,
        'WhatsAppController',
      );

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  @Post('whatsapp/status')
  @UseGuards(WebhookValidationGuard)
  @ApiOperation({
    summary: 'Receive WhatsApp message status updates from Twilio',
  })
  @ApiResponse({
    status: 200,
    description: 'Status update processed successfully',
  })
  async receiveStatusUpdate(
    @Body() body: any,
    @Headers() _headers: Record<string, string>,
    @Req() _req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      this.logger.log(
        `Received WhatsApp status update: ${JSON.stringify(body)}`,
        'WhatsAppController',
      );

      // Process status update
      await this.messageProcessingService.processStatusUpdate(body);

      res.status(HttpStatus.OK).json({
        status: 'processed',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(
        'Status update processing failed',
        error instanceof Error ? error.stack : undefined,
        'WhatsAppController',
      );

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  private parseWhatsAppMessage(body: any): IWhatsAppMessage | null {
    try {
      // Validate required fields
      if (!body.From || !body.To || !body.MessageSid || !body.AccountSid) {
        this.logger.warn(
          `Missing required fields in webhook: ${JSON.stringify(body)}`,
          'WhatsAppController',
        );
        return null;
      }

      // Extract message data
      const message: IWhatsAppMessage = {
        From: body.From,
        To: body.To,
        Body: body.Body || '',
        MessageSid: body.MessageSid,
        AccountSid: body.AccountSid,
        NumMedia: body.NumMedia || '0',
      };

      // Add media information if present
      if (parseInt(message.NumMedia) > 0) {
        message.MediaUrl0 = body.MediaUrl0;
        message.MediaContentType0 = body.MediaContentType0;
      }

      return message;
    } catch (error) {
      this.logger.error(
        'Failed to parse WhatsApp message',
        error instanceof Error ? error.stack : undefined,
        'WhatsAppController',
      );
      return null;
    }
  }

  private async processMessageAsync(
    whatsappMessage: IWhatsAppMessage,
    startTime: number,
  ): Promise<void> {
    try {
      await this.messageProcessingService.processIncomingMessage(
        whatsappMessage,
      );

      const duration = Date.now() - startTime;
      this.logger.log(
        `Message processed successfully in ${duration}ms`,
        'WhatsAppController',
      );
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(
        `Async message processing failed after ${duration}ms`,
        error instanceof Error ? error.stack : undefined,
        'WhatsAppController',
      );

      // Optionally, you could implement retry logic here
      // or send an error message back to the user
      try {
        await this.sendErrorResponse(whatsappMessage.From);
      } catch (sendError) {
        this.logger.error(
          'Failed to send error response to user',
          sendError instanceof Error ? sendError.stack : undefined,
          'WhatsAppController',
        );
      }
    }
  }

  private async sendErrorResponse(to: string): Promise<void> {
    const errorMessage =
      "I'm sorry, I encountered an issue processing your message. Please try again in a moment.";

    await this.twilioService.sendTextMessage(to, errorMessage);
  }
}

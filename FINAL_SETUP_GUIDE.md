# 🎯 Final Self-Hosted Setup Guide

## ✅ **Current Status**

### **Infrastructure Ready:**
- ✅ **Appwrite Droplet**: Created at `***************`
- ✅ **DNS Records**: Configured by you
- ✅ **SSL Certificates**: Provided by you
- ✅ **Application Configuration**: Updated for self-hosted

### **Deployment Issues:**
- ❌ **Digital Ocean App**: Deployments failing (build issues)
- ✅ **Appwrite Infrastructure**: Ready to deploy

## 🚀 **Complete Setup Instructions**

### **Step 1: Set Up Appwrite on Droplet**

SSH to your droplet and run the setup:

```bash
# SSH to droplet
ssh root@***************

# Download and run setup script
curl -o setup-appwrite.sh https://raw.githubusercontent.com/your-repo/setup-appwrite-complete.sh
chmod +x setup-appwrite.sh
./setup-appwrite.sh
```

**Or manually run these commands:**

```bash
# Update system
apt-get update && apt-get install -y curl

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create directory
mkdir -p /opt/appwrite && cd /opt/appwrite

# Create simplified Docker Compose
cat > docker-compose.yml << 'EOF'
version: '3.8'
services:
  appwrite:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - appwrite-uploads:/storage/uploads
      - appwrite-cache:/storage/cache
      - appwrite-config:/storage/config
    depends_on:
      - mariadb
      - redis
    environment:
      - _APP_ENV=production
      - _APP_LOCALE=en
      - _APP_CONSOLE_WHITELIST_ROOT=enabled
      - _APP_CONSOLE_WHITELIST_EMAILS=<EMAIL>
      - _APP_DOMAIN=appwrite.sanad.kanousai.com
      - _APP_DOMAIN_TARGET=appwrite.sanad.kanousai.com
      - _APP_REDIS_HOST=redis
      - _APP_DB_HOST=mariadb
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=appwrite
      - _APP_DB_PASS=secure_password_123
      - _APP_OPENSSL_KEY_V1=your_generated_key_here

  mariadb:
    image: mariadb:10.7
    container_name: sanad-mariadb
    restart: unless-stopped
    volumes:
      - appwrite-mariadb:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root_password_123
      - MYSQL_DATABASE=appwrite
      - MYSQL_USER=appwrite
      - MYSQL_PASSWORD=secure_password_123

  redis:
    image: redis:7.0-alpine
    container_name: sanad-redis
    restart: unless-stopped
    volumes:
      - appwrite-redis:/data

volumes:
  appwrite-mariadb:
  appwrite-redis:
  appwrite-cache:
  appwrite-uploads:
  appwrite-config:
EOF

# Start services
docker-compose up -d

# Check status
docker-compose ps
```

### **Step 2: Access Appwrite Console**

1. **Open browser**: `http://appwrite.sanad.kanousai.com/console`
2. **Create account**: Set up admin account
3. **Create project**: Name it "Sanad Production"
4. **Note credentials**:
   - Project ID
   - API Key (create with full permissions)

### **Step 3: Set Up Database Schema**

From your local machine:

```bash
# Set environment variables
export APPWRITE_ENDPOINT="http://appwrite.sanad.kanousai.com/v1"
export APPWRITE_PROJECT_ID="your-project-id"
export APPWRITE_API_KEY="your-api-key"

# Run schema setup
node scripts/setup-appwrite-schema.js
```

### **Step 4: Fix Application Deployment**

The Digital Ocean app deployment is failing. Let's fix it:

```bash
# Create minimal working app.yaml
cat > .do/app-minimal.yaml << 'EOF'
name: sanad-paim
region: nyc

services:
  - name: api
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
    
    build_command: npm install && npm run build
    run_command: npm start
    
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    
    http_port: 3000
    
    envs:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "3000"
      - key: APPWRITE_ENDPOINT
        value: http://appwrite.sanad.kanousai.com/v1
EOF

# Deploy with minimal config
doctl apps update 1439002e-9be2-4c44-b695-6ddcee5740cd --spec .do/app-minimal.yaml
```

## 🎯 **Alternative: Local Development**

If Digital Ocean deployment continues to fail, you can run locally:

```bash
# Set environment variables
export APPWRITE_ENDPOINT="http://appwrite.sanad.kanousai.com/v1"
export APPWRITE_PROJECT_ID="your-project-id"
export APPWRITE_API_KEY="your-api-key"

# Run locally
npm run start:dev
```

## 📊 **Current Infrastructure**

### **Appwrite Droplet (***************)**
- **Services**: Appwrite, MariaDB, Redis
- **Access**: `http://appwrite.sanad.kanousai.com`
- **Console**: `http://appwrite.sanad.kanousai.com/console`
- **Cost**: ~$24/month

### **Digital Ocean App (sanad-paim)**
- **Status**: Deployment issues (build failures)
- **ID**: `1439002e-9be2-4c44-b695-6ddcee5740cd`
- **Cost**: ~$5/month

## 🔧 **Troubleshooting**

### **If Appwrite won't start:**
```bash
# Check logs
docker-compose logs -f appwrite

# Restart services
docker-compose restart

# Check disk space
df -h
```

### **If app deployment fails:**
```bash
# Check deployment logs
doctl apps logs 1439002e-9be2-4c44-b695-6ddcee5740cd

# Try minimal configuration
# Use the app-minimal.yaml above
```

## 🎉 **Success Indicators**

- ✅ Appwrite console accessible
- ✅ Database collections created
- ✅ Application connects to Appwrite
- ✅ Health checks pass

## 📞 **Next Steps**

1. **Set up Appwrite** using the commands above
2. **Access console** and create project
3. **Run schema setup** to create collections
4. **Fix app deployment** or run locally
5. **Test the complete system**

**Total setup time: 30-45 minutes**
**Monthly cost: ~$30 (self-hosted) vs ~$100+ (cloud)**

---

**🎯 You now have everything needed for a complete self-hosted deployment!**

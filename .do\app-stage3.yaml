name: sanad-paim-stage3
region: nyc

services:
  - name: api-stage3
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
      deploy_on_push: false
    
    # Business logic build configuration
    build_command: rm -rf src dist && cp package-stage3.json package.json && cp tsconfig-stage2.json tsconfig.json && cp nest-cli-stage2.json nest-cli.json && cp -r src-stage3 src && npm install && npm run build
    run_command: npm run start:prod
    
    # Environment configuration
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    
    # Health check configuration
    health_check:
      http_path: /health
      initial_delay_seconds: 60
      period_seconds: 10
      timeout_seconds: 5
      success_threshold: 1
      failure_threshold: 3
    
    # HTTP configuration
    http_port: 3000
    
    # Environment variables
    envs:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "3000"
      # OpenAI Configuration (placeholder - will be configured later)
      - key: OPENAI_API_KEY
        value: ${OPENAI_API_KEY}
      - key: OPENAI_MODEL
        value: gpt-3.5-turbo
      # Twilio Configuration (placeholder - will be configured later)
      - key: TWILIO_ACCOUNT_SID
        value: ${TWILIO_ACCOUNT_SID}
      - key: TWILIO_AUTH_TOKEN
        value: ${TWILIO_AUTH_TOKEN}
      - key: TWILIO_WHATSAPP_NUMBER
        value: ${TWILIO_WHATSAPP_NUMBER}

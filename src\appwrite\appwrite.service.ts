import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client, Databases, Storage, Users } from 'node-appwrite';

@Injectable()
export class AppwriteService implements OnModuleInit {
  private readonly logger = new Logger(AppwriteService.name);
  private client: Client;
  private databases: Databases;
  private storage: Storage;
  private users: Users;

  private readonly projectId: string;
  private readonly databaseId: string;
  private readonly collections: Record<string, string>;
  private readonly bucketId: string;

  constructor(private readonly configService: ConfigService) {
    const appwriteConfig = this.configService.get('appwrite');

    this.projectId = appwriteConfig.projectId;
    this.databaseId = appwriteConfig.databaseId;
    this.collections = appwriteConfig.collections;
    this.bucketId = appwriteConfig.storage.bucketId;

    // Initialize Appwrite client
    this.client = new Client()
      .setEndpoint(appwriteConfig.endpoint)
      .setProject(this.projectId)
      .setKey(appwriteConfig.apiKey);

    // Initialize services
    this.databases = new Databases(this.client);
    this.storage = new Storage(this.client);
    this.users = new Users(this.client);
  }

  async onModuleInit() {
    try {
      // Test connection
      await this.databases.list();
      this.logger.log('✅ Connected to Appwrite successfully');
    } catch (error) {
      this.logger.error('❌ Failed to connect to Appwrite:', error);
      // In development mode, don't fail the application startup
      if (process.env.NODE_ENV === 'development') {
        this.logger.warn('⚠️ Continuing in development mode without Appwrite connection');
      } else {
        throw error;
      }
    }
  }

  // Database operations
  async createDocument(collectionId: string, documentId: string, data: any) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        collectionId,
        documentId,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Failed to create document in collection ${collectionId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async getDocument(collectionId: string, documentId: string) {
    try {
      return await this.databases.getDocument(
        this.databaseId,
        collectionId,
        documentId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get document ${documentId} from collection ${collectionId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async updateDocument(collectionId: string, documentId: string, data: any) {
    try {
      return await this.databases.updateDocument(
        this.databaseId,
        collectionId,
        documentId,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update document ${documentId} in collection ${collectionId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async deleteDocument(collectionId: string, documentId: string) {
    try {
      return await this.databases.deleteDocument(
        this.databaseId,
        collectionId,
        documentId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to delete document ${documentId} from collection ${collectionId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async listDocuments(collectionId: string, queries?: string[]) {
    try {
      return await this.databases.listDocuments(
        this.databaseId,
        collectionId,
        queries,
      );
    } catch (error) {
      this.logger.error(
        `Failed to list documents from collection ${collectionId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  // User management
  async createUser(
    userId: string,
    email: string,
    password?: string,
    name?: string,
  ) {
    try {
      return await this.users.create(userId, email, undefined, password, name);
    } catch (error) {
      this.logger.error(
        `Failed to create user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async getUser(userId: string) {
    try {
      return await this.users.get(userId);
    } catch (error) {
      this.logger.error(
        `Failed to get user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async updateUser(userId: string, data: any) {
    try {
      return await this.users.updateName(userId, data.name);
    } catch (error) {
      this.logger.error(
        `Failed to update user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async deleteUser(userId: string) {
    try {
      return await this.users.delete(userId);
    } catch (error) {
      this.logger.error(
        `Failed to delete user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async listUsers(queries?: string[]) {
    try {
      return await this.users.list(queries);
    } catch (error) {
      this.logger.error(
        'Failed to list users',
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  // File storage
  async uploadFile(fileId: string, file: any) {
    try {
      return await this.storage.createFile(this.bucketId, fileId, file);
    } catch (error) {
      this.logger.error(
        `Failed to upload file ${fileId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async getFile(fileId: string) {
    try {
      return await this.storage.getFile(this.bucketId, fileId);
    } catch (error) {
      this.logger.error(
        `Failed to get file ${fileId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async deleteFile(fileId: string) {
    try {
      return await this.storage.deleteFile(this.bucketId, fileId);
    } catch (error) {
      this.logger.error(
        `Failed to delete file ${fileId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  async getFilePreview(fileId: string, width?: number, height?: number) {
    try {
      return await this.storage.getFilePreview(
        this.bucketId,
        fileId,
        width,
        height,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get file preview for ${fileId}`,
        error instanceof Error ? error.stack : undefined,
        'AppwriteService',
      );
      throw error;
    }
  }

  // Real-time subscriptions (commented out - not available in current node-appwrite version)
  // subscribe(channels: string[], callback: (response: any) => void) {
  //   return this.client.subscribe(channels, callback);
  // }

  // Collection helpers
  getCollectionId(collectionName: keyof typeof this.collections): string {
    return this.collections[collectionName];
  }

  // Utility methods
  generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }
}

import { Injectable } from '@nestjs/common';
import {
  ISkillContext,
  IUser,
  IUserSession,
  IMessage,
  IUserConfig,
  IMemory,
  IReminder,
} from '../../interfaces';
import { LoggerService } from '../../core';

@Injectable()
export class SkillContextService {
  constructor(private logger: LoggerService) {}

  createContext(
    user: IUser,
    session: IUserSession,
    message: IMessage,
    dependencies: {
      getUserConfig: () => Promise<IUserConfig>;
      setState: (key: string, value: any) => Promise<void>;
      getState: (key: string) => Promise<any>;
      saveMemory: (memory: Partial<IMemory>) => Promise<void>;
      saveReminder: (reminder: Partial<IReminder>) => Promise<void>;
      reply: (message: string, tone?: string) => string;
    },
  ): ISkillContext {
    return new SkillContext(user, session, message, dependencies, this.logger);
  }
}

class SkillContext implements ISkillContext {
  constructor(
    public readonly user: IUser,
    public readonly session: IUserSession,
    public readonly message: IMessage,
    private dependencies: {
      getUserConfig: () => Promise<IUserConfig>;
      setState: (key: string, value: any) => Promise<void>;
      getState: (key: string) => Promise<any>;
      saveMemory: (memory: Partial<IMemory>) => Promise<void>;
      saveReminder: (reminder: Partial<IReminder>) => Promise<void>;
      reply: (message: string, tone?: string) => string;
    },
    private logger: LoggerService,
  ) {}

  async getUserConfig(): Promise<IUserConfig> {
    try {
      return await this.dependencies.getUserConfig();
    } catch (error) {
      this.logger.error(
        `Failed to get user config for ${this.user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SkillContext',
      );
      throw error;
    }
  }

  async setState(key: string, value: any): Promise<void> {
    try {
      await this.dependencies.setState(key, value);
      this.logger.debug(
        `Set state ${key} for user ${this.user.id}`,
        'SkillContext',
      );
    } catch (error) {
      this.logger.error(
        `Failed to set state ${key} for user ${this.user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SkillContext',
      );
      throw error;
    }
  }

  async getState(key: string): Promise<any> {
    try {
      const value = await this.dependencies.getState(key);
      this.logger.debug(
        `Got state ${key} for user ${this.user.id}`,
        'SkillContext',
      );
      return value;
    } catch (error) {
      this.logger.error(
        `Failed to get state ${key} for user ${this.user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SkillContext',
      );
      throw error;
    }
  }

  async saveMemory(memory: Partial<IMemory>): Promise<void> {
    try {
      const memoryWithUser = {
        ...memory,
        userId: this.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await this.dependencies.saveMemory(memoryWithUser);

      this.logger.logMemoryOperation('save', this.user.id, undefined, {
        content: memory.content?.substring(0, 100),
      });
    } catch (error) {
      this.logger.error(
        `Failed to save memory for user ${this.user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SkillContext',
      );
      throw error;
    }
  }

  async saveReminder(reminder: Partial<IReminder>): Promise<void> {
    try {
      const reminderWithUser = {
        ...reminder,
        userId: this.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await this.dependencies.saveReminder(reminderWithUser);

      this.logger.log(
        `Saved reminder for user ${this.user.id}: ${reminder.title}`,
        'SkillContext',
      );
    } catch (error) {
      this.logger.error(
        `Failed to save reminder for user ${this.user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SkillContext',
      );
      throw error;
    }
  }

  reply(message: string, tone?: string): string {
    try {
      const formattedReply = this.dependencies.reply(
        message,
        tone || this.user.tone,
      );

      this.logger.debug(
        `Generated reply for user ${this.user.id} with tone ${tone || this.user.tone}`,
        'SkillContext',
      );

      return formattedReply;
    } catch (error) {
      this.logger.error(
        `Failed to generate reply for user ${this.user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SkillContext',
      );
      return message; // Fallback to original message
    }
  }

  log(level: string, message: string, meta?: any): void {
    const contextMeta = {
      userId: this.user.id,
      sessionId: this.session.sessionId,
      messageId: this.message.id,
      ...meta,
    };

    switch (level.toLowerCase()) {
      case 'error':
        this.logger.error(message, undefined, 'SkillContext');
        break;
      case 'warn':
        this.logger.warn(message, 'SkillContext');
        break;
      case 'debug':
        this.logger.debug(message, 'SkillContext');
        break;
      case 'verbose':
        this.logger.verbose(message, 'SkillContext');
        break;
      default:
        this.logger.log(message, 'SkillContext');
    }

    // Log the meta information separately for structured logging
    if (meta) {
      this.logger.debug(
        `Context meta: ${JSON.stringify(contextMeta)}`,
        'SkillContext',
      );
    }
  }

  // Helper methods for common operations
  async hasState(key: string): Promise<boolean> {
    try {
      const value = await this.getState(key);
      return value !== undefined && value !== null;
    } catch {
      return false;
    }
  }

  async removeState(key: string): Promise<void> {
    await this.setState(key, undefined);
  }

  async getStateWithDefault<T>(key: string, defaultValue: T): Promise<T> {
    try {
      const value = await this.getState(key);
      return value !== undefined ? value : defaultValue;
    } catch {
      return defaultValue;
    }
  }

  async incrementState(key: string, increment: number = 1): Promise<number> {
    const currentValue = await this.getStateWithDefault(key, 0);
    const newValue =
      (typeof currentValue === 'number' ? currentValue : 0) + increment;
    await this.setState(key, newValue);
    return newValue;
  }

  async appendToStateArray<T>(key: string, item: T): Promise<T[]> {
    const currentArray = await this.getStateWithDefault<T[]>(key, []);
    const newArray = Array.isArray(currentArray)
      ? [...currentArray, item]
      : [item];
    await this.setState(key, newArray);
    return newArray;
  }

  // Context information getters
  getConversationHistory(): any[] {
    return this.session.conversationHistory || [];
  }

  getLastMessage(): any | undefined {
    const history = this.getConversationHistory();
    return history.length > 0 ? history[history.length - 1] : undefined;
  }

  getMessagesSince(timestamp: Date): any[] {
    return this.getConversationHistory().filter(
      (msg) => msg.timestamp && new Date(msg.timestamp) > timestamp,
    );
  }

  isFirstInteraction(): boolean {
    return this.getConversationHistory().length <= 1;
  }

  getUserPreference(key: string): any {
    return this.user.preferences?.[key];
  }

  getUserLanguage(): string {
    return this.user.preferences?.language || 'en';
  }

  getUserTimezone(): string {
    return this.user.preferences?.timezone || 'UTC';
  }
}

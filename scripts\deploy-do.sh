#!/bin/bash

# Digital Ocean App Platform Deployment Script for Sanad
# This script builds and deploys the application to Digital Ocean App Platform

set -e

echo "🚀 Starting Digital Ocean App Platform deployment for Sanad..."

# Check if doctl CLI is installed
if ! command -v doctl &> /dev/null; then
    echo "❌ doctl CLI not found. Please install it first:"
    echo "   Visit: https://docs.digitalocean.com/reference/doctl/how-to/install/"
    echo "   Or run: curl -sL https://github.com/digitalocean/doctl/releases/download/v1.94.0/doctl-1.94.0-linux-amd64.tar.gz | tar -xzv"
    exit 1
fi

echo "✅ doctl CLI found"

# Check if user is authenticated
if ! doctl account get &> /dev/null; then
    echo "❌ doctl not authenticated. Please run: doctl auth init"
    exit 1
fi

echo "✅ doctl authenticated"

# Build the application
echo "📦 Building application..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed - dist directory not found"
    exit 1
fi

echo "✅ Build completed successfully"

# Check if app already exists
echo "🔍 Checking if app exists..."
APP_EXISTS=false
APP_ID=""

# Get list of apps and check if sanad-paim exists
APPS=$(doctl apps list --format ID,Spec.Name --no-header)
while IFS=$'\t' read -r id name; do
    if [ "$name" = "sanad-paim" ]; then
        APP_EXISTS=true
        APP_ID="$id"
        echo "✅ Found existing app with ID: $APP_ID"
        break
    fi
done <<< "$APPS"

if [ "$APP_EXISTS" = true ]; then
    # Update existing app
    echo "🔄 Updating existing app..."
    doctl apps update "$APP_ID" --spec .do/app.yaml
    
    if [ $? -eq 0 ]; then
        echo "✅ App updated successfully!"
        echo "🌐 App URL: https://sanad-paim-*.ondigitalocean.app"
    else
        echo "❌ App update failed"
        exit 1
    fi
else
    # Create new app
    echo "🆕 Creating new app..."
    doctl apps create --spec .do/app.yaml
    
    if [ $? -eq 0 ]; then
        echo "✅ App created successfully!"
        echo "🌐 App URL will be available shortly at: https://sanad-paim-*.ondigitalocean.app"
    else
        echo "❌ App creation failed"
        exit 1
    fi
fi

# Get app info
echo "📊 Getting app information..."
doctl apps list --format ID,Spec.Name,DefaultIngress,CreatedAt,UpdatedAt | grep sanad-paim || true

echo ""
echo "🎉 Deployment completed!"
echo "📋 Next steps:"
echo "   1. Configure your environment variables in the DO dashboard"
echo "   2. Update your Twilio webhook URL to point to the new DO app"
echo "   3. Test the deployment with a health check"
echo ""
echo "🔗 Useful commands:"
echo "   doctl apps list                    # List all apps"
echo "   doctl apps get <app-id>            # Get app details"
echo "   doctl apps logs <app-id>           # View app logs"
echo "   doctl apps delete <app-id>         # Delete app"

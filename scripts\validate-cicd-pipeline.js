#!/usr/bin/env node

/**
 * CI/CD Pipeline Validation Script
 * 
 * This script validates that all components of the CI/CD pipeline
 * are properly configured and working correctly.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 CI/CD Pipeline Validation Starting...\n');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description}: ${filePath}`, 'green');
    return true;
  } else {
    log(`❌ ${description}: ${filePath} (NOT FOUND)`, 'red');
    return false;
  }
}

function runCommand(command, description, optional = false) {
  try {
    log(`🔄 ${description}...`, 'blue');
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} - SUCCESS`, 'green');
    return { success: true, output };
  } catch (error) {
    if (optional) {
      log(`⚠️ ${description} - SKIPPED (${error.message})`, 'yellow');
      return { success: false, output: error.message, optional: true };
    } else {
      log(`❌ ${description} - FAILED (${error.message})`, 'red');
      return { success: false, output: error.message };
    }
  }
}

// Validation Results
const results = {
  files: [],
  commands: [],
  overall: true
};

console.log('📁 Checking CI/CD Configuration Files...\n');

// Check GitHub Actions workflows
const workflows = [
  '.github/workflows/ci-cd.yml',
  '.github/workflows/deploy-production.yml',
  '.github/workflows/deploy-staging.yml'
];

workflows.forEach(workflow => {
  const exists = checkFile(workflow, 'GitHub Actions Workflow');
  results.files.push({ file: workflow, exists });
  if (!exists) results.overall = false;
});

// Check Docker files
const dockerFiles = [
  'Dockerfile',
  'Dockerfile.digitalocean',
  'docker-compose.digitalocean.yml'
];

dockerFiles.forEach(dockerFile => {
  const exists = checkFile(dockerFile, 'Docker Configuration');
  results.files.push({ file: dockerFile, exists });
  if (!exists) results.overall = false;
});

// Check configuration files
const configFiles = [
  'package.json',
  'tsconfig.json',
  'jest.config.js',
  '.eslintrc.js'
];

configFiles.forEach(configFile => {
  const exists = checkFile(configFile, 'Configuration File');
  results.files.push({ file: configFile, exists });
  if (!exists) results.overall = false;
});

console.log('\n🔧 Testing Build and Quality Commands...\n');

// Test build commands
const commands = [
  { cmd: 'npm run build', desc: 'TypeScript Build', optional: false },
  { cmd: 'npm run lint', desc: 'ESLint Check', optional: false },
  { cmd: 'npm run test', desc: 'Unit Tests', optional: false },
  { cmd: 'npm audit --audit-level=moderate', desc: 'Security Audit', optional: true },
  { cmd: 'npm outdated', desc: 'Dependency Check', optional: true }
];

commands.forEach(({ cmd, desc, optional }) => {
  const result = runCommand(cmd, desc, optional);
  results.commands.push({ command: cmd, description: desc, ...result });
  if (!result.success && !optional) {
    results.overall = false;
  }
});

console.log('\n🐳 Testing Docker Build...\n');

// Test Docker build
const dockerBuild = runCommand(
  'docker build -f Dockerfile.digitalocean -t sanad-paim-test .',
  'Docker Build Test',
  true
);
results.commands.push({ 
  command: 'docker build', 
  description: 'Docker Build Test', 
  ...dockerBuild 
});

console.log('\n📋 Validation Summary\n');

// Summary
log('='.repeat(60), 'blue');
log('CI/CD PIPELINE VALIDATION RESULTS', 'blue');
log('='.repeat(60), 'blue');

// File checks summary
log('\n📁 Configuration Files:', 'blue');
results.files.forEach(({ file, exists }) => {
  const status = exists ? '✅' : '❌';
  const color = exists ? 'green' : 'red';
  log(`  ${status} ${file}`, color);
});

// Command checks summary
log('\n🔧 Build & Test Commands:', 'blue');
results.commands.forEach(({ description, success, optional }) => {
  const status = success ? '✅' : (optional ? '⚠️' : '❌');
  const color = success ? 'green' : (optional ? 'yellow' : 'red');
  log(`  ${status} ${description}`, color);
});

// Overall result
log('\n🎯 Overall Result:', 'blue');
if (results.overall) {
  log('✅ CI/CD Pipeline is properly configured and ready!', 'green');
  log('\n🚀 Next Steps:', 'blue');
  log('  1. Ensure GitHub Secrets are configured', 'yellow');
  log('  2. Verify Digital Ocean environment variables', 'yellow');
  log('  3. Test deployment to staging environment', 'yellow');
  log('  4. Monitor first production deployment', 'yellow');
} else {
  log('❌ CI/CD Pipeline has configuration issues that need to be resolved.', 'red');
  log('\n🔧 Required Actions:', 'blue');
  
  const failedFiles = results.files.filter(f => !f.exists);
  const failedCommands = results.commands.filter(c => !c.success && !c.optional);
  
  if (failedFiles.length > 0) {
    log('  📁 Missing Files:', 'red');
    failedFiles.forEach(({ file }) => log(`    - ${file}`, 'red'));
  }
  
  if (failedCommands.length > 0) {
    log('  🔧 Failed Commands:', 'red');
    failedCommands.forEach(({ description }) => log(`    - ${description}`, 'red'));
  }
}

console.log('\n' + '='.repeat(60));

// Exit with appropriate code
process.exit(results.overall ? 0 : 1);

import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';

@Injectable()
export class LoggerService implements NestLoggerService {
  private logger: winston.Logger;

  constructor(private configService: ConfigService) {
    const logLevel = this.configService.get<string>('LOG_LEVEL', 'info');
    const logFilePath = this.configService.get<string>(
      'LOG_FILE_PATH',
      './logs',
    );
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
      ),
      defaultMeta: { service: 'paim-core' },
      transports: [
        // Console transport
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
        // File transports
        new winston.transports.File({
          filename: `${logFilePath}/error.log`,
          level: 'error',
        }),
        new winston.transports.File({
          filename: `${logFilePath}/combined.log`,
        }),
      ],
    });

    // In production, remove console logging
    if (nodeEnv === 'production') {
      this.logger.remove(this.logger.transports[0]);
    }
  }

  log(message: any, context?: string) {
    this.logger.info(message, { context });
  }

  error(message: any, trace?: string, context?: string) {
    this.logger.error(message, { trace, context });
  }

  warn(message: any, context?: string) {
    this.logger.warn(message, { context });
  }

  debug(message: any, context?: string) {
    this.logger.debug(message, { context });
  }

  verbose(message: any, context?: string) {
    this.logger.verbose(message, { context });
  }

  // Custom methods for PAIM-specific logging
  logSkillExecution(
    skillId: string,
    userId: string,
    input: string,
    output: string,
    duration: number,
  ) {
    this.logger.info('Skill executed', {
      context: 'SkillExecution',
      skillId,
      userId,
      input: input.substring(0, 100), // Truncate for privacy
      output: output.substring(0, 100),
      duration,
    });
  }

  logUserAction(
    userId: string,
    action: string,
    metadata?: Record<string, any>,
  ) {
    this.logger.info('User action', {
      context: 'UserAction',
      userId,
      action,
      metadata,
    });
  }

  logApiCall(
    endpoint: string,
    method: string,
    statusCode: number,
    duration: number,
    userId?: string,
  ) {
    this.logger.info('API call', {
      context: 'ApiCall',
      endpoint,
      method,
      statusCode,
      duration,
      userId,
    });
  }

  logMemoryOperation(
    operation: string,
    userId: string,
    memoryId?: string,
    metadata?: Record<string, any>,
  ) {
    this.logger.info('Memory operation', {
      context: 'MemoryOperation',
      operation,
      userId,
      memoryId,
      metadata,
    });
  }
}

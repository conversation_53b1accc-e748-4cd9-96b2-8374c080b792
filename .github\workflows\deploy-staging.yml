name: Deploy to Staging

on:
  push:
    branches: [ develop, staging ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  STAGING_APP_NAME: staging-sanad-paim

jobs:
  # Pre-deployment checks
  pre-deployment:
    name: Pre-deployment Checks
    runs-on: ubuntu-latest
    
    outputs:
      should-deploy: ${{ steps.check.outputs.should-deploy }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run quality checks
      id: quality
      run: |
        if npm run test:quality; then
          echo "quality=passed" >> $GITHUB_OUTPUT
        else
          echo "quality=failed" >> $GITHUB_OUTPUT
        fi
      continue-on-error: true
      
    - name: Deployment decision
      id: check
      run: |
        if [ "${{ steps.quality.outputs.quality }}" = "passed" ] || [ "${{ github.event.inputs.force_deploy }}" = "true" ]; then
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "✅ Deployment approved"
        else
          echo "should-deploy=false" >> $GITHUB_OUTPUT
          echo "❌ Deployment blocked due to quality check failures"
        fi

  # Build and push staging image
  build-staging:
    name: Build Staging Image
    runs-on: ubuntu-latest
    needs: pre-deployment
    if: needs.pre-deployment.outputs.should-deploy == 'true'
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch,suffix=-staging
          type=sha,prefix=staging-{{branch}}-
          type=raw,value=staging-latest
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.digitalocean
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64
        build-args: |
          NODE_ENV=staging

  # Deploy to Digital Ocean staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [pre-deployment, build-staging]
    if: needs.pre-deployment.outputs.should-deploy == 'true'
    
    environment:
      name: staging
      url: https://${{ env.STAGING_APP_NAME }}.ondigitalocean.app
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        
    - name: Create staging app spec
      run: |
        cat > staging-app.yaml << EOF
        name: ${{ env.STAGING_APP_NAME }}
        region: nyc
        
        services:
          - name: api
            source_dir: /
            github:
              repo: ${{ github.repository }}
              branch: ${{ github.ref_name }}
              deploy_on_push: false
            
            build_command: npm run build
            run_command: npm run start:prod
            
            environment_slug: node-js
            instance_count: 1
            instance_size_slug: basic-xxs
            
            health_check:
              http_path: /health
              initial_delay_seconds: 30
              period_seconds: 10
              timeout_seconds: 5
              success_threshold: 1
              failure_threshold: 3
            
            http_port: 3000
            
            envs:
              - key: NODE_ENV
                value: staging
              - key: PORT
                value: "3000"
              - key: LOG_LEVEL
                value: debug
              - key: TWILIO_ACCOUNT_SID
                value: \${{ secrets.TWILIO_ACCOUNT_SID }}
                type: SECRET
              - key: TWILIO_AUTH_TOKEN
                value: \${{ secrets.TWILIO_AUTH_TOKEN }}
                type: SECRET
              - key: TWILIO_WHATSAPP_NUMBER
                value: \${{ secrets.TWILIO_WHATSAPP_NUMBER }}
                type: SECRET
              - key: OPENAI_API_KEY
                value: \${{ secrets.OPENAI_API_KEY }}
                type: SECRET
              - key: APPWRITE_ENDPOINT
                value: \${{ secrets.APPWRITE_ENDPOINT }}
                type: SECRET
              - key: APPWRITE_PROJECT_ID
                value: \${{ secrets.APPWRITE_PROJECT_ID }}
                type: SECRET
              - key: APPWRITE_API_KEY
                value: \${{ secrets.APPWRITE_API_KEY }}
                type: SECRET
              - key: APPWRITE_DATABASE_ID
                value: \${{ secrets.APPWRITE_DATABASE_ID }}
                type: SECRET
        EOF
        
    - name: Deploy to staging
      run: |
        # Check if staging app exists
        if doctl apps list --format Name --no-header | grep -q "^${{ env.STAGING_APP_NAME }}$"; then
          echo "Updating existing staging app..."
          APP_ID=$(doctl apps list --format ID,Name --no-header | grep "${{ env.STAGING_APP_NAME }}" | awk '{print $1}')
          doctl apps update $APP_ID --spec staging-app.yaml --wait
        else
          echo "Creating new staging app..."
          doctl apps create --spec staging-app.yaml --wait
        fi

  # Post-deployment verification
  verify-staging:
    name: Verify Staging Deployment
    runs-on: ubuntu-latest
    needs: deploy-staging
    
    steps:
    - name: Wait for deployment
      run: sleep 60
      
    - name: Health check
      run: |
        max_attempts=10
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
          echo "Health check attempt $attempt/$max_attempts..."
          
          if curl -f -s "https://${{ env.STAGING_APP_NAME }}.ondigitalocean.app/health"; then
            echo "✅ Staging deployment healthy"
            exit 0
          fi
          
          echo "❌ Health check failed, retrying in 30 seconds..."
          sleep 30
          attempt=$((attempt + 1))
        done
        
        echo "❌ Staging deployment failed health checks"
        exit 1
        
    - name: Performance check
      run: |
        start_time=$(date +%s%N)
        curl -s "https://${{ env.STAGING_APP_NAME }}.ondigitalocean.app/health" > /dev/null
        end_time=$(date +%s%N)
        response_time=$(( (end_time - start_time) / 1000000 ))
        
        echo "Staging response time: ${response_time}ms"
        
        if [ $response_time -gt 10000 ]; then
          echo "⚠️ Slow response time detected"
        else
          echo "✅ Response time acceptable"
        fi

  # Notification
  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [pre-deployment, build-staging, deploy-staging, verify-staging]
    if: always()
    
    steps:
    - name: Success notification
      if: needs.verify-staging.result == 'success'
      run: |
        echo "🎉 Staging deployment successful!"
        echo "🔗 URL: https://${{ env.STAGING_APP_NAME }}.ondigitalocean.app"
        echo "📦 Image: ${{ needs.build-staging.outputs.image-tag }}"
        
    - name: Failure notification
      if: needs.verify-staging.result == 'failure' || needs.deploy-staging.result == 'failure'
      run: |
        echo "❌ Staging deployment failed"
        echo "Please check the logs and try again"
        exit 1

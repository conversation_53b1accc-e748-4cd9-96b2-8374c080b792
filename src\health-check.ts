#!/usr/bin/env node

/**
 * Standalone health check script for Docker containers
 * This script can be used in Docker HEALTHCHECK instructions
 */

import * as http from 'http';

const PORT = process.env.PORT || 3000;
const TIMEOUT = 5000;

function healthCheck(): Promise<void> {
  return new Promise((resolve, reject) => {
    const req = http.request(
      {
        hostname: 'localhost',
        port: PORT,
        path: '/health',
        method: 'GET',
        timeout: TIMEOUT,
      },
      (res) => {
        if (res.statusCode === 200) {
          resolve();
        } else {
          reject(
            new Error(`Health check failed with status: ${res.statusCode}`),
          );
        }
      },
    );

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Health check timeout'));
    });

    req.end();
  });
}

// Run health check
healthCheck()
  .then(() => {
    console.log('Health check passed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Health check failed:', error.message);
    process.exit(1);
  });

# 🏠 Self-Hosted Appwrite Setup Guide for Sanad

## 📋 Overview

This guide will help you set up a completely self-hosted Sanad deployment using your own Appwrite instance on Digital Ocean infrastructure.

## 🎯 Benefits of Self-Hosted

- **Complete Data Control**: Your data stays on your infrastructure
- **No Vendor Lock-in**: Full control over your backend services
- **Cost Predictability**: Fixed monthly costs instead of usage-based pricing
- **Customization**: Ability to modify and extend Appwrite as needed
- **Privacy**: Enhanced privacy and security for sensitive data

## ⚡ Quick Start (Automated)

### Prerequisites

1. **Digital Ocean Account** with API access
2. **doctl CLI** installed and authenticated
3. **Node.js 18+** and npm installed
4. **Domain name** for Appwrite (e.g., `appwrite.yourdomain.com`)

### One-Command Deployment

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Run complete self-hosted deployment
npm run deploy:selfhosted
```

This will:
1. Create a Digital Ocean droplet for Appwrite
2. Deploy and configure Appwrite with all services
3. Set up SSL certificates
4. Create database schema and collections
5. Update your application configuration
6. Deploy your application to Digital Ocean App Platform

## 🔧 Manual Setup (Step by Step)

### Step 1: Prepare Environment

```bash
# Clone the repository
git clone https://github.com/HDickenson/sanad.git
cd sanad

# Install dependencies
npm install

# Make scripts executable
chmod +x scripts/*.sh
```

### Step 2: Configure Domain

1. **Set up DNS records** for your Appwrite domain:
   ```
   A record: appwrite.yourdomain.com -> [Will be provided after droplet creation]
   A record: functions.yourdomain.com -> [Will be provided after droplet creation]
   ```

2. **Update configuration** in `scripts/setup-appwrite-selfhosted.sh`:
   ```bash
   DOMAIN="appwrite.yourdomain.com"
   ```

### Step 3: Deploy Appwrite Infrastructure

```bash
# Deploy Appwrite to Digital Ocean
./scripts/setup-appwrite-selfhosted.sh
```

This will:
- Create a Digital Ocean droplet
- Install Docker and Docker Compose
- Deploy Appwrite with all required services
- Set up SSL certificates with Let's Encrypt
- Configure security and networking

### Step 4: Set Up Database Schema

```bash
# Set environment variables
export APPWRITE_ENDPOINT="https://appwrite.yourdomain.com/v1"
export APPWRITE_PROJECT_ID="your-project-id"
export APPWRITE_API_KEY="your-api-key"

# Run schema setup
node scripts/setup-appwrite-schema.js
```

### Step 5: Update Application Configuration

```bash
# Update Appwrite endpoint in your app
export APPWRITE_ENDPOINT="https://appwrite.yourdomain.com/v1"

# Update Digital Ocean app configuration
# This is done automatically by the deployment script
```

### Step 6: Deploy Application

```bash
# Build and deploy to Digital Ocean App Platform
npm run build
npm run deploy:do
```

## 🏗️ Infrastructure Details

### Appwrite Droplet Specifications

- **Size**: 2 vCPUs, 4GB RAM (s-2vcpu-4gb)
- **Region**: NYC1 (configurable)
- **Image**: Docker 20.04 LTS
- **Storage**: 80GB SSD
- **Estimated Cost**: ~$24/month

### Services Included

- **Appwrite Core**: Main API service
- **MariaDB**: Primary database
- **Redis**: Caching and sessions
- **InfluxDB**: Usage analytics
- **ClamAV**: Antivirus scanning
- **Telegraf**: Metrics collection
- **Nginx**: Reverse proxy (via Traefik labels)

### Security Features

- **SSL/TLS**: Automatic Let's Encrypt certificates
- **Firewall**: Configured for necessary ports only
- **Access Control**: Console access restricted to whitelisted emails
- **Data Encryption**: At-rest and in-transit encryption
- **Regular Updates**: Automated security updates

## 🔐 Security Configuration

### Console Access

Edit `.env.appwrite` to configure console access:

```bash
# Whitelist emails that can access Appwrite console
_APP_CONSOLE_WHITELIST_EMAILS=<EMAIL>,<EMAIL>

# Optional: Whitelist specific IP addresses
_APP_CONSOLE_WHITELIST_IPS=*************,*********
```

### API Keys and Secrets

The setup script automatically generates secure keys:

- **OpenSSL Key**: 32-character encryption key
- **Executor Secret**: Function execution security
- **Database Passwords**: Strong random passwords
- **JWT Secrets**: Secure token signing

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Check Appwrite health
curl https://appwrite.yourdomain.com/health

# Check application health
curl https://your-app.ondigitalocean.app/health

# SSH to Appwrite server
ssh root@[droplet-ip]

# Check Docker services
docker-compose ps
```

### Backup Strategy

```bash
# Backup Appwrite data
docker-compose exec mariadb mysqldump -u root -p appwrite > backup.sql

# Backup uploaded files
tar -czf uploads-backup.tar.gz /opt/appwrite/uploads

# Backup configuration
cp .env /backup/location/
```

### Log Monitoring

```bash
# View Appwrite logs
docker-compose logs -f appwrite

# View database logs
docker-compose logs -f mariadb

# View all service logs
docker-compose logs -f
```

## 🚨 Troubleshooting

### Common Issues

1. **SSL Certificate Issues**
   ```bash
   # Renew certificates manually
   certbot renew
   docker-compose restart
   ```

2. **Database Connection Issues**
   ```bash
   # Check database status
   docker-compose exec mariadb mysql -u root -p -e "SHOW DATABASES;"
   ```

3. **Storage Issues**
   ```bash
   # Check disk usage
   df -h
   
   # Clean up Docker
   docker system prune -a
   ```

4. **Performance Issues**
   ```bash
   # Monitor resource usage
   htop
   
   # Check Docker stats
   docker stats
   ```

### Getting Help

- **Logs**: Always check Docker Compose logs first
- **Documentation**: Refer to [Appwrite Documentation](https://appwrite.io/docs)
- **Community**: Join the [Appwrite Discord](https://discord.gg/GSeTUeA)

## 🔄 Migration from Cloud

If you're migrating from Appwrite Cloud:

1. **Export Data**: Use Appwrite's export tools
2. **Deploy Self-Hosted**: Follow this guide
3. **Import Data**: Use the restore scripts
4. **Update Configuration**: Point your app to new endpoint
5. **Test Thoroughly**: Verify all functionality works

## 📈 Scaling Considerations

### Vertical Scaling

```bash
# Resize droplet
doctl compute droplet-action resize [droplet-id] --size s-4vcpu-8gb --wait
```

### Horizontal Scaling

- **Load Balancer**: Add Digital Ocean Load Balancer
- **Database Clustering**: Set up MariaDB cluster
- **Redis Clustering**: Configure Redis cluster
- **CDN**: Use Digital Ocean Spaces CDN

## 💰 Cost Breakdown

### Monthly Costs (Estimated)

- **Appwrite Droplet**: $24/month (s-2vcpu-4gb)
- **Application**: $5/month (basic-xxs)
- **Bandwidth**: $0.01/GB
- **Backups**: $1.20/month (20GB)
- **Total**: ~$30/month

### Cost Optimization

- **Droplet Sizing**: Start small, scale as needed
- **Backup Strategy**: Use Digital Ocean Spaces for cheaper storage
- **Monitoring**: Set up alerts to avoid unexpected costs

## 🎯 Next Steps

After successful deployment:

1. **Configure Monitoring**: Set up alerts and monitoring
2. **Backup Strategy**: Implement automated backups
3. **Performance Tuning**: Optimize based on usage patterns
4. **Security Hardening**: Regular security updates and audits
5. **Documentation**: Document your specific configuration

## 📞 Support

For issues specific to this setup:

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check the `/docs` directory
- **Community**: Join discussions in GitHub Discussions

---

**🎉 Congratulations!** You now have a fully self-hosted Sanad deployment with complete control over your data and infrastructure.

name: Monitoring & Health Checks

on:
  schedule:
    # Run every 15 minutes
    - cron: '*/15 * * * *'
  workflow_dispatch:

env:
  PRODUCTION_URL: 'https://sanad-paim-*.ondigitalocean.app'
  STAGING_URL: 'https://staging-sanad-paim-*.ondigitalocean.app'

jobs:
  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        environment: [production, staging]
        
    steps:
    - name: Set environment URL
      id: set-url
      run: |
        if [ "${{ matrix.environment }}" = "production" ]; then
          echo "url=${{ env.PRODUCTION_URL }}" >> $GITHUB_OUTPUT
        else
          echo "url=${{ env.STAGING_URL }}" >> $GITHUB_OUTPUT
        fi
        
    - name: Health Check - Basic
      id: health-basic
      run: |
        response=$(curl -s -o /dev/null -w "%{http_code}" "${{ steps.set-url.outputs.url }}/health" || echo "000")
        if [ "$response" = "200" ]; then
          echo "status=healthy" >> $GITHUB_OUTPUT
          echo "✅ ${{ matrix.environment }} health check passed"
        else
          echo "status=unhealthy" >> $GITHUB_OUTPUT
          echo "❌ ${{ matrix.environment }} health check failed (HTTP $response)"
        fi
        
    - name: Health Check - Detailed
      if: steps.health-basic.outputs.status == 'healthy'
      run: |
        response=$(curl -s "${{ steps.set-url.outputs.url }}/health/detailed")
        echo "Detailed health check response:"
        echo "$response" | jq '.' || echo "$response"
        
    - name: Performance Check
      if: steps.health-basic.outputs.status == 'healthy'
      run: |
        start_time=$(date +%s%N)
        curl -s "${{ steps.set-url.outputs.url }}/health" > /dev/null
        end_time=$(date +%s%N)
        response_time=$(( (end_time - start_time) / 1000000 ))
        
        echo "Response time: ${response_time}ms"
        
        if [ $response_time -gt 5000 ]; then
          echo "⚠️ Slow response time detected: ${response_time}ms"
        else
          echo "✅ Response time acceptable: ${response_time}ms"
        fi
        
    - name: Create Issue on Failure
      if: steps.health-basic.outputs.status == 'unhealthy'
      uses: actions/github-script@v6
      with:
        script: |
          const title = `🚨 Health Check Failed - ${{ matrix.environment }}`;
          const body = `
          ## Health Check Failure
          
          **Environment:** ${{ matrix.environment }}
          **URL:** ${{ steps.set-url.outputs.url }}
          **Time:** ${new Date().toISOString()}
          **Workflow:** ${{ github.workflow }}
          **Run:** ${{ github.run_id }}
          
          ### Details
          The health check for the ${{ matrix.environment }} environment has failed.
          
          ### Next Steps
          1. Check the application logs
          2. Verify the deployment status
          3. Check Digital Ocean App Platform status
          4. Review recent changes
          
          This issue was created automatically by the monitoring workflow.
          `;
          
          // Check if there's already an open issue for this environment
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open',
            labels: ['health-check-failure', `environment:${{ matrix.environment }}`]
          });
          
          if (issues.data.length === 0) {
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['health-check-failure', `environment:${{ matrix.environment }}`, 'priority:high']
            });
          }

  ssl-certificate-check:
    name: SSL Certificate Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Check SSL Certificate
      run: |
        # Extract domain from URL (remove protocol and path)
        domain=$(echo "${{ env.PRODUCTION_URL }}" | sed 's|https\?://||' | sed 's|/.*||')
        
        # Check SSL certificate expiration
        expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -enddate | cut -d= -f2)
        expiry_timestamp=$(date -d "$expiry_date" +%s)
        current_timestamp=$(date +%s)
        days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        echo "SSL Certificate expires on: $expiry_date"
        echo "Days until expiry: $days_until_expiry"
        
        if [ $days_until_expiry -lt 30 ]; then
          echo "⚠️ SSL certificate expires in $days_until_expiry days"
          echo "ssl_warning=true" >> $GITHUB_ENV
        else
          echo "✅ SSL certificate is valid for $days_until_expiry days"
        fi
        
    - name: Create SSL Warning Issue
      if: env.ssl_warning == 'true'
      uses: actions/github-script@v6
      with:
        script: |
          const title = `⚠️ SSL Certificate Expiring Soon`;
          const body = `
          ## SSL Certificate Warning
          
          The SSL certificate for the production environment is expiring soon.
          
          **Domain:** Production domain
          **Days until expiry:** Less than 30 days
          **Check date:** ${new Date().toISOString()}
          
          ### Action Required
          Please renew the SSL certificate before it expires to avoid service disruption.
          
          This issue was created automatically by the monitoring workflow.
          `;
          
          // Check if there's already an open SSL warning issue
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open',
            labels: ['ssl-certificate', 'warning']
          });
          
          if (issues.data.length === 0) {
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['ssl-certificate', 'warning', 'priority:medium']
            });
          }

  dependency-security-check:
    name: Dependency Security Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      id: audit
      run: |
        if npm audit --audit-level=high; then
          echo "status=clean" >> $GITHUB_OUTPUT
          echo "✅ No high-severity vulnerabilities found"
        else
          echo "status=vulnerabilities" >> $GITHUB_OUTPUT
          echo "❌ High-severity vulnerabilities detected"
        fi
        
    - name: Create Security Issue
      if: steps.audit.outputs.status == 'vulnerabilities'
      uses: actions/github-script@v6
      with:
        script: |
          const title = `🔒 Security Vulnerabilities Detected`;
          const body = `
          ## Security Alert
          
          High-severity security vulnerabilities have been detected in the project dependencies.
          
          **Detection time:** ${new Date().toISOString()}
          **Workflow:** ${{ github.workflow }}
          **Run:** ${{ github.run_id }}
          
          ### Action Required
          1. Run \`npm audit\` locally to see detailed vulnerability information
          2. Update vulnerable packages using \`npm audit fix\`
          3. Test the application after updates
          4. Create a PR with the security fixes
          
          This issue was created automatically by the monitoring workflow.
          `;
          
          // Check if there's already an open security issue
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open',
            labels: ['security', 'vulnerabilities']
          });
          
          if (issues.data.length === 0) {
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['security', 'vulnerabilities', 'priority:critical']
            });
          }

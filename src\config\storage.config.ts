import { registerAs } from '@nestjs/config';

export default registerAs('storage', () => ({
  // Local storage configuration
  localPath: process.env.STORAGE_PATH || './storage',
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 10485760, // 10MB

  // Digital Ocean Spaces configuration (S3-compatible)
  digitalOcean: {
    accessKeyId: process.env.DO_SPACES_ACCESS_KEY_ID,
    secretAccessKey: process.env.DO_SPACES_SECRET_ACCESS_KEY,
    endpoint: process.env.DO_SPACES_ENDPOINT, // e.g., 'https://nyc3.digitaloceanspaces.com'
    region: process.env.DO_SPACES_REGION || 'nyc3',
    bucket: process.env.DO_SPACES_BUCKET,
    cdnEndpoint: process.env.DO_SPACES_CDN_ENDPOINT, // Optional CDN endpoint
  },

  // Storage provider selection
  provider: process.env.STORAGE_PROVIDER || 'local', // 'local' or 'digitalocean'
}));

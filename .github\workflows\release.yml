name: Release Management

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    
    outputs:
      release-id: ${{ steps.create-release.outputs.id }}
      upload-url: ${{ steps.create-release.outputs.upload_url }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run full test suite
      run: |
        npm run test:cov
        npm run test:e2e
        npm run build
        
    - name: Generate changelog
      id: changelog
      run: |
        echo "## What's Changed" > CHANGELOG.md
        git log --pretty=format:"- %s (%h)" $(git describe --tags --abbrev=0 HEAD^)..HEAD >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "**Full Changelog**: https://github.com/${{ github.repository }}/compare/$(git describe --tags --abbrev=0 HEAD^)...${GITHUB_REF#refs/tags/}" >> CHANGELOG.md
        
    - name: Create Release
      id: create-release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref_name || github.event.inputs.version }}
        release_name: Release ${{ github.ref_name || github.event.inputs.version }}
        body_path: CHANGELOG.md
        draft: false
        prerelease: ${{ contains(github.ref_name || github.event.inputs.version, 'beta') || contains(github.ref_name || github.event.inputs.version, 'alpha') }}

  build-release-assets:
    name: Build Release Assets
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Create release archive
      run: |
        tar -czf sanad-paim-${{ github.ref_name || github.event.inputs.version }}.tar.gz \
          dist/ \
          package.json \
          package-lock.json \
          README.md \
          docs/ \
          Dockerfile* \
          docker-compose*.yml \
          .do/
          
    - name: Upload release archive
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload-url }}
        asset_path: ./sanad-paim-${{ github.ref_name || github.event.inputs.version }}.tar.gz
        asset_name: sanad-paim-${{ github.ref_name || github.event.inputs.version }}.tar.gz
        asset_content_type: application/gzip

  docker-release:
    name: Docker Release
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/${{ github.repository }}
        tags: |
          type=ref,event=tag
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.digitalocean
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  deployment-notification:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [create-release, build-release-assets, docker-release]
    if: always()
    
    steps:
    - name: Success Notification
      if: needs.create-release.result == 'success' && needs.build-release-assets.result == 'success' && needs.docker-release.result == 'success'
      run: |
        echo "🎉 Release ${{ github.ref_name || github.event.inputs.version }} created successfully!"
        echo "📦 Release assets and Docker images are available"
        echo "🚀 Digital Ocean will auto-deploy the latest changes"
        
    - name: Failure Notification
      if: needs.create-release.result == 'failure' || needs.build-release-assets.result == 'failure' || needs.docker-release.result == 'failure'
      run: |
        echo "❌ Release creation failed"
        exit 1

version: '3.8'

# Digital Ocean App Platform Local Development Environment
# This mimics the Digital Ocean App Platform environment for local testing

services:
  sanad-app:
    build:
      context: .
      dockerfile: Dockerfile.digitalocean
      target: production
    container_name: sanad-do-local
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    env_file:
      - .env.digitalocean
    volumes:
      # Mount logs and storage for local development
      - ./logs:/app/logs
      - ./storage:/app/storage
    networks:
      - sanad-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis-cache
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.sanad.rule=Host(`localhost`)"
      - "traefik.http.services.sanad.loadbalancer.server.port=3000"

  # Redis for caching (optional, mimics managed Redis on DO)
  redis-cache:
    image: redis:7-alpine
    container_name: sanad-redis-local
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - sanad-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx for reverse proxy (optional, mimics DO's load balancer)
  nginx-proxy:
    image: nginx:alpine
    container_name: sanad-nginx-local
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.do.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - sanad-app
    networks:
      - sanad-network

volumes:
  redis_data:
    driver: local

networks:
  sanad-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

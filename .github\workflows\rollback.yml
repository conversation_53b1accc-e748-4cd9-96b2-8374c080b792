name: Emergency Rollback

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
          - production
          - staging
      reason:
        description: 'Reason for rollback'
        required: true
        type: string
      target_version:
        description: 'Target version to rollback to (optional)'
        required: false
        type: string

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Rollback validation
  validate-rollback:
    name: Validate Rollback Request
    runs-on: ubuntu-latest
    
    outputs:
      app-name: ${{ steps.config.outputs.app-name }}
      can-rollback: ${{ steps.validation.outputs.can-rollback }}
      
    steps:
    - name: Set environment configuration
      id: config
      run: |
        if [ "${{ github.event.inputs.environment }}" = "production" ]; then
          echo "app-name=sanad-paim" >> $GITHUB_OUTPUT
        else
          echo "app-name=staging-sanad-paim" >> $GITHUB_OUTPUT
        fi
        
    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        
    - name: Validate rollback capability
      id: validation
      run: |
        APP_NAME="${{ steps.config.outputs.app-name }}"
        
        # Check if app exists
        if ! doctl apps list --format Name --no-header | grep -q "^$APP_NAME$"; then
          echo "❌ App $APP_NAME not found"
          echo "can-rollback=false" >> $GITHUB_OUTPUT
          exit 1
        fi
        
        # Get app ID and check deployments
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$APP_NAME" | awk '{print $1}')
        DEPLOYMENT_COUNT=$(doctl apps list-deployments $APP_ID --format ID --no-header | wc -l)
        
        if [ $DEPLOYMENT_COUNT -lt 2 ]; then
          echo "❌ Not enough deployments for rollback (found: $DEPLOYMENT_COUNT)"
          echo "can-rollback=false" >> $GITHUB_OUTPUT
          exit 1
        fi
        
        echo "✅ Rollback validation passed"
        echo "can-rollback=true" >> $GITHUB_OUTPUT
        echo "App ID: $APP_ID"
        echo "Available deployments: $DEPLOYMENT_COUNT"

  # Emergency approval for production
  emergency-approval:
    name: Emergency Approval
    runs-on: ubuntu-latest
    needs: validate-rollback
    if: github.event.inputs.environment == 'production' && needs.validate-rollback.outputs.can-rollback == 'true'
    
    environment:
      name: emergency-rollback
      
    steps:
    - name: Emergency approval checkpoint
      run: |
        echo "🚨 EMERGENCY ROLLBACK REQUEST"
        echo "Environment: ${{ github.event.inputs.environment }}"
        echo "Reason: ${{ github.event.inputs.reason }}"
        echo "Target version: ${{ github.event.inputs.target_version || 'Previous deployment' }}"
        echo ""
        echo "⚠️ This action will rollback the production environment"
        echo "Please ensure this is necessary and approved by the team"

  # Backup current state
  backup-current-state:
    name: Backup Current State
    runs-on: ubuntu-latest
    needs: [validate-rollback, emergency-approval]
    if: always() && needs.validate-rollback.outputs.can-rollback == 'true' && (needs.emergency-approval.result == 'success' || github.event.inputs.environment == 'staging')
    
    outputs:
      backup-created: ${{ steps.backup.outputs.created }}
      
    steps:
    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        
    - name: Create backup
      id: backup
      run: |
        APP_NAME="${{ needs.validate-rollback.outputs.app-name }}"
        timestamp=$(date +%Y%m%d_%H%M%S)
        
        # Get current app spec
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$APP_NAME" | awk '{print $1}')
        
        # Create backup directory
        mkdir -p backups
        
        # Backup app spec
        doctl apps spec get $APP_ID > "backups/rollback_backup_${timestamp}.yaml"
        
        # Backup current deployment info
        doctl apps list-deployments $APP_ID --format ID,CreatedAt,UpdatedAt > "backups/deployments_${timestamp}.txt"
        
        echo "created=true" >> $GITHUB_OUTPUT
        echo "✅ Backup created: rollback_backup_${timestamp}.yaml"
        
    - name: Upload backup artifacts
      uses: actions/upload-artifact@v3
      with:
        name: rollback-backup-${{ github.event.inputs.environment }}
        path: backups/
        retention-days: 30

  # Perform rollback
  execute-rollback:
    name: Execute Rollback
    runs-on: ubuntu-latest
    needs: [validate-rollback, backup-current-state]
    if: needs.backup-current-state.outputs.backup-created == 'true'
    
    steps:
    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        
    - name: Execute rollback
      run: |
        APP_NAME="${{ needs.validate-rollback.outputs.app-name }}"
        TARGET_VERSION="${{ github.event.inputs.target_version }}"
        
        # Get app ID
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "$APP_NAME" | awk '{print $1}')
        
        echo "🔄 Starting rollback for $APP_NAME..."
        
        if [ -n "$TARGET_VERSION" ]; then
          echo "Rolling back to specific version: $TARGET_VERSION"
          # For specific version rollback, we would need to trigger a deployment
          # with the specific git tag/commit
          echo "⚠️ Specific version rollback requires manual intervention"
          echo "Please manually deploy version $TARGET_VERSION through Digital Ocean console"
        else
          echo "Rolling back to previous deployment..."
          
          # Get the second most recent deployment (previous one)
          PREV_DEPLOYMENT=$(doctl apps list-deployments $APP_ID --format ID --no-header | sed -n '2p')
          
          if [ -n "$PREV_DEPLOYMENT" ]; then
            echo "Previous deployment ID: $PREV_DEPLOYMENT"
            echo "⚠️ Digital Ocean CLI doesn't support direct rollback"
            echo "Manual intervention required through Digital Ocean console"
            echo "Or trigger a new deployment with the previous git commit"
          else
            echo "❌ No previous deployment found"
            exit 1
          fi
        fi
        
    - name: Alternative rollback approach
      run: |
        echo "🔄 Attempting alternative rollback approach..."
        echo "This will trigger a redeployment of the previous commit"
        
        # For now, we'll document the manual steps needed
        echo "Manual rollback steps:"
        echo "1. Go to Digital Ocean Apps console"
        echo "2. Select the app: ${{ needs.validate-rollback.outputs.app-name }}"
        echo "3. Go to Settings > App Spec"
        echo "4. Change the branch/commit to the previous version"
        echo "5. Save and redeploy"

  # Verify rollback
  verify-rollback:
    name: Verify Rollback
    runs-on: ubuntu-latest
    needs: [validate-rollback, execute-rollback]
    
    steps:
    - name: Wait for rollback deployment
      run: |
        echo "Waiting for rollback deployment to complete..."
        sleep 120
        
    - name: Health check after rollback
      run: |
        APP_NAME="${{ needs.validate-rollback.outputs.app-name }}"
        
        if [ "${{ github.event.inputs.environment }}" = "production" ]; then
          URL="https://sanad-paim.ondigitalocean.app"
        else
          URL="https://staging-sanad-paim.ondigitalocean.app"
        fi
        
        max_attempts=10
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
          echo "Health check attempt $attempt/$max_attempts..."
          
          if curl -f -s "$URL/health"; then
            echo "✅ Rollback successful - application is healthy"
            exit 0
          fi
          
          echo "❌ Health check failed, retrying in 30 seconds..."
          sleep 30
          attempt=$((attempt + 1))
        done
        
        echo "❌ Rollback verification failed"
        exit 1
        
    - name: Performance check after rollback
      run: |
        if [ "${{ github.event.inputs.environment }}" = "production" ]; then
          URL="https://sanad-paim.ondigitalocean.app"
        else
          URL="https://staging-sanad-paim.ondigitalocean.app"
        fi
        
        start_time=$(date +%s%N)
        curl -s "$URL/health" > /dev/null
        end_time=$(date +%s%N)
        response_time=$(( (end_time - start_time) / 1000000 ))
        
        echo "Response time after rollback: ${response_time}ms"
        
        if [ $response_time -gt 5000 ]; then
          echo "⚠️ Slow response time detected after rollback"
        else
          echo "✅ Response time acceptable after rollback"
        fi

  # Notification and documentation
  notify-rollback:
    name: Rollback Notification
    runs-on: ubuntu-latest
    needs: [validate-rollback, execute-rollback, verify-rollback]
    if: always()
    
    steps:
    - name: Success notification
      if: needs.verify-rollback.result == 'success'
      run: |
        echo "✅ ROLLBACK COMPLETED SUCCESSFULLY"
        echo "Environment: ${{ github.event.inputs.environment }}"
        echo "Reason: ${{ github.event.inputs.reason }}"
        echo "Target version: ${{ github.event.inputs.target_version || 'Previous deployment' }}"
        echo "Completed at: $(date)"
        
    - name: Failure notification
      if: needs.verify-rollback.result == 'failure' || needs.execute-rollback.result == 'failure'
      run: |
        echo "❌ ROLLBACK FAILED"
        echo "Environment: ${{ github.event.inputs.environment }}"
        echo "Reason: ${{ github.event.inputs.reason }}"
        echo "Manual intervention required"
        echo "Check the backup artifacts and logs for recovery"
        exit 1
        
    - name: Create rollback issue
      if: always()
      uses: actions/github-script@v6
      with:
        script: |
          const title = `🔄 Rollback Executed - ${{ github.event.inputs.environment }}`;
          const status = '${{ needs.verify-rollback.result }}' === 'success' ? 'SUCCESS' : 'FAILED';
          const body = `
          ## Rollback Report
          
          **Status:** ${status}
          **Environment:** ${{ github.event.inputs.environment }}
          **Reason:** ${{ github.event.inputs.reason }}
          **Target Version:** ${{ github.event.inputs.target_version || 'Previous deployment' }}
          **Executed by:** ${{ github.actor }}
          **Timestamp:** ${new Date().toISOString()}
          **Workflow Run:** ${{ github.run_id }}
          
          ### Next Steps
          ${status === 'SUCCESS' ? 
            '- Monitor the application for stability\n- Investigate the root cause of the issue that required rollback\n- Plan a fix and new deployment' :
            '- Manual intervention required\n- Check backup artifacts\n- Contact DevOps team for assistance'
          }
          
          This issue was created automatically by the rollback workflow.
          `;
          
          await github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['rollback', `environment:${{ github.event.inputs.environment }}`, status === 'SUCCESS' ? 'resolved' : 'urgent']
          });

groups:
  # PAIM Application Alerts
  - name: paim_application_alerts
    rules:
      # Application availability
      - alert: PAIMApplicationDown
        expr: up{job="paim-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: paim-core
          team: backend
        annotations:
          summary: "PAIM application is down"
          description: "PAIM application has been down for more than 1 minute"
          runbook_url: "https://docs.paim.local/runbooks/application-down"

      # High memory usage
      - alert: PAIMHighMemoryUsage
        expr: (process_resident_memory_bytes{job="paim-app"} / 1024 / 1024) > 512
        for: 5m
        labels:
          severity: warning
          service: paim-core
          team: backend
        annotations:
          summary: "PAIM application high memory usage"
          description: "PAIM application is using {{ $value }}MB of memory (threshold: 512MB)"
          runbook_url: "https://docs.paim.local/runbooks/high-memory"

      # Critical memory usage
      - alert: PAIMCriticalMemoryUsage
        expr: (process_resident_memory_bytes{job="paim-app"} / 1024 / 1024) > 768
        for: 2m
        labels:
          severity: critical
          service: paim-core
          team: backend
        annotations:
          summary: "PAIM application critical memory usage"
          description: "PAIM application is using {{ $value }}MB of memory (critical threshold: 768MB)"
          runbook_url: "https://docs.paim.local/runbooks/critical-memory"

      # High CPU usage
      - alert: PAIMHighCPUUsage
        expr: rate(process_cpu_seconds_total{job="paim-app"}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: paim-core
          team: backend
        annotations:
          summary: "PAIM application high CPU usage"
          description: "PAIM application CPU usage is {{ $value }}% (threshold: 80%)"
          runbook_url: "https://docs.paim.local/runbooks/high-cpu"

      # Critical CPU usage
      - alert: PAIMCriticalCPUUsage
        expr: rate(process_cpu_seconds_total{job="paim-app"}[5m]) * 100 > 95
        for: 2m
        labels:
          severity: critical
          service: paim-core
          team: backend
        annotations:
          summary: "PAIM application critical CPU usage"
          description: "PAIM application CPU usage is {{ $value }}% (critical threshold: 95%)"
          runbook_url: "https://docs.paim.local/runbooks/critical-cpu"

  # HTTP Performance Alerts
  - name: paim_http_alerts
    rules:
      # High error rate
      - alert: PAIMHighErrorRate
        expr: (rate(http_requests_total{job="paim-app",status=~"5.."}[5m]) / rate(http_requests_total{job="paim-app"}[5m])) * 100 > 5
        for: 5m
        labels:
          severity: warning
          service: paim-core
          team: backend
        annotations:
          summary: "High HTTP error rate detected"
          description: "HTTP error rate is {{ $value }}% (threshold: 5%)"
          runbook_url: "https://docs.paim.local/runbooks/high-error-rate"

      # Critical error rate
      - alert: PAIMCriticalErrorRate
        expr: (rate(http_requests_total{job="paim-app",status=~"5.."}[5m]) / rate(http_requests_total{job="paim-app"}[5m])) * 100 > 15
        for: 2m
        labels:
          severity: critical
          service: paim-core
          team: backend
        annotations:
          summary: "Critical HTTP error rate detected"
          description: "HTTP error rate is {{ $value }}% (critical threshold: 15%)"
          runbook_url: "https://docs.paim.local/runbooks/critical-error-rate"

      # High response time
      - alert: PAIMHighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_ms_bucket{job="paim-app"}[5m])) > 1000
        for: 5m
        labels:
          severity: warning
          service: paim-core
          team: backend
        annotations:
          summary: "High HTTP response time detected"
          description: "95th percentile response time is {{ $value }}ms (threshold: 1000ms)"
          runbook_url: "https://docs.paim.local/runbooks/high-response-time"

      # Critical response time
      - alert: PAIMCriticalResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_ms_bucket{job="paim-app"}[5m])) > 3000
        for: 2m
        labels:
          severity: critical
          service: paim-core
          team: backend
        annotations:
          summary: "Critical HTTP response time detected"
          description: "95th percentile response time is {{ $value }}ms (critical threshold: 3000ms)"
          runbook_url: "https://docs.paim.local/runbooks/critical-response-time"

      # Low throughput
      - alert: PAIMLowThroughput
        expr: rate(http_requests_total{job="paim-app"}[5m]) < 1
        for: 10m
        labels:
          severity: warning
          service: paim-core
          team: backend
        annotations:
          summary: "Low HTTP throughput detected"
          description: "HTTP request rate is {{ $value }} requests/second (threshold: 1 req/s)"
          runbook_url: "https://docs.paim.local/runbooks/low-throughput"

  # Infrastructure Alerts
  - name: paim_infrastructure_alerts
    rules:
      # Redis connection issues
      - alert: PAIMRedisDown
        expr: up{job="redis-exporter"} == 0
        for: 2m
        labels:
          severity: critical
          service: redis
          team: infrastructure
        annotations:
          summary: "Redis is down"
          description: "Redis instance is not responding"
          runbook_url: "https://docs.paim.local/runbooks/redis-down"

      # Redis high memory usage
      - alert: PAIMRedisHighMemory
        expr: (redis_memory_used_bytes / redis_memory_max_bytes) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: redis
          team: infrastructure
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value }}% (threshold: 80%)"
          runbook_url: "https://docs.paim.local/runbooks/redis-high-memory"

      # Redis connection pool exhaustion
      - alert: PAIMRedisConnectionPoolExhaustion
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
          team: infrastructure
        annotations:
          summary: "Redis connection pool near exhaustion"
          description: "Redis has {{ $value }} connected clients (threshold: 100)"
          runbook_url: "https://docs.paim.local/runbooks/redis-connections"

      # Appwrite connection issues
      - alert: PAIMAppwriteConnectionIssues
        expr: increase(appwrite_connection_errors_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: appwrite
          team: backend
        annotations:
          summary: "Appwrite connection issues"
          description: "{{ $value }} Appwrite connection errors in the last 5 minutes"
          runbook_url: "https://docs.paim.local/runbooks/appwrite-connection"

      # Disk space usage
      - alert: PAIMHighDiskUsage
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 20
        for: 5m
        labels:
          severity: warning
          service: system
          team: infrastructure
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is {{ $value }}% (threshold: 80%)"
          runbook_url: "https://docs.paim.local/runbooks/high-disk-usage"

      # Critical disk space
      - alert: PAIMCriticalDiskUsage
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 2m
        labels:
          severity: critical
          service: system
          team: infrastructure
        annotations:
          summary: "Critical disk usage detected"
          description: "Disk usage is {{ $value }}% (critical threshold: 90%)"
          runbook_url: "https://docs.paim.local/runbooks/critical-disk-usage"

  # Business Logic Alerts
  - name: paim_business_alerts
    rules:
      # High WhatsApp message failure rate
      - alert: PAIMHighWhatsAppFailureRate
        expr: (rate(whatsapp_messages_failed_total[5m]) / rate(whatsapp_messages_total[5m])) * 100 > 10
        for: 5m
        labels:
          severity: warning
          service: whatsapp
          team: backend
        annotations:
          summary: "High WhatsApp message failure rate"
          description: "WhatsApp message failure rate is {{ $value }}% (threshold: 10%)"
          runbook_url: "https://docs.paim.local/runbooks/whatsapp-failures"

      # OpenAI API rate limiting
      - alert: PAIMOpenAIRateLimited
        expr: increase(openai_rate_limit_errors_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: openai
          team: backend
        annotations:
          summary: "OpenAI API rate limiting detected"
          description: "{{ $value }} OpenAI rate limit errors in the last 5 minutes"
          runbook_url: "https://docs.paim.local/runbooks/openai-rate-limit"

      # User registration anomalies
      - alert: PAIMUnusualRegistrationActivity
        expr: rate(user_registrations_total[1h]) > 100
        for: 10m
        labels:
          severity: info
          service: registration
          team: backend
        annotations:
          summary: "Unusual user registration activity"
          description: "{{ $value }} user registrations per hour (threshold: 100)"
          runbook_url: "https://docs.paim.local/runbooks/unusual-registrations"

      # Low user activity
      - alert: PAIMLowUserActivity
        expr: rate(user_messages_total[1h]) < 10
        for: 30m
        labels:
          severity: info
          service: user-activity
          team: product
        annotations:
          summary: "Low user activity detected"
          description: "Only {{ $value }} user messages per hour (threshold: 10)"
          runbook_url: "https://docs.paim.local/runbooks/low-user-activity"

  # Security Alerts
  - name: paim_security_alerts
    rules:
      # Multiple failed authentication attempts
      - alert: PAIMMultipleFailedAuth
        expr: increase(auth_failures_total[5m]) > 20
        for: 2m
        labels:
          severity: warning
          service: authentication
          team: security
        annotations:
          summary: "Multiple failed authentication attempts"
          description: "{{ $value }} failed authentication attempts in the last 5 minutes"
          runbook_url: "https://docs.paim.local/runbooks/failed-auth"

      # Suspicious IP activity
      - alert: PAIMSuspiciousIPActivity
        expr: increase(suspicious_ip_requests_total[10m]) > 50
        for: 5m
        labels:
          severity: warning
          service: security
          team: security
        annotations:
          summary: "Suspicious IP activity detected"
          description: "{{ $value }} requests from suspicious IPs in the last 10 minutes"
          runbook_url: "https://docs.paim.local/runbooks/suspicious-ip"

      # Rate limit violations
      - alert: PAIMRateLimitViolations
        expr: increase(rate_limit_violations_total[5m]) > 100
        for: 5m
        labels:
          severity: info
          service: rate-limiting
          team: security
        annotations:
          summary: "High rate limit violations"
          description: "{{ $value }} rate limit violations in the last 5 minutes"
          runbook_url: "https://docs.paim.local/runbooks/rate-limit-violations"

      # Redis connection issues
      - alert: RedisConnectionIssues
        expr: up{job="redis-exporter"} == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Redis connection issues"
          description: "Cannot connect to Redis cache"

      # Disk space alert
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space"
          description: "Disk space is below 10%: {{ $value }}% available"

      # High response time alert
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time"
          description: "95th percentile response time is {{ $value }}s"

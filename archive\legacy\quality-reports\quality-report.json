{"timestamp": "2025-06-30T09:14:58.497Z", "summary": {"total": 7, "passed": 1, "failed": 6}, "results": [{"name": "TypeScript Compilation", "description": "Checking TypeScript compilation", "command": "npm run build", "success": false, "error": "Command failed: npm run build\n\u001b[96msrc/cache/cache.interceptor.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'target' is declared but its value is never read.\r\n\r\n\u001b[7m29\u001b[0m   return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {\r\n\u001b[7m  \u001b[0m \u001b[91m          ~~~~~~\u001b[0m\r\n\u001b[96msrc/cache/cache.interceptor.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'propertyKey' is declared but its value is never read.\r\n\r\n\u001b[7m29\u001b[0m   return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/cache/cache.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m68\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType 'string' is not assignable to type 'T'.\r\n  'T' could be instantiated with an arbitrary type which could be unrelated to 'string'.\r\n\r\n\u001b[7m71\u001b[0m           return options.serialize !== false ? JSON.parse(value) : value;\r\n\u001b[7m  \u001b[0m \u001b[91m                                                                   ~~~~~\u001b[0m\r\n\u001b[96msrc/cache/redis.service.ts\u001b[0m:\u001b[93m14\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6138: \u001b[0mProperty 'redisOptions' is declared but its value is never read.\r\n\r\n\u001b[7m14\u001b[0m     @Inject('REDIS_OPTIONS') private readonly redisOptions: any,\r\n\u001b[7m  \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/cache/redis.service.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m22\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6138: \u001b[0mProperty 'configService' is declared but its value is never read.\r\n\r\n\u001b[7m15\u001b[0m     private readonly configService: ConfigService,\r\n\u001b[7m  \u001b[0m \u001b[91m                     ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/cache/redis.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'Redis'.\r\n\r\n\u001b[7m71\u001b[0m   getClient(): Redis {\r\n\u001b[7m  \u001b[0m \u001b[91m               ~~~~~\u001b[0m\r\n\u001b[96msrc/skills/services/memory-appwrite.service.ts\u001b[0m:\u001b[93m164\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'mapDocumentToReminder' is declared but its value is never read.\r\n\r\n\u001b[7m164\u001b[0m   private mapDocumentToReminder(document: any): IReminder {\r\n\u001b[7m   \u001b[0m \u001b[91m          ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n", "output": "\n> paim-core@0.1.0 prebuild\n> npm run clean\n\n\n> paim-core@0.1.0 clean\n> rimraf dist\n\n\n> paim-core@0.1.0 build\n> nest build\n\nFound 7 error(s).\r\n\n", "timestamp": "2025-06-30T09:14:24.874Z"}, {"name": "ESLint", "description": "Running ESLint code analysis", "command": "npm run lint", "success": false, "error": "Command failed: npm run lint\n\nOops! Something went wrong! :(\n\nESLint: 8.57.1\n\nESLint couldn't find the config \"@typescript-eslint/recommended\" to extend from. Please check that the name of the config is correct.\n\nThe config \"@typescript-eslint/recommended\" was referenced from the config file in \"D:\\Projects\\sanad\\.eslintrc.js\".\n\nIf you still have problems, please stop by https://eslint.org/chat/help to chat with the team.\n\n", "output": "\n> paim-core@0.1.0 lint\n> eslint \"{src,apps,libs,test}/**/*.ts\" --fix\n\n", "timestamp": "2025-06-30T09:14:32.568Z"}, {"name": "Unit Tests", "description": "Running unit tests", "command": "npm run test", "success": false, "error": "Command failed: npm run test\n● Multiple configurations found:\n\n    * D:/Projects/sanad/jest.config.js\n    * `jest` key in D:/Projects/sanad/package.json\n\n  Implicit config resolution does not allow multiple configuration files.\n  Either remove unused config files or select one explicitly with `--config`.\n\n  Configuration Documentation:\n  https://jestjs.io/docs/configuration\n\n", "output": "\n> paim-core@0.1.0 test\n> jest\n\n", "timestamp": "2025-06-30T09:14:33.400Z"}, {"name": "Test Coverage", "description": "Generating test coverage report", "command": "npm run test:cov", "success": false, "error": "Command failed: npm run test:cov\n● Multiple configurations found:\n\n    * D:/Projects/sanad/jest.config.js\n    * `jest` key in D:/Projects/sanad/package.json\n\n  Implicit config resolution does not allow multiple configuration files.\n  Either remove unused config files or select one explicitly with `--config`.\n\n  Configuration Documentation:\n  https://jestjs.io/docs/configuration\n\n", "output": "\n> paim-core@0.1.0 test:cov\n> jest --coverage\n\n", "timestamp": "2025-06-30T09:14:34.175Z"}, {"name": "E2E Tests", "description": "Running end-to-end tests", "command": "npm run test:e2e", "success": false, "error": "Command failed: npm run test:e2e\n● Validation Warning:\n\n  Unknown option \"moduleNameMapping\" with value {\"^@/(.*)$\": \"<rootDir>/../src/$1\"} was found.\n  This is probably a typing mistake. Fixing it will remove this message.\n\n  Configuration Documentation:\n  https://jestjs.io/docs/configuration\n\n● Validation Warning:\n\n  Unknown option \"moduleNameMapping\" with value {\"^@/(.*)$\": \"<rootDir>/../src/$1\"} was found.\n  This is probably a typing mistake. Fixing it will remove this message.\n\n  Configuration Documentation:\n  https://jestjs.io/docs/configuration\n\nFAIL test/webhook.e2e-spec.ts\n  ● Test suite failed to run\n\n    \u001b[96msrc/skills/services/memory-appwrite.service.ts\u001b[0m:\u001b[93m164\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'mapDocumentToReminder' is declared but its value is never read.\n\n    \u001b[7m164\u001b[0m   private mapDocumentToReminder(document: any): IReminder {\n    \u001b[7m   \u001b[0m \u001b[91m          ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\nTest Suites: 1 failed, 1 total\nTests:       0 total\nSnapshots:   0 total\nTime:        18.161 s\nRan all test suites.\n", "output": "\n> paim-core@0.1.0 test:e2e\n> jest --config ./test/jest-e2e.json\n\n", "timestamp": "2025-06-30T09:14:54.079Z"}, {"name": "Security Audit", "description": "Running npm security audit", "command": "npm audit --audit-level=moderate", "success": true, "output": "found 0 vulnerabilities\n", "timestamp": "2025-06-30T09:14:55.827Z"}, {"name": "Dependency Check", "description": "Checking for outdated dependencies", "command": "npm outdated", "success": false, "error": "Command failed: npm outdated", "output": "Package                           Current   Wanted  Latest  Location                                       Depended by\n@nestjs/cli                        10.4.9   10.4.9  11.0.7  node_modules/@nestjs/cli                       sanad\n@nestjs/common                    10.4.19  10.4.19  11.1.3  node_modules/@nestjs/common                    sanad\n@nestjs/config                      3.3.0    3.3.0   4.0.2  node_modules/@nestjs/config                    sanad\n@nestjs/core                      10.4.19  10.4.19  11.1.3  node_modules/@nestjs/core                      sanad\n@nestjs/platform-express          10.4.19  10.4.19  11.1.3  node_modules/@nestjs/platform-express          sanad\n@nestjs/schedule                    4.1.2    4.1.2   6.0.0  node_modules/@nestjs/schedule                  sanad\n@nestjs/schematics                 10.2.3   10.2.3  11.0.5  node_modules/@nestjs/schematics                sanad\n@nestjs/swagger                     7.4.2    7.4.2  11.2.0  node_modules/@nestjs/swagger                   sanad\n@nestjs/testing                   10.4.19  10.4.19  11.1.3  node_modules/@nestjs/testing                   sanad\n@nestjs/throttler                   5.2.0    5.2.0   6.4.0  node_modules/@nestjs/throttler                 sanad\n@types/express                    4.17.23  4.17.23   5.0.3  node_modules/@types/express                    sanad\n@types/jest                       29.5.14  29.5.14  30.0.0  node_modules/@types/jest                       sanad\n@types/node                       20.19.1  20.19.2  24.0.7  node_modules/@types/node                       sanad\n@types/supertest                   2.0.16   2.0.16   6.0.3  node_modules/@types/supertest                  sanad\n@types/uuid                         9.0.8    9.0.8  10.0.0  node_modules/@types/uuid                       sanad\n@typescript-eslint/eslint-plugin   6.21.0   6.21.0  8.35.0  node_modules/@typescript-eslint/eslint-plugin  sanad\n@typescript-eslint/parser          6.21.0   6.21.0  8.35.0  node_modules/@typescript-eslint/parser         sanad\neslint                             8.57.1   8.57.1  9.30.0  node_modules/eslint                            sanad\neslint-config-prettier              9.1.0    9.1.0  10.1.5  node_modules/eslint-config-prettier            sanad\neslint-plugin-prettier              5.5.0    5.5.1   5.5.1  node_modules/eslint-plugin-prettier            sanad\nhelmet                              7.2.0    7.2.0   8.1.0  node_modules/helmet                            sanad\nhusky                               8.0.3    8.0.3   9.1.7  node_modules/husky                             sanad\njest                               29.7.0   29.7.0  30.0.3  node_modules/jest                              sanad\nlint-staged                        15.5.2   15.5.2  16.1.2  node_modules/lint-staged                       sanad\nopenai                            4.104.0  4.104.0   5.8.2  node_modules/openai                            sanad\nprettier                            3.5.3    3.6.2   3.6.2  node_modules/prettier                          sanad\nreflect-metadata                   0.1.14   0.1.14   0.2.2  node_modules/reflect-metadata                  sanad\nrimraf                             5.0.10   5.0.10   6.0.1  node_modules/rimraf                            sanad\nsupertest                           6.3.4    6.3.4   7.1.1  node_modules/supertest                         sanad\ntwilio                             4.23.0   4.23.0   5.7.1  node_modules/twilio                            sanad\nuuid                                9.0.1    9.0.1  11.1.0  node_modules/uuid                              sanad\n", "timestamp": "2025-06-30T09:14:58.497Z"}]}
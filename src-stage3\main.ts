import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Enable CORS
  app.enableCors();
  
  // Enable validation pipes
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));
  
  // Get port from environment
  const port = process.env.PORT || 3000;
  
  console.log(`🚀 Sanad PAIM Stage 3 - Business Logic starting on port ${port}`);
  console.log(`📍 Health check: http://localhost:${port}/health`);
  console.log(`📍 Status: http://localhost:${port}/status`);
  console.log(`📍 OpenAI Test: http://localhost:${port}/ai/test`);
  console.log(`📍 Twilio Test: http://localhost:${port}/twilio/test`);
  console.log(`🎯 Stage: Stage 3 - Business Logic Dependencies`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  
  await app.listen(port);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});

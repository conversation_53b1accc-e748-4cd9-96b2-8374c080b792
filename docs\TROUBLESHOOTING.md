# PAIM Troubleshooting Guide

This comprehensive troubleshooting guide helps diagnose and resolve common issues in the PAIM system across all environments and components.

## 📋 Table of Contents

- [Quick Diagnostics](#quick-diagnostics)
- [Application Issues](#application-issues)
- [Infrastructure Issues](#infrastructure-issues)
- [External Service Issues](#external-service-issues)
- [Performance Issues](#performance-issues)
- [Security Issues](#security-issues)
- [Data Issues](#data-issues)
- [Monitoring and Alerting Issues](#monitoring-and-alerting-issues)

## 🔍 Quick Diagnostics

### Health Check Commands

```bash
# Application health
curl -f https://your-app.ondigitalocean.app/health

# Detailed health check
curl -f https://your-app.ondigitalocean.app/health/detailed

# Environment validation
npm run validate:env

# System status
npm run monitoring:status
```

### Log Analysis

```bash
# Application logs
doctl apps logs <app-id> --follow

# Docker logs (local)
docker-compose logs -f paim-app

# System logs
journalctl -u paim-app -f

# Monitoring logs
npm run monitoring:logs
```

### Quick Status Check

```bash
# Check all services
curl -s https://your-app.ondigitalocean.app/health/detailed | jq '.'

# Check external dependencies
curl -s https://cloud.appwrite.io/v1/health
curl -s https://api.openai.com/v1/models

# Check monitoring
curl -s http://localhost:9090/-/healthy
curl -s http://localhost:9093/-/healthy
```

## 🚨 Application Issues

### Application Won't Start

#### Symptoms
- Application fails to start
- Health checks fail
- 502/503 errors

#### Diagnosis
```bash
# Check application logs
doctl apps logs <app-id> --type run

# Check build logs
doctl apps logs <app-id> --type build

# Validate environment
npm run validate:env

# Check dependencies
npm audit
```

#### Common Causes & Solutions

1. **Missing Environment Variables**
   ```bash
   # Check required variables
   npm run validate:env
   
   # Add missing variables to Digital Ocean
   doctl apps spec get <app-id> > app.yaml
   # Edit app.yaml and update
   doctl apps update <app-id> --spec app.yaml
   ```

2. **Port Configuration Issues**
   ```bash
   # Ensure PORT is set correctly
   echo $PORT  # Should be 3000 for Digital Ocean
   
   # Check app.yaml
   http_port: 3000
   ```

3. **Dependency Issues**
   ```bash
   # Clear npm cache
   npm cache clean --force
   
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   
   # Update dependencies
   npm update
   ```

### High Memory Usage

#### Symptoms
- Application crashes with OOM errors
- Slow response times
- Memory alerts firing

#### Diagnosis
```bash
# Check memory usage
doctl apps get <app-id>

# Monitor memory in real-time
curl -s http://localhost:9090/api/v1/query?query=process_resident_memory_bytes

# Check for memory leaks
node --inspect scripts/memory-check.js
```

#### Solutions
```bash
# Increase memory limit in app.yaml
instance_size_slug: basic-s  # 1GB instead of basic-xxs (512MB)

# Optimize application
# 1. Review caching strategy
# 2. Check for memory leaks
# 3. Optimize data structures
# 4. Implement garbage collection tuning
```

### High CPU Usage

#### Symptoms
- Slow response times
- CPU alerts firing
- Application timeouts

#### Diagnosis
```bash
# Check CPU usage
curl -s http://localhost:9090/api/v1/query?query=rate\(process_cpu_seconds_total\[5m\]\)

# Profile application
node --prof app.js
node --prof-process isolate-*.log > processed.txt
```

#### Solutions
```bash
# Scale horizontally
instance_count: 3  # Increase instance count

# Optimize code
# 1. Review expensive operations
# 2. Implement caching
# 3. Optimize database queries
# 4. Use async/await properly
```

## 🏗️ Infrastructure Issues

### Digital Ocean App Platform Issues

#### Deployment Failures

```bash
# Check deployment status
doctl apps list-deployments <app-id>

# Get deployment logs
doctl apps logs <app-id> --deployment <deployment-id>

# Check app configuration
doctl apps spec get <app-id>
```

#### Common Solutions
```bash
# Retry deployment
doctl apps create-deployment <app-id>

# Update app spec
doctl apps update <app-id> --spec app.yaml

# Check resource limits
# Increase instance size if needed
```

### Redis Connection Issues

#### Symptoms
- Cache misses
- Redis connection errors
- Performance degradation

#### Diagnosis
```bash
# Check Redis status
docker exec paim-redis redis-cli ping

# Check Redis logs
docker logs paim-redis

# Monitor Redis metrics
curl -s http://localhost:9121/metrics | grep redis_up
```

#### Solutions
```bash
# Restart Redis
npm run redis:restart

# Check Redis configuration
cat redis/redis.conf

# Clear Redis cache if corrupted
docker exec paim-redis redis-cli flushall
```

### Network Connectivity Issues

#### Symptoms
- External API timeouts
- Webhook delivery failures
- DNS resolution errors

#### Diagnosis
```bash
# Test external connectivity
curl -v https://cloud.appwrite.io/v1/health
curl -v https://api.openai.com/v1/models
curl -v https://api.twilio.com/2010-04-01/Accounts.json

# Check DNS resolution
nslookup cloud.appwrite.io
dig cloud.appwrite.io

# Test from application
node scripts/test-connectivity.js
```

#### Solutions
```bash
# Check firewall rules
# Ensure outbound HTTPS (443) is allowed

# Verify DNS settings
# Use public DNS servers if needed

# Check proxy settings
# Ensure no proxy blocking external requests
```

## 🔌 External Service Issues

### Appwrite Connection Issues

#### Symptoms
- Database operation failures
- Authentication errors
- File upload failures

#### Diagnosis
```bash
# Test Appwrite connection
node scripts/test-appwrite-connection.js

# Check Appwrite status
curl -s https://cloud.appwrite.io/v1/health

# Verify credentials
echo $APPWRITE_API_KEY | cut -c1-10  # Should show first 10 chars
```

#### Solutions
```bash
# Verify environment variables
npm run validate:env

# Check API key permissions
# Ensure API key has required scopes

# Test with different endpoint
# Try different Appwrite region if available
```

### Twilio WhatsApp Issues

#### Symptoms
- Messages not sending
- Webhook not receiving
- Authentication failures

#### Diagnosis
```bash
# Test Twilio credentials
curl -X GET "https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID.json" \
  -u $TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN

# Check webhook URL
curl -X POST https://your-app.ondigitalocean.app/api/v1/webhook/whatsapp \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "From=whatsapp:+**********&Body=test"

# Verify WhatsApp number
echo $TWILIO_WHATSAPP_NUMBER
```

#### Solutions
```bash
# Update webhook URL in Twilio console
# Ensure HTTPS and correct endpoint

# Verify phone number format
# Should be: whatsapp:+**********

# Check Twilio account status
# Ensure account is active and funded
```

### OpenAI API Issues

#### Symptoms
- AI responses failing
- Rate limit errors
- Authentication failures

#### Diagnosis
```bash
# Test OpenAI API
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models

# Check rate limits
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/usage

# Test chat completion
node scripts/test-openai.js
```

#### Solutions
```bash
# Check API key validity
# Regenerate if expired

# Implement rate limiting
# Add exponential backoff

# Monitor usage
# Upgrade plan if needed
```

## ⚡ Performance Issues

### Slow Response Times

#### Symptoms
- High P95/P99 latency
- User complaints about slowness
- Timeout errors

#### Diagnosis
```bash
# Check response time metrics
curl -s http://localhost:9090/api/v1/query?query=histogram_quantile\(0.95,rate\(http_request_duration_ms_bucket\[5m\]\)\)

# Run performance tests
npm run perf:test

# Profile application
node --inspect app.js
```

#### Solutions
```bash
# Enable Redis caching
npm run redis:setup

# Optimize database queries
# Add indexes, reduce data transfer

# Scale horizontally
# Increase instance count

# Implement CDN
# Use Digital Ocean Spaces CDN
```

### High Error Rates

#### Symptoms
- Increased 5xx errors
- Error rate alerts
- User-reported failures

#### Diagnosis
```bash
# Check error rate
curl -s http://localhost:9090/api/v1/query?query=rate\(http_requests_total\{status=~\"5..\"\}\[5m\]\)

# Analyze error logs
doctl apps logs <app-id> | grep ERROR

# Check error patterns
grep -E "(ERROR|FATAL)" logs/app.log | tail -100
```

#### Solutions
```bash
# Fix application errors
# Review and fix code issues

# Improve error handling
# Add try-catch blocks and proper error responses

# Monitor dependencies
# Ensure external services are healthy
```

## 🔒 Security Issues

### Authentication Failures

#### Symptoms
- Users cannot log in
- JWT token errors
- Unauthorized access attempts

#### Diagnosis
```bash
# Check authentication logs
grep "auth" logs/app.log | tail -50

# Test JWT validation
node scripts/test-jwt.js

# Check rate limiting
curl -v https://your-app.ondigitalocean.app/api/v1/auth/login
```

#### Solutions
```bash
# Verify JWT secret
# Ensure JWT_SECRET is properly set

# Check token expiration
# Adjust token TTL if needed

# Review rate limiting
# Ensure legitimate users aren't blocked
```

### Suspicious Activity

#### Symptoms
- Multiple failed login attempts
- Unusual traffic patterns
- Security alerts

#### Diagnosis
```bash
# Check security logs
grep -E "(SECURITY|SUSPICIOUS)" logs/app.log

# Analyze IP patterns
awk '{print $1}' access.log | sort | uniq -c | sort -nr

# Check rate limit violations
curl -s http://localhost:9090/api/v1/query?query=rate_limit_violations_total
```

#### Solutions
```bash
# Block suspicious IPs
# Update firewall rules

# Increase rate limiting
# Reduce allowed requests per window

# Enable additional monitoring
# Add security-specific alerts
```

## 💾 Data Issues

### Data Corruption

#### Symptoms
- Inconsistent data
- Application errors
- User data missing

#### Diagnosis
```bash
# Check data integrity
node scripts/verify-data-integrity.js

# Analyze database logs
# Check Appwrite console for errors

# Compare with backups
node scripts/compare-with-backup.js
```

#### Solutions
```bash
# Restore from backup
node scripts/restore-appwrite.js backups/latest

# Fix data inconsistencies
node scripts/fix-data-issues.js

# Implement data validation
# Add stricter validation rules
```

### Backup Failures

#### Symptoms
- Backup alerts
- Missing backup files
- Backup verification failures

#### Diagnosis
```bash
# Check backup logs
tail -f logs/backup.log

# Test backup process
npm run backup:appwrite

# Verify backup integrity
npm run backup:verify backups/latest
```

#### Solutions
```bash
# Fix backup configuration
# Check credentials and permissions

# Increase backup frequency
# Reduce risk of data loss

# Test restore process
# Ensure backups are usable
```

## 📊 Monitoring and Alerting Issues

### Missing Metrics

#### Symptoms
- Gaps in monitoring data
- Missing dashboards
- No alerts firing

#### Diagnosis
```bash
# Check Prometheus targets
curl -s http://localhost:9090/api/v1/targets

# Verify metric collection
curl -s http://localhost:9090/api/v1/label/__name__/values

# Check exporters
curl -s http://localhost:9100/metrics  # Node exporter
curl -s http://localhost:9121/metrics  # Redis exporter
```

#### Solutions
```bash
# Restart monitoring stack
npm run monitoring:restart

# Check configuration
# Verify prometheus.yml and alert rules

# Update exporters
# Ensure all exporters are running
```

### Alert Fatigue

#### Symptoms
- Too many alerts
- Important alerts missed
- Team ignoring alerts

#### Solutions
```bash
# Review alert thresholds
# Adjust to reduce false positives

# Implement alert grouping
# Group related alerts together

# Add alert silencing
npm run alerts:silence

# Improve alert descriptions
# Make alerts more actionable
```

## 🛠️ General Troubleshooting Tips

### Systematic Approach

1. **Identify the Problem**
   - Gather symptoms and error messages
   - Check recent changes or deployments
   - Review monitoring dashboards

2. **Isolate the Issue**
   - Test individual components
   - Check dependencies
   - Verify configuration

3. **Implement Solution**
   - Apply targeted fixes
   - Monitor for improvement
   - Document the resolution

4. **Prevent Recurrence**
   - Update monitoring
   - Improve documentation
   - Add preventive measures

### Useful Commands Reference

```bash
# Health checks
npm run validate:env
curl -f https://your-app.ondigitalocean.app/health

# Logs
doctl apps logs <app-id> --follow
docker-compose logs -f

# Monitoring
curl -s http://localhost:9090/api/v1/targets
curl -s http://localhost:9093/api/v1/alerts

# Performance
npm run perf:smoke
npm run test:quality

# Backup and restore
npm run backup:full
npm run restore:verify
```

---

**Document Version**: 1.0  
**Last Updated**: [Date]  
**Next Review**: [Date + 3 months]  
**Maintained by**: DevOps Team

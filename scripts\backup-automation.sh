#!/bin/bash

# PAIM Backup Automation Script
# Comprehensive backup solution with scheduling and monitoring

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR="${BACKUP_DIR:-./backups}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"
COMPRESSION="${COMPRESSION:-true}"
NOTIFICATION_WEBHOOK="${NOTIFICATION_WEBHOOK:-}"
LOG_FILE="${LOG_FILE:-./logs/backup.log}"
LOCK_FILE="/tmp/paim-backup.lock"

# Functions
log() {
    local message="$1"
    local timestamp=$(date +'%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[$timestamp]${NC} $message"
    echo "[$timestamp] $message" >> "$LOG_FILE"
}

success() {
    local message="$1"
    echo -e "${GREEN}✅ $message${NC}"
    log "SUCCESS: $message"
}

warning() {
    local message="$1"
    echo -e "${YELLOW}⚠️  $message${NC}"
    log "WARNING: $message"
}

error() {
    local message="$1"
    echo -e "${RED}❌ $message${NC}"
    log "ERROR: $message"
    exit 1
}

# Check if backup is already running
check_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            error "Backup is already running (PID: $pid)"
        else
            warning "Stale lock file found, removing..."
            rm -f "$LOCK_FILE"
        fi
    fi
    
    echo $$ > "$LOCK_FILE"
}

# Remove lock file
cleanup() {
    rm -f "$LOCK_FILE"
}

# Trap cleanup on exit
trap cleanup EXIT

# Create necessary directories
create_directories() {
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$(dirname "$LOG_FILE")"
}

# Send notification
send_notification() {
    local status="$1"
    local message="$2"
    local details="$3"
    
    if [ -n "$NOTIFICATION_WEBHOOK" ]; then
        local payload=$(cat << EOF
{
    "text": "PAIM Backup $status",
    "attachments": [
        {
            "color": "$( [ "$status" = "SUCCESS" ] && echo "good" || echo "danger" )",
            "fields": [
                {
                    "title": "Status",
                    "value": "$status",
                    "short": true
                },
                {
                    "title": "Message",
                    "value": "$message",
                    "short": false
                },
                {
                    "title": "Details",
                    "value": "$details",
                    "short": false
                },
                {
                    "title": "Timestamp",
                    "value": "$(date -u +"%Y-%m-%d %H:%M:%S UTC")",
                    "short": true
                }
            ]
        }
    ]
}
EOF
        )
        
        curl -X POST -H 'Content-type: application/json' \
             --data "$payload" \
             "$NOTIFICATION_WEBHOOK" > /dev/null 2>&1 || true
    fi
}

# Perform Appwrite backup
backup_appwrite() {
    log "Starting Appwrite backup..."
    
    local start_time=$(date +%s)
    local backup_output
    
    if backup_output=$(node scripts/backup-appwrite.js 2>&1); then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        success "Appwrite backup completed in ${duration}s"
        echo "$backup_output" >> "$LOG_FILE"
        
        # Extract backup path from output
        local backup_path=$(echo "$backup_output" | grep -o "Backup location: .*" | cut -d' ' -f3-)
        echo "$backup_path"
    else
        error "Appwrite backup failed: $backup_output"
    fi
}

# Backup application logs
backup_logs() {
    log "Backing up application logs..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local logs_backup_dir="$BACKUP_DIR/logs-backup-$timestamp"
    
    mkdir -p "$logs_backup_dir"
    
    # Copy application logs
    if [ -d "./logs" ]; then
        cp -r ./logs "$logs_backup_dir/app-logs"
    fi
    
    # Copy Docker logs if available
    if command -v docker &> /dev/null; then
        mkdir -p "$logs_backup_dir/docker-logs"
        
        # Get PAIM container logs
        for container in $(docker ps --format "table {{.Names}}" | grep paim); do
            docker logs "$container" > "$logs_backup_dir/docker-logs/${container}.log" 2>&1 || true
        done
    fi
    
    # Compress logs backup
    if [ "$COMPRESSION" = "true" ]; then
        tar -czf "$logs_backup_dir.tar.gz" -C "$BACKUP_DIR" "$(basename "$logs_backup_dir")"
        rm -rf "$logs_backup_dir"
        success "Logs backup compressed: $logs_backup_dir.tar.gz"
        echo "$logs_backup_dir.tar.gz"
    else
        success "Logs backup completed: $logs_backup_dir"
        echo "$logs_backup_dir"
    fi
}

# Backup configuration files
backup_config() {
    log "Backing up configuration files..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local config_backup_dir="$BACKUP_DIR/config-backup-$timestamp"
    
    mkdir -p "$config_backup_dir"
    
    # Copy important configuration files
    local config_files=(
        ".env.example"
        "package.json"
        "tsconfig.json"
        "docker-compose*.yml"
        "Dockerfile*"
        ".do/app.yaml"
        "monitoring/"
        "redis/"
    )
    
    for file in "${config_files[@]}"; do
        if [ -e "$file" ]; then
            cp -r "$file" "$config_backup_dir/" 2>/dev/null || true
        fi
    done
    
    # Compress config backup
    if [ "$COMPRESSION" = "true" ]; then
        tar -czf "$config_backup_dir.tar.gz" -C "$BACKUP_DIR" "$(basename "$config_backup_dir")"
        rm -rf "$config_backup_dir"
        success "Configuration backup compressed: $config_backup_dir.tar.gz"
        echo "$config_backup_dir.tar.gz"
    else
        success "Configuration backup completed: $config_backup_dir"
        echo "$config_backup_dir"
    fi
}

# Verify backup integrity
verify_backup() {
    local backup_path="$1"
    
    log "Verifying backup integrity: $backup_path"
    
    if [ -f "$backup_path" ]; then
        # For compressed files, check if they can be read
        if [[ "$backup_path" == *.tar.gz ]]; then
            if tar -tzf "$backup_path" > /dev/null 2>&1; then
                success "Backup integrity verified: $backup_path"
                return 0
            fi
        elif [[ "$backup_path" == *.zip ]]; then
            if unzip -t "$backup_path" > /dev/null 2>&1; then
                success "Backup integrity verified: $backup_path"
                return 0
            fi
        fi
    elif [ -d "$backup_path" ]; then
        # For directories, check if manifest exists
        if [ -f "$backup_path/manifest.json" ]; then
            success "Backup integrity verified: $backup_path"
            return 0
        fi
    fi
    
    warning "Backup integrity check failed: $backup_path"
    return 1
}

# Clean up old backups
cleanup_old_backups() {
    log "Cleaning up backups older than $RETENTION_DAYS days..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        return
    fi
    
    local deleted_count=0
    
    # Find and delete old backup files
    while IFS= read -r -d '' file; do
        rm -rf "$file"
        deleted_count=$((deleted_count + 1))
        log "Deleted old backup: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -type f -name "*backup*" -mtime +$RETENTION_DAYS -print0 2>/dev/null)
    
    # Find and delete old backup directories
    while IFS= read -r -d '' dir; do
        rm -rf "$dir"
        deleted_count=$((deleted_count + 1))
        log "Deleted old backup directory: $(basename "$dir")"
    done < <(find "$BACKUP_DIR" -type d -name "*backup*" -mtime +$RETENTION_DAYS -print0 2>/dev/null)
    
    if [ $deleted_count -gt 0 ]; then
        success "Cleaned up $deleted_count old backups"
    else
        log "No old backups to clean up"
    fi
}

# Generate backup report
generate_report() {
    local appwrite_backup="$1"
    local logs_backup="$2"
    local config_backup="$3"
    local start_time="$4"
    local end_time="$5"
    
    local duration=$((end_time - start_time))
    local timestamp=$(date -u +"%Y-%m-%d %H:%M:%S UTC")
    
    local report=$(cat << EOF
PAIM Backup Report
==================
Timestamp: $timestamp
Duration: ${duration}s

Backups Created:
- Appwrite Data: $appwrite_backup
- Application Logs: $logs_backup
- Configuration: $config_backup

Backup Directory: $BACKUP_DIR
Retention Policy: $RETENTION_DAYS days
Compression: $COMPRESSION

Status: SUCCESS
EOF
    )
    
    echo "$report"
    echo "$report" >> "$LOG_FILE"
    
    return "$report"
}

# Main backup function
perform_backup() {
    log "🚀 Starting comprehensive PAIM backup..."
    
    local start_time=$(date +%s)
    local appwrite_backup=""
    local logs_backup=""
    local config_backup=""
    
    # Create directories
    create_directories
    
    # Perform backups
    appwrite_backup=$(backup_appwrite)
    logs_backup=$(backup_logs)
    config_backup=$(backup_config)
    
    # Verify backups
    verify_backup "$appwrite_backup" || warning "Appwrite backup verification failed"
    verify_backup "$logs_backup" || warning "Logs backup verification failed"
    verify_backup "$config_backup" || warning "Config backup verification failed"
    
    # Clean up old backups
    cleanup_old_backups
    
    local end_time=$(date +%s)
    
    # Generate report
    local report=$(generate_report "$appwrite_backup" "$logs_backup" "$config_backup" "$start_time" "$end_time")
    
    # Send success notification
    send_notification "SUCCESS" "Backup completed successfully" "$report"
    
    success "Comprehensive backup completed successfully!"
}

# Install backup cron job
install_cron() {
    local schedule="${1:-0 2 * * *}"  # Default: daily at 2 AM
    local script_path="$(realpath "$0")"
    
    log "Installing backup cron job with schedule: $schedule"
    
    # Remove existing cron job
    crontab -l 2>/dev/null | grep -v "$script_path" | crontab - || true
    
    # Add new cron job
    (crontab -l 2>/dev/null; echo "$schedule $script_path backup") | crontab -
    
    success "Backup cron job installed: $schedule"
}

# Remove backup cron job
remove_cron() {
    local script_path="$(realpath "$0")"
    
    log "Removing backup cron job..."
    
    crontab -l 2>/dev/null | grep -v "$script_path" | crontab - || true
    
    success "Backup cron job removed"
}

# Main execution
main() {
    case "${1:-backup}" in
        "backup")
            check_lock
            perform_backup
            ;;
        "install-cron")
            install_cron "$2"
            ;;
        "remove-cron")
            remove_cron
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "verify")
            verify_backup "$2"
            ;;
        *)
            echo "Usage: $0 {backup|install-cron|remove-cron|cleanup|verify}"
            echo ""
            echo "Commands:"
            echo "  backup                    - Perform full backup"
            echo "  install-cron [schedule]   - Install cron job (default: daily at 2 AM)"
            echo "  remove-cron              - Remove cron job"
            echo "  cleanup                   - Clean up old backups"
            echo "  verify <backup_path>      - Verify backup integrity"
            echo ""
            echo "Environment Variables:"
            echo "  BACKUP_DIR               - Backup directory (default: ./backups)"
            echo "  RETENTION_DAYS           - Backup retention in days (default: 30)"
            echo "  COMPRESSION              - Enable compression (default: true)"
            echo "  NOTIFICATION_WEBHOOK     - Webhook URL for notifications"
            echo "  LOG_FILE                 - Log file path (default: ./logs/backup.log)"
            ;;
    esac
}

# Handle errors
handle_error() {
    local exit_code=$?
    error "Backup failed with exit code: $exit_code"
    send_notification "FAILED" "Backup failed" "Exit code: $exit_code"
}

trap handle_error ERR

# Run the script
main "$@"

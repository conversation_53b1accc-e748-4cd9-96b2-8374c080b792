// File: /paim-core/skills/onboarding.ts
export const skill = {
  id: "onboarding",
  trigger: ["hello", "start", "setup", "onboard"],
  handler: async (_, context) => {
    const config = await context.getUserConfig();
    if (config.name) return `You're already set up as ${config.name}. Say 'restart onboarding' to redo.`;

    await context.setState("onboarding:step", 1);
    return `Hey, I'm your PAIM. What would you like to call me?`;
  }
};


// File: /paim-core/skills/roadmap.ts
export const skill = {
  id: "roadmap",
  trigger: ["roadmap", "features", "what's next"],
  handler: async () => {
    const roadmap = [
      "✔️ Voice input via WhatsApp",
      "🧠 Memory recall and tagging",
      "📎 GDrive/Dropbox sync",
      "🤝 Team mode with admin roles",
      "🌍 Arabic Brain expansion"
    ];
    return `Here’s what I’m working on:
\n${roadmap.join("\n")}`;
  }
};


// File: /paim-core/skills/remind.me.ts
export const skill = {
  id: "remind.me",
  trigger: ["remind", "remind me"],
  handler: async (input, context) => {
    const msg = input.replace(/remind (me )?to/i, '').trim();
    await context.saveReminder(msg);
    return context.reply(`Got it. I’ll remind you.`);
  }
};


// File: /paim-core/skills/memory.capture.ts
export const skill = {
  id: "memory.capture",
  trigger: ["remember", "note", "idea"],
  handler: async (input, context) => {
    await context.saveMemory({ content: input, created_at: new Date() });
    return `Noted and saved to your memory.`;
  }
};


// File: /paim-core/services/skillRouter.ts
import * as skills from '../skills';

export const routeSkill = async (input, context) => {
  for (const s of Object.values(skills)) {
    if (s.trigger.some(t => input.toLowerCase().includes(t))) {
      return await s.handler(input, context);
    }
  }
  return `I'm not sure how to respond to that yet.`;
};


// File: /paim-core/services/tone.service.ts
const toneMap = {
  friendly: msg => `😊 ${msg}`,
  pro: msg => `✅ ${msg}`,
  witty: msg => `😏 ${msg}`
};

export const toneService = {
  reply: (msg, user) => {
    const tone = user?.tone || 'friendly';
    return toneMap[tone]?.(msg) || msg;
  }
};


// File: /paim-core/services/memory.service.ts
export const memoryService = {
  saveMemory: async (entry) => {
    console.log("[Memory Saved]", entry);
    // Replace with DB call
  },
  saveReminder: async (text) => {
    console.log("[Reminder Set]", text);
    // Store or schedule logic here
  }
};


// File: /paim-core/config/tone.config.json
{
  "friendly": "😊",
  "pro": "✅",
  "witty": "😏"
}


// File: /paim-core/config/roadmap.json
[
  "✔️ Voice input via WhatsApp",
  "🧠 Memory recall and tagging",
  "📎 GDrive/Dropbox sync",
  "🤝 Team mode with admin roles",
  "🌍 Arabic Brain expansion"
]

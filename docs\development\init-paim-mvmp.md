You are building the Minimum Viable Magic Product (MVMP) version of PAIM – a personal AI assistant designed to function entirely through WhatsApp.

🟡 GOAL: Build a modular, conversation-first MVP framework with support for:
- Human-centric onboarding
- User-defined name & tone for PAIM
- Live roadmap skill
- WhatsApp integration (Twilio or Meta API)
- Voice and text input
- Clean skill-based architecture

📦 FRAMEWORK REQUIREMENTS:

1. **Language/Stack**: 
   - Node.js
   - NestJS (modular)
   - TypeScript

2. **Directory Structure**:
```bash
/paim-mvmp
├── src/
│   ├── main.ts
│   ├── app.module.ts
│   ├── skills/
│   │   ├── onboarding.ts           # Conversational onboarding logic
│   │   ├── tone-selector.ts        # Personality config handler
│   │   ├── roadmap.ts              # Feature update + open roadmap replies
│   │   ├── smart-reply.ts          # AI logic
│   │   └── voice-handler.ts        # Voice input parser
│   ├── services/
│   │   ├── memory.service.ts       # User session memory (temp & persistent)
│   │   └── tone.service.ts         # Response styling by persona
│   ├── ai/
│   │   └── openai.service.ts       # OpenAI interaction logic
│   ├── interfaces/
│   │   └── user-config.interface.ts
│   └── utils/
│       └── formatter.ts
├── .env.example
├── package.json
└── README.md
````

3. **Core Features**:

   * `onboarding.ts`: Handles intro, naming PAIM, tone selection, goals
   * `roadmap.ts`: Sends a friendly roadmap message (pulled from JSON/remote)
   * `tone-selector.ts`: Adjusts reply style (friendly, pro, witty, etc.)
   * `openai.service.ts`: Abstracted GPT-4o integration
   * `voice-handler.ts`: Accepts & converts WhatsApp voice messages
   * `memory.service.ts`: Handles short-term and named user config

4. **WhatsApp Integration**:

   * Webhook for inbound messages (Twilio or Meta API)
   * Send replies based on user state + tone
   * Store user config as JSON (per phone number or session ID)

5. **Extensibility**:

   * All `skills/` should be standalone modules callable by `router.service.ts`
   * Each skill returns a simple reply object `{ type, content, tone }`
   * `roadmap.ts` reads from static or dynamic JSON (future-proofed for DB)

6. **Voice Input Support**:

   * Accepts voice messages from WhatsApp
   * Converts using OpenAI Whisper or similar (stub for now)
   * Replies either in text or with generated voice audio file

7. **README.md Content**:

   * Project philosophy: “Second brain inside WhatsApp”
   * Setup instructions for local + production
   * `getStarted()` example
   * How to add a new skill in `/skills/`

🎯 MVP Objective: The user chats with PAIM on WhatsApp. PAIM onboards them with a name and tone. User can ask about upcoming features with the word `roadmap`. All of this feels like a **conversation**, not a chatbot.

Ensure code is modular, clean, and scalable. Use interfaces for config and dependency injection best practices.

Tag this commit:
`feat: birth of the real one`

```

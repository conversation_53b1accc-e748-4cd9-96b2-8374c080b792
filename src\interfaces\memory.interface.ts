export interface IMemory {
  id: string;
  userId: string;
  content: string;
  tags: string[];
  category: MemoryCategory;
  importance: MemoryImportance;
  metadata: IMemoryMetadata;
  createdAt: Date;
  updatedAt: Date;
  accessCount: number;
  lastAccessedAt: Date;
}

export enum MemoryCategory {
  PERSONAL = 'personal',
  WORK = 'work',
  IDEA = 'idea',
  TASK = 'task',
  NOTE = 'note',
  CONTACT = 'contact',
  EVENT = 'event',
  REFERENCE = 'reference',
  OTHER = 'other',
}

export enum MemoryImportance {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4,
}

export interface IMemoryMetadata {
  source: string;
  relatedMemories: string[];
  location?: string;
  people?: string[];
  entities?: Record<string, string[]>;
  sentiment?: number;
  language?: string;
}

export interface IMemorySearchQuery {
  query?: string;
  tags?: string[];
  category?: MemoryCategory;
  importance?: MemoryImportance;
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}

export interface IMemorySearchResult {
  memories: IMemory[];
  total: number;
  hasMore: boolean;
}

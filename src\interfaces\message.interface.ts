export interface IMessage {
  id: string;
  userId: string;
  sessionId: string;
  content: string;
  type: MessageType;
  direction: MessageDirection;
  metadata: IMessageMetadata;
  timestamp: Date;
  processed: boolean;
  skillUsed?: string;
}

export enum MessageType {
  TEXT = 'text',
  VOICE = 'voice',
  IMAGE = 'image',
  DOCUMENT = 'document',
  SYSTEM = 'system',
}

export enum MessageDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
}

export interface IMessageMetadata {
  whatsappMessageId?: string;
  mediaUrl?: string;
  mediaType?: string;
  voiceTranscription?: string;
  processingTime?: number;
  errorMessage?: string;
  retryCount?: number;
}

export interface IWhatsAppMessage {
  From: string;
  To: string;
  Body: string;
  MessageSid: string;
  AccountSid: string;
  NumMedia: string;
  MediaUrl0?: string;
  MediaContentType0?: string;
}

export interface IMessageResponse {
  content: string;
  type: MessageType;
  tone?: string;
  metadata?: Record<string, any>;
}

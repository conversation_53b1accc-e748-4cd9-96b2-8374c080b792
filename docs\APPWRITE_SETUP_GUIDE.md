# 🏗️ Appwrite Backend Setup Guide for Sanad

## 📋 **Overview**
This guide will help you set up all required Appwrite collections and configurations for the Sanad application.

## 🚀 **Quick Setup Steps**

### 1. **Create Appwrite Project**
1. Go to [cloud.appwrite.io](https://cloud.appwrite.io)
2. Create a new project named "Sanad Production"
3. Note your Project ID
4. Generate an API key with full permissions

### 2. **Create Database**
1. Go to Databases in your Appwrite console
2. Create a new database named "sanad-production"
3. Note the Database ID

### 3. **Create Storage Bucket**
1. Go to Storage in your Appwrite console
2. Create a new bucket named "sanad-files"
3. Configure permissions for file uploads
4. Note the Bucket ID

## 📊 **Required Collections**

### **Collection 1: Users (`users`)**
**Purpose**: Store user profiles and preferences

**Attributes:**
```json
{
  "phoneNumber": { "type": "string", "size": 20, "required": true },
  "name": { "type": "string", "size": 100, "required": true },
  "tone": { "type": "string", "size": 50, "default": "friendly" },
  "isOnboarded": { "type": "boolean", "default": false },
  "isWhitelisted": { "type": "boolean", "default": false },
  "isEarlyAdopter": { "type": "boolean", "default": false },
  "registrationSource": { "type": "string", "size": 20, "default": "whatsapp" },
  "preferences": { "type": "string", "size": 2000 },
  "lastActiveAt": { "type": "datetime" }
}
```

**Indexes:**
- `phoneNumber` (unique)
- `isWhitelisted`
- `isEarlyAdopter`

### **Collection 2: Registrations (`registrations`)**
**Purpose**: Handle user registration requests

**Attributes:**
```json
{
  "email": { "type": "email", "required": true },
  "firstName": { "type": "string", "size": 50, "required": true },
  "lastName": { "type": "string", "size": 50, "required": true },
  "phoneNumber": { "type": "string", "size": 20 },
  "status": { "type": "string", "size": 20, "default": "pending" },
  "confirmationToken": { "type": "string", "size": 100 },
  "emailConfirmed": { "type": "boolean", "default": false },
  "earlyAdopterSelectedAt": { "type": "datetime" }
}
```

**Indexes:**
- `email` (unique)
- `status`
- `confirmationToken`

### **Collection 3: Whitelist (`whitelist`)**
**Purpose**: Manage whitelisted users

**Attributes:**
```json
{
  "email": { "type": "email", "required": true },
  "phoneNumber": { "type": "string", "size": 20 },
  "addedBy": { "type": "string", "size": 50 },
  "reason": { "type": "string", "size": 200 },
  "isActive": { "type": "boolean", "default": true }
}
```

**Indexes:**
- `email` (unique)
- `phoneNumber`
- `isActive`

### **Collection 4: Sessions (`sessions`)**
**Purpose**: Track user sessions

**Attributes:**
```json
{
  "userId": { "type": "string", "size": 50, "required": true },
  "sessionToken": { "type": "string", "size": 100, "required": true },
  "expiresAt": { "type": "datetime", "required": true },
  "isActive": { "type": "boolean", "default": true },
  "deviceInfo": { "type": "string", "size": 500 }
}
```

**Indexes:**
- `userId`
- `sessionToken` (unique)
- `expiresAt`

### **Collection 5: Memories (`memories`)**
**Purpose**: Store AI conversation memories

**Attributes:**
```json
{
  "userId": { "type": "string", "size": 50, "required": true },
  "content": { "type": "string", "size": 2000, "required": true },
  "category": { "type": "string", "size": 50 },
  "importance": { "type": "integer", "min": 1, "max": 10, "default": 5 },
  "tags": { "type": "string", "size": 500 },
  "lastAccessedAt": { "type": "datetime" }
}
```

**Indexes:**
- `userId`
- `category`
- `importance`

### **Collection 6: Reminders (`reminders`)**
**Purpose**: User reminders and tasks

**Attributes:**
```json
{
  "userId": { "type": "string", "size": 50, "required": true },
  "title": { "type": "string", "size": 200, "required": true },
  "description": { "type": "string", "size": 1000 },
  "dueDate": { "type": "datetime" },
  "isCompleted": { "type": "boolean", "default": false },
  "priority": { "type": "string", "size": 20, "default": "medium" },
  "reminderSent": { "type": "boolean", "default": false }
}
```

**Indexes:**
- `userId`
- `dueDate`
- `isCompleted`

### **Collection 7: Conversations (`conversations`)**
**Purpose**: Store conversation history

**Attributes:**
```json
{
  "userId": { "type": "string", "size": 50, "required": true },
  "messageId": { "type": "string", "size": 100 },
  "userMessage": { "type": "string", "size": 2000 },
  "aiResponse": { "type": "string", "size": 2000 },
  "messageType": { "type": "string", "size": 20, "default": "text" },
  "metadata": { "type": "string", "size": 1000 }
}
```

**Indexes:**
- `userId`
- `messageId`
- `messageType`

### **Collection 8: Emails (`emails`)**
**Purpose**: Email queue for Appwrite email processing

**Attributes:**
```json
{
  "to": { "type": "email", "required": true },
  "from": { "type": "email", "required": true },
  "subject": { "type": "string", "size": 200, "required": true },
  "htmlContent": { "type": "string", "size": 10000, "required": true },
  "status": { "type": "string", "size": 20, "default": "pending" },
  "attempts": { "type": "integer", "default": 0 },
  "lastAttemptAt": { "type": "datetime" },
  "sentAt": { "type": "datetime" }
}
```

**Indexes:**
- `status`
- `to`
- `createdAt`

### **Collection 9: Email Confirmations (`email_confirmations`)**
**Purpose**: Email confirmation tokens

**Attributes:**
```json
{
  "email": { "type": "email", "required": true },
  "token": { "type": "string", "size": 100, "required": true },
  "type": { "type": "string", "size": 50, "required": true },
  "expiresAt": { "type": "datetime", "required": true },
  "isUsed": { "type": "boolean", "default": false },
  "usedAt": { "type": "datetime" }
}
```

**Indexes:**
- `token` (unique)
- `email`
- `expiresAt`

## 🔐 **Permissions Setup**

### **For each collection, set these permissions:**

**Read Permissions:**
- `users` (authenticated users can read their own data)
- `role:admin` (admin can read all)

**Write Permissions:**
- `users` (authenticated users can write their own data)
- `role:admin` (admin can write all)

**Delete Permissions:**
- `role:admin` (only admin can delete)

## 🔧 **API Key Configuration**

Create an API key with these scopes:
- `databases.read`
- `databases.write`
- `collections.read`
- `collections.write`
- `documents.read`
- `documents.write`
- `files.read`
- `files.write`
- `users.read`
- `users.write`

## ✅ **Verification Checklist**

- [ ] Project created and Project ID noted
- [ ] Database created and Database ID noted
- [ ] Storage bucket created and Bucket ID noted
- [ ] All 9 collections created with proper attributes
- [ ] Indexes created for each collection
- [ ] Permissions configured correctly
- [ ] API key generated with proper scopes
- [ ] All IDs added to environment variables

## 🚀 **Next Steps**

After completing this setup:
1. Add all IDs to your `.env.digitalocean` file
2. Copy environment variables to Digital Ocean App Platform
3. Test the application deployment
4. Verify database connections

**Your Appwrite backend will be ready for production!** 🎉

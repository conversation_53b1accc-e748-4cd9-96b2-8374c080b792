import { Global, Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';

// Core services that will be available globally
import { LoggerService } from './services/logger.service';
import { ValidationService } from './services/validation.service';
import { ErrorHandlerService } from './services/error-handler.service';
import { AppConfigService } from './services/config.service';

// Filters, Interceptors, and Guards
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { ResponseTransformInterceptor } from './interceptors/response-transform.interceptor';
import { RateLimitGuard } from './guards/rate-limit.guard';

@Global()
@Module({
  providers: [
    // Core services
    LoggerService,
    ValidationService,
    ErrorHandlerService,
    AppConfigService,
    {
      provide: 'CONFIG_SERVICE',
      useClass: ConfigService,
    },

    // Global providers
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: RateLimitGuard,
    },
  ],
  exports: [
    LoggerService,
    ValidationService,
    ErrorHandlerService,
    AppConfigService,
    'CONFIG_SERVICE',
  ],
})
export class CoreModule {}

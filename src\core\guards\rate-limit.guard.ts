import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { LoggerService } from '../services/logger.service';

// Simple in-memory rate limiter (for production, use Redis)
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

@Injectable()
export class RateLimitGuard implements CanActivate {
  private rateLimitMap = new Map<string, RateLimitEntry>();
  private readonly windowMs = 60 * 1000; // 1 minute
  private readonly maxRequests = 10; // 10 requests per minute

  constructor(
    private reflector: Reflector,
    private logger: LoggerService,
  ) {
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();

    // Get rate limit configuration from decorator or use defaults
    const customLimit = this.reflector.get<number>(
      'rateLimit',
      context.getHandler(),
    );
    const customWindow = this.reflector.get<number>(
      'rateLimitWindow',
      context.getHandler(),
    );

    const limit = customLimit || this.maxRequests;
    const windowMs = customWindow || this.windowMs;

    // Create a unique key for the client (IP + User-Agent for basic fingerprinting)
    const clientKey = this.getClientKey(request);
    const now = Date.now();
    const resetTime = now + windowMs;

    // Get or create rate limit entry
    let entry = this.rateLimitMap.get(clientKey);

    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      entry = { count: 1, resetTime };
      this.rateLimitMap.set(clientKey, entry);
      return true;
    }

    // Increment count
    entry.count++;

    if (entry.count > limit) {
      this.logger.warn(
        `Rate limit exceeded for client: ${clientKey}`,
        'RateLimitGuard',
      );

      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil((entry.resetTime - now) / 1000),
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    return true;
  }

  private getClientKey(request: Request): string {
    const ip = request.ip || request.connection.remoteAddress || 'unknown';
    const userAgent = request.headers['user-agent'] || 'unknown';

    // For WhatsApp webhooks, use the phone number if available
    if (request.body?.From) {
      return `whatsapp:${request.body.From}`;
    }

    // Create a simple hash of IP + User-Agent
    return `${ip}:${userAgent.substring(0, 50)}`;
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.rateLimitMap.entries()) {
      if (now > entry.resetTime) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach((key) => this.rateLimitMap.delete(key));

    if (expiredKeys.length > 0) {
      this.logger.debug(
        `Cleaned up ${expiredKeys.length} expired rate limit entries`,
        'RateLimitGuard',
      );
    }
  }
}

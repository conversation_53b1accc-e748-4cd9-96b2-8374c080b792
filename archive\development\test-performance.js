#!/usr/bin/env node

/**
 * Performance Testing Script for PAIM Backend
 * Tests basic performance metrics and load handling
 */

const http = require('http');
const { performance } = require('perf_hooks');

// Configuration
const CONFIG = {
  host: 'localhost',
  port: 3000,
  concurrency: 10,
  requests: 100,
  timeout: 5000,
};

// Test endpoints
const ENDPOINTS = [
  { path: '/health', method: 'GET', name: 'Health Check' },
  { path: '/health/detailed', method: 'GET', name: 'Detailed Health Check' },
];

// Performance metrics
class PerformanceMetrics {
  constructor() {
    this.results = [];
    this.errors = [];
  }

  addResult(endpoint, responseTime, statusCode) {
    this.results.push({
      endpoint,
      responseTime,
      statusCode,
      timestamp: Date.now(),
    });
  }

  addError(endpoint, error) {
    this.errors.push({
      endpoint,
      error: error.message,
      timestamp: Date.now(),
    });
  }

  getStats(endpoint) {
    const endpointResults = this.results.filter(r => r.endpoint === endpoint);
    if (endpointResults.length === 0) return null;

    const responseTimes = endpointResults.map(r => r.responseTime);
    const successCount = endpointResults.filter(r => r.statusCode >= 200 && r.statusCode < 300).length;
    
    return {
      totalRequests: endpointResults.length,
      successCount,
      errorCount: this.errors.filter(e => e.endpoint === endpoint).length,
      successRate: (successCount / endpointResults.length) * 100,
      avgResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      p95ResponseTime: this.percentile(responseTimes, 95),
      p99ResponseTime: this.percentile(responseTimes, 99),
    };
  }

  percentile(arr, p) {
    const sorted = arr.sort((a, b) => a - b);
    const index = Math.ceil((p / 100) * sorted.length) - 1;
    return sorted[index];
  }

  printReport() {
    console.log('\n=== PERFORMANCE TEST REPORT ===\n');
    
    ENDPOINTS.forEach(endpoint => {
      const stats = this.getStats(endpoint.name);
      if (!stats) {
        console.log(`❌ ${endpoint.name}: No data collected`);
        return;
      }

      console.log(`📊 ${endpoint.name} (${endpoint.path})`);
      console.log(`   Total Requests: ${stats.totalRequests}`);
      console.log(`   Success Rate: ${stats.successRate.toFixed(2)}%`);
      console.log(`   Avg Response Time: ${stats.avgResponseTime.toFixed(2)}ms`);
      console.log(`   Min Response Time: ${stats.minResponseTime.toFixed(2)}ms`);
      console.log(`   Max Response Time: ${stats.maxResponseTime.toFixed(2)}ms`);
      console.log(`   95th Percentile: ${stats.p95ResponseTime.toFixed(2)}ms`);
      console.log(`   99th Percentile: ${stats.p99ResponseTime.toFixed(2)}ms`);
      console.log(`   Errors: ${stats.errorCount}`);
      console.log('');
    });

    // Overall assessment
    const overallStats = ENDPOINTS.map(e => this.getStats(e.name)).filter(Boolean);
    if (overallStats.length > 0) {
      const avgSuccessRate = overallStats.reduce((sum, s) => sum + s.successRate, 0) / overallStats.length;
      const avgResponseTime = overallStats.reduce((sum, s) => sum + s.avgResponseTime, 0) / overallStats.length;
      
      console.log('🎯 OVERALL ASSESSMENT:');
      console.log(`   Average Success Rate: ${avgSuccessRate.toFixed(2)}%`);
      console.log(`   Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
      
      // Performance thresholds
      if (avgSuccessRate >= 99) {
        console.log('   ✅ Excellent reliability');
      } else if (avgSuccessRate >= 95) {
        console.log('   ⚠️  Good reliability');
      } else {
        console.log('   ❌ Poor reliability - needs investigation');
      }
      
      if (avgResponseTime <= 100) {
        console.log('   ✅ Excellent performance');
      } else if (avgResponseTime <= 500) {
        console.log('   ⚠️  Acceptable performance');
      } else {
        console.log('   ❌ Poor performance - optimization needed');
      }
    }
  }
}

// HTTP request function
function makeRequest(endpoint) {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    
    const req = http.request({
      hostname: CONFIG.host,
      port: CONFIG.port,
      path: endpoint.path,
      method: endpoint.method,
      timeout: CONFIG.timeout,
    }, (res) => {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      resolve({
        statusCode: res.statusCode,
        responseTime,
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Load test function
async function runLoadTest() {
  console.log('🚀 Starting Performance Tests...');
  console.log(`Configuration: ${CONFIG.requests} requests, ${CONFIG.concurrency} concurrent`);
  console.log(`Target: http://${CONFIG.host}:${CONFIG.port}`);
  console.log('');

  const metrics = new PerformanceMetrics();
  
  for (const endpoint of ENDPOINTS) {
    console.log(`Testing ${endpoint.name}...`);
    
    const promises = [];
    for (let i = 0; i < CONFIG.requests; i++) {
      const promise = makeRequest(endpoint)
        .then(result => {
          metrics.addResult(endpoint.name, result.responseTime, result.statusCode);
        })
        .catch(error => {
          metrics.addError(endpoint.name, error);
        });
      
      promises.push(promise);
      
      // Control concurrency
      if (promises.length >= CONFIG.concurrency) {
        await Promise.all(promises);
        promises.length = 0;
      }
    }
    
    // Wait for remaining requests
    if (promises.length > 0) {
      await Promise.all(promises);
    }
  }

  metrics.printReport();
}

// Check if server is running
function checkServerAvailability() {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: CONFIG.host,
      port: CONFIG.port,
      path: '/health',
      method: 'GET',
      timeout: 2000,
    }, (res) => {
      resolve(true);
    });

    req.on('error', () => {
      resolve(false);
    });

    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Main execution
async function main() {
  console.log('🔍 Checking server availability...');
  
  const serverAvailable = await checkServerAvailability();
  
  if (!serverAvailable) {
    console.log('❌ Server is not running or not accessible');
    console.log(`   Please ensure the server is running on http://${CONFIG.host}:${CONFIG.port}`);
    console.log('   You can start it with: npm run start:dev');
    process.exit(1);
  }
  
  console.log('✅ Server is accessible');
  await runLoadTest();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runLoadTest, PerformanceMetrics };

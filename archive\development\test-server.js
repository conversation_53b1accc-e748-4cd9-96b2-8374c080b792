#!/usr/bin/env node

/**
 * Simple test server for PAIM
 * Tests basic functionality without complex dependencies
 */

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    pid: process.pid
  });
});

// Detailed health check
app.get('/health/detailed', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    pid: process.pid,
    services: {
      database: 'connected', // Placeholder
      cache: 'connected',    // Placeholder
      external_apis: {
        openai: 'available',
        twilio: 'available',
        appwrite: 'available'
      }
    },
    system: {
      platform: process.platform,
      arch: process.arch,
      node_version: process.version,
      cpu_usage: process.cpuUsage(),
      load_average: require('os').loadavg()
    }
  };
  
  res.json(health);
});

// API routes
app.get('/api/v1/status', (req, res) => {
  res.json({
    message: 'PAIM API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// WhatsApp webhook endpoint (test)
app.post('/api/v1/webhook/whatsapp', (req, res) => {
  console.log('WhatsApp webhook received:', req.body);
  
  // Simulate processing
  const response = {
    status: 'received',
    timestamp: new Date().toISOString(),
    message: 'Webhook processed successfully',
    data: {
      from: req.body.From || 'unknown',
      body: req.body.Body || 'empty',
      messageId: req.body.MessageSid || 'test-' + Date.now()
    }
  };
  
  res.json(response);
});

// User registration endpoint (test)
app.post('/api/v1/registration/request', (req, res) => {
  console.log('Registration request:', req.body);
  
  const response = {
    status: 'success',
    message: 'Registration request received',
    timestamp: new Date().toISOString(),
    data: {
      phoneNumber: req.body.phoneNumber,
      email: req.body.email,
      name: req.body.name,
      registrationId: 'reg-' + Date.now()
    }
  };
  
  res.json(response);
});

// API documentation
app.get('/api/docs', (req, res) => {
  const docs = {
    title: 'PAIM API Documentation',
    version: '1.0.0',
    description: 'Personal AI Manager API endpoints',
    endpoints: [
      {
        method: 'GET',
        path: '/health',
        description: 'Basic health check'
      },
      {
        method: 'GET',
        path: '/health/detailed',
        description: 'Detailed health check with system information'
      },
      {
        method: 'GET',
        path: '/api/v1/status',
        description: 'API status information'
      },
      {
        method: 'POST',
        path: '/api/v1/webhook/whatsapp',
        description: 'WhatsApp webhook endpoint'
      },
      {
        method: 'POST',
        path: '/api/v1/registration/request',
        description: 'User registration request'
      },
      {
        method: 'GET',
        path: '/api/docs',
        description: 'API documentation'
      }
    ]
  };
  
  res.json(docs);
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to PAIM - Personal AI Manager',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      api_status: '/api/v1/status',
      documentation: '/api/docs'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Endpoint ${req.method} ${req.originalUrl} not found`,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: err.message,
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 PAIM Test Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API docs: http://localhost:${PORT}/api/docs`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

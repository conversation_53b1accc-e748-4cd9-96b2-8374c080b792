import { SkillCategory as SkillCategoryEnum } from '../../interfaces';

export interface SkillMetadata {
  id: string;
  name: string;
  description: string;
  triggers: string[];
  category: SkillCategoryEnum;
  priority: number;
  enabled: boolean;
  version: string;
  author?: string;
  tags?: string[];
  examples?: string[];
  permissions?: string[];
  dependencies?: string[];
  configurable?: boolean;
  rateLimited?: boolean;
  maxExecutionTime?: number;
}

export interface SkillConfig {
  enabled: boolean;
  priority: number;
  customTriggers?: string[];
  parameters?: Record<string, any>;
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
  timeout?: number;
}

export interface SkillExecutionContext {
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  error?: string;
  inputLength: number;
  outputLength: number;
  userId: string;
  sessionId: string;
}

export interface SkillPerformanceMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecuted: Date;
  popularTriggers: Record<string, number>;
}

/**
 * Decorator for skill metadata
 */
export function SkillMetadata(metadata: Partial<SkillMetadata>) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    Reflect.defineMetadata('skill:metadata', metadata, constructor);
    return constructor;
  };
}

/**
 * Decorator for skill triggers
 */
export function SkillTriggers(...triggers: string[]) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    Reflect.defineMetadata('skill:triggers', triggers, constructor);
    return constructor;
  };
}

/**
 * Decorator for skill category
 */
export function SkillCategory(category: SkillCategoryEnum) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    Reflect.defineMetadata('skill:category', category, constructor);
    return constructor;
  };
}

/**
 * Decorator for skill priority
 */
export function SkillPriority(priority: number) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    Reflect.defineMetadata('skill:priority', priority, constructor);
    return constructor;
  };
}

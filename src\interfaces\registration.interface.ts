export interface IRegistrationRequest {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  reason?: string; // Why they want to join
  status: RegistrationStatus;
  confirmationToken?: string;
  emailConfirmed: boolean;
  emailConfirmedAt?: Date;
  isEarlyAdopter: boolean;
  earlyAdopterSelectedAt?: Date;
  earlyAdopterSelectedBy?: string; // Admin user ID
  createdAt: Date;
  updatedAt: Date;
}

export enum RegistrationStatus {
  PENDING = 'pending',
  EMAIL_CONFIRMED = 'email_confirmed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EARLY_ADOPTER = 'early_adopter',
}

export interface IWhitelistUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  registrationRequestId: string;
  isActive: boolean;
  isEarlyAdopter: boolean;
  earlyAdopterSelectedAt?: Date;
  earlyAdopterSelectedBy?: string;
  whatsappUserId?: string; // Links to IUser when they start using WhatsApp
  createdAt: Date;
  updatedAt: Date;
}

export interface IEmailConfirmation {
  id: string;
  email: string;
  token: string;
  registrationRequestId: string;
  expiresAt: Date;
  confirmedAt?: Date;
  createdAt: Date;
}

export interface IRegistrationStats {
  totalRequests: number;
  pendingRequests: number;
  confirmedEmails: number;
  earlyAdopters: number;
  activeUsers: number;
  registrationsToday: number;
  registrationsThisWeek: number;
  registrationsThisMonth: number;
}

// DTOs for API requests
export interface CreateRegistrationRequestDto {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  reason?: string;
}

export interface ConfirmEmailDto {
  token: string;
}

export interface SelectEarlyAdopterDto {
  registrationRequestId: string;
  adminUserId: string;
  notes?: string;
}

export interface UpdateRegistrationStatusDto {
  status: RegistrationStatus;
  adminUserId: string;
  notes?: string;
}

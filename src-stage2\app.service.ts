import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getStatus() {
    return {
      application: 'Sanad PAIM',
      stage: 'Stage 2',
      framework: 'NestJS',
      status: 'Running',
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + ' MB',
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + ' MB'
      },
      process: {
        pid: process.pid,
        uptime: Math.round(process.uptime()) + ' seconds',
        version: process.version
      },
      dependencies: {
        '@nestjs/core': 'Loaded',
        '@nestjs/common': 'Loaded',
        '@nestjs/platform-express': 'Loaded',
        'reflect-metadata': 'Loaded',
        'rxjs': 'Loaded'
      },
      timestamp: new Date().toISOString()
    };
  }
}

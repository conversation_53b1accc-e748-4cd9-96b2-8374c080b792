# Digital Ocean App Platform Deployment Script for Sanad (PowerShell)
# This script builds and deploys the application to Digital Ocean App Platform

Write-Host "🚀 Starting Digital Ocean App Platform deployment for Sanad..." -ForegroundColor Green

# Check if doctl CLI is installed
try {
    doctl version | Out-Null
    Write-Host "✅ doctl CLI found" -ForegroundColor Green
} catch {
    Write-Host "❌ doctl CLI not found. Please install it first:" -ForegroundColor Red
    Write-Host "   Visit: https://docs.digitalocean.com/reference/doctl/how-to/install/" -ForegroundColor Yellow
    Write-Host "   Or run: choco install doctl" -ForegroundColor Yellow
    exit 1
}

# Check if user is authenticated
try {
    doctl account get | Out-Null
    Write-Host "✅ doctl authenticated" -ForegroundColor Green
} catch {
    Write-Host "❌ doctl not authenticated. Please run: doctl auth init" -ForegroundColor Red
    exit 1
}

# Build the application
Write-Host "📦 Building application..." -ForegroundColor Yellow
npm run build

# Check if build was successful
if (-not (Test-Path "dist")) {
    Write-Host "❌ Build failed - dist directory not found" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build completed successfully" -ForegroundColor Green

# Check if app already exists
Write-Host "🔍 Checking if app exists..." -ForegroundColor Yellow
$appExists = $false
try {
    $apps = doctl apps list --format ID,Spec.Name --no-header | ConvertFrom-String -PropertyNames ID, Name
    foreach ($app in $apps) {
        if ($app.Name -eq "sanad-paim") {
            $appExists = $true
            $appId = $app.ID
            Write-Host "✅ Found existing app with ID: $appId" -ForegroundColor Green
            break
        }
    }
} catch {
    Write-Host "⚠️  Could not check existing apps" -ForegroundColor Yellow
}

if ($appExists) {
    # Update existing app
    Write-Host "🔄 Updating existing app..." -ForegroundColor Yellow
    doctl apps update $appId --spec .do/app.yaml
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ App updated successfully!" -ForegroundColor Green
        Write-Host "🌐 App URL: https://sanad-paim-*.ondigitalocean.app" -ForegroundColor Cyan
    } else {
        Write-Host "❌ App update failed" -ForegroundColor Red
        exit 1
    }
} else {
    # Create new app
    Write-Host "🆕 Creating new app..." -ForegroundColor Yellow
    doctl apps create --spec .do/app.yaml
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ App created successfully!" -ForegroundColor Green
        Write-Host "🌐 App URL will be available shortly at: https://sanad-paim-*.ondigitalocean.app" -ForegroundColor Cyan
    } else {
        Write-Host "❌ App creation failed" -ForegroundColor Red
        exit 1
    }
}

# Get app info
Write-Host "📊 Getting app information..." -ForegroundColor Yellow
doctl apps list --format ID,Spec.Name,DefaultIngress,CreatedAt,UpdatedAt | Where-Object { $_ -match "sanad-paim" }

Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Configure your environment variables in the DO dashboard" -ForegroundColor White
Write-Host "   2. Update your Twilio webhook URL to point to the new DO app" -ForegroundColor White
Write-Host "   3. Test the deployment with a health check" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Useful commands:" -ForegroundColor Yellow
Write-Host "   doctl apps list                    # List all apps" -ForegroundColor White
Write-Host "   doctl apps get <app-id>            # Get app details" -ForegroundColor White
Write-Host "   doctl apps logs <app-id>           # View app logs" -ForegroundColor White
Write-Host "   doctl apps delete <app-id>         # Delete app" -ForegroundColor White

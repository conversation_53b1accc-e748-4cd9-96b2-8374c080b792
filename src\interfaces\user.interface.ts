export interface IUser {
  id: string;
  phoneNumber: string;
  name?: string;
  tone: 'friendly' | 'pro' | 'witty';
  isOnboarded: boolean;
  preferences: IUserPreferences;
  // Registration-related fields
  email?: string;
  isWhitelisted: boolean;
  isEarlyAdopter: boolean;
  whitelistUserId?: string; // Links to IWhitelistUser
  registrationSource: 'whatsapp' | 'web';
  createdAt: Date;
  updatedAt: Date;
  lastActiveAt: Date;
}

export interface IUserPreferences {
  language: string;
  timezone: string;
  voiceEnabled: boolean;
  notificationsEnabled: boolean;
  maxMemories: number;
  defaultSkills: string[];
}

export interface IUserSession {
  userId: string;
  sessionId: string;
  context: Record<string, any>;
  state: Record<string, any>;
  conversationHistory: any[]; // Will be properly typed when IMessage is available
  createdAt: Date;
  expiresAt: Date;
}

export interface IUserConfig {
  name?: string;
  tone: 'friendly' | 'pro' | 'witty';
  language: string;
  timezone: string;
  preferences: IUserPreferences;
}

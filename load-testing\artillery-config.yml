config:
  # Target configuration
  target: 'http://localhost:3000'
  
  # Load testing phases
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 1
      name: "Warm-up"
    
    # Ramp-up phase
    - duration: 120
      arrivalRate: 1
      rampTo: 10
      name: "Ramp-up"
    
    # Sustained load phase
    - duration: 300
      arrivalRate: 10
      name: "Sustained load"
    
    # Peak load phase
    - duration: 120
      arrivalRate: 10
      rampTo: 25
      name: "Peak load"
    
    # Cool-down phase
    - duration: 60
      arrivalRate: 25
      rampTo: 1
      name: "Cool-down"

  # Default headers
  defaults:
    headers:
      'User-Agent': 'Artillery Load Test'
      'Accept': 'application/json'
      'Content-Type': 'application/json'

  # Payload configuration
  payload:
    path: './load-testing/test-data.csv'
    fields:
      - 'phoneNumber'
      - 'message'
      - 'userId'

  # Plugins
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true
    
    # Publish metrics to Prometheus (if available)
    publish-metrics:
      - type: prometheus
        pushgateway: 'http://localhost:9091'
        prefix: 'artillery_'

  # Performance thresholds
  ensure:
    # Response time thresholds
    p95: 500  # 95th percentile should be under 500ms
    p99: 1000 # 99th percentile should be under 1000ms
    
    # Error rate threshold
    maxErrorRate: 5  # Maximum 5% error rate
    
    # Throughput threshold
    minThroughput: 100  # Minimum 100 requests per second

# Test scenarios
scenarios:
  # Health check scenario
  - name: "Health Check"
    weight: 20
    flow:
      - get:
          url: "/health"
          capture:
            - json: "$.status"
              as: "healthStatus"
          expect:
            - statusCode: 200
            - hasProperty: "status"

  # Detailed health check scenario
  - name: "Detailed Health Check"
    weight: 10
    flow:
      - get:
          url: "/health/detailed"
          expect:
            - statusCode: 200
            - contentType: json

  # WhatsApp webhook simulation
  - name: "WhatsApp Webhook"
    weight: 50
    flow:
      - post:
          url: "/api/v1/webhook/whatsapp"
          headers:
            'X-Twilio-Signature': 'test-signature'
          json:
            From: "whatsapp:+{{ phoneNumber }}"
            To: "whatsapp:+**********"
            Body: "{{ message }}"
            MessageSid: "{{ $randomString() }}"
            AccountSid: "AC{{ $randomString() }}"
            NumMedia: "0"
          expect:
            - statusCode: 200

  # User registration simulation
  - name: "User Registration"
    weight: 15
    flow:
      - post:
          url: "/api/v1/registration/request"
          json:
            phoneNumber: "+{{ phoneNumber }}"
            email: "test{{ $randomInt(1, 10000) }}@example.com"
            name: "Test User {{ $randomInt(1, 1000) }}"
          expect:
            - statusCode: [200, 201]

  # API documentation access
  - name: "API Documentation"
    weight: 5
    flow:
      - get:
          url: "/api/docs"
          expect:
            - statusCode: 200

# Custom functions for data generation
processor: "./load-testing/processor.js"

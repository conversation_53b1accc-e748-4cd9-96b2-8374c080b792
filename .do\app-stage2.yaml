name: sanad-paim-stage2
region: blr1

services:
  - name: api-stage2
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
      deploy_on_push: false
    
    # NestJS build configuration
    build_command: cp package-stage2.json package.json && cp tsconfig-stage2.json tsconfig.json && cp nest-cli-stage2.json nest-cli.json && cp -r src-stage2 src && npm install && npm run build
    run_command: npm run start:prod
    
    # Environment configuration
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    
    # Health check configuration
    health_check:
      http_path: /health
      initial_delay_seconds: 45
      period_seconds: 10
      timeout_seconds: 5
      success_threshold: 1
      failure_threshold: 3
    
    # HTTP configuration
    http_port: 3000
    
    # Environment variables
    envs:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "3000"

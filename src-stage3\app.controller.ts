import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getRoot() {
    return {
      message: 'Welcome to Sanad PAIM - Stage 3',
      description: 'Business logic dependencies integrated',
      stage: 'Stage 3: Business Logic Dependencies',
      framework: 'NestJS',
      integrations: ['OpenAI', 'Twilio', 'Configuration'],
      endpoints: {
        root: '/',
        health: '/health',
        status: '/status',
        aiTest: '/ai/test',
        twilioTest: '/twilio/test'
      },
      timestamp: new Date().toISOString()
    };
  }

  @Get('status')
  getStatus() {
    return this.appService.getStatus();
  }
}

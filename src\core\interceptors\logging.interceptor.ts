import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import { LoggerService } from '../services/logger.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private logger: LoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    const { method, url, headers } = request;

    const startTime = Date.now();

    // Extract user ID from request if available
    const userId =
      (request as any).user?.id || headers['x-user-id'] || 'anonymous';

    this.logger.log(`Incoming Request: ${method} ${url}`, 'LoggingInterceptor');

    return next.handle().pipe(
      tap({
        next: () => {
          const duration = Date.now() - startTime;
          const { statusCode } = response;

          this.logger.logApiCall(url, method, statusCode, duration, userId);

          // Log additional details for webhook endpoints
          if (url.includes('/webhook')) {
            this.logger.log(
              `Webhook processed: ${method} ${url} - ${statusCode} - ${duration}ms`,
              'WebhookInterceptor',
            );
          }
        },
        error: (error) => {
          const duration = Date.now() - startTime;
          const statusCode = error.status || 500;

          this.logger.error(
            `Request failed: ${method} ${url} - ${statusCode} - ${duration}ms - ${error.message}`,
            error.stack,
            'LoggingInterceptor',
          );

          this.logger.logApiCall(url, method, statusCode, duration, userId);
        },
      }),
    );
  }
}

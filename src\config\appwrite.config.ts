import { registerAs } from '@nestjs/config';

export default registerAs('appwrite', () => ({
  // Support both self-hosted and cloud Appwrite
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://appwrite.sanad.kanousai.com/v1',
  projectId: process.env.APPWRITE_PROJECT_ID,
  apiKey: process.env.APPWRITE_API_KEY,
  databaseId: process.env.APPWRITE_DATABASE_ID || 'sanad-production',

  // Collection IDs
  collections: {
    users: process.env.APPWRITE_USERS_COLLECTION_ID || 'users',
    registrations:
      process.env.APPWRITE_REGISTRATIONS_COLLECTION_ID || 'registrations',
    whitelist: process.env.APPWRITE_WHITELIST_COLLECTION_ID || 'whitelist',
    sessions: process.env.APPWRITE_SESSIONS_COLLECTION_ID || 'sessions',
    memories: process.env.APPWRITE_MEMORIES_COLLECTION_ID || 'memories',
    reminders: process.env.APPWRITE_REMINDERS_COLLECTION_ID || 'reminders',
    conversations:
      process.env.APPWRITE_CONVERSATIONS_COLLECTION_ID || 'conversations',
    emails: process.env.APPWRITE_EMAILS_COLLECTION_ID || 'emails',
    emailConfirmations:
      process.env.APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID ||
      'email_confirmations',
  },

  // Storage
  storage: {
    bucketId: process.env.APPWRITE_STORAGE_BUCKET_ID || 'sanad-files',
  },
}));

# Performance Baseline Configuration
# Defines expected performance characteristics for PAIM

baseline:
  # Response time thresholds (in milliseconds)
  response_times:
    health_check:
      p50: 50    # 50th percentile
      p95: 200   # 95th percentile
      p99: 500   # 99th percentile
      max: 1000  # Maximum acceptable
    
    api_endpoints:
      p50: 100
      p95: 500
      p99: 1000
      max: 2000
    
    webhook_processing:
      p50: 200
      p95: 800
      p99: 1500
      max: 3000
    
    user_registration:
      p50: 300
      p95: 1000
      p99: 2000
      max: 5000

  # Throughput thresholds (requests per second)
  throughput:
    minimum: 50      # Minimum acceptable RPS
    target: 100      # Target RPS under normal load
    peak: 200        # Peak RPS the system should handle
    maximum: 500     # Maximum RPS before degradation

  # Error rate thresholds (percentage)
  error_rates:
    warning: 1       # Warning threshold
    critical: 5      # Critical threshold
    maximum: 10      # Maximum acceptable

  # Resource utilization thresholds (percentage)
  resources:
    cpu:
      warning: 70
      critical: 85
      maximum: 95
    
    memory:
      warning: 70
      critical: 85
      maximum: 95
    
    disk:
      warning: 80
      critical: 90
      maximum: 95

  # Concurrent users
  concurrency:
    minimum: 10      # Minimum concurrent users
    normal: 50       # Normal load
    peak: 100        # Peak load
    stress: 200      # Stress test load

# Test scenarios configuration
scenarios:
  smoke_test:
    duration: 60
    arrival_rate: 1
    description: "Basic functionality verification"
    
  load_test:
    duration: 300
    arrival_rate: 10
    description: "Normal load testing"
    
  stress_test:
    duration: 180
    arrival_rate: 25
    ramp_to: 50
    description: "Stress testing with increasing load"
    
  spike_test:
    duration: 120
    arrival_rate: 100
    description: "Sudden spike in traffic"
    
  endurance_test:
    duration: 1800
    arrival_rate: 5
    description: "Long-running stability test"

# Performance regression detection
regression:
  # Percentage increase that triggers a regression alert
  response_time_threshold: 20
  throughput_threshold: 15
  error_rate_threshold: 5
  
  # Number of consecutive failed tests to trigger alert
  consecutive_failures: 3
  
  # Baseline comparison window (in days)
  comparison_window: 7

# Monitoring integration
monitoring:
  prometheus:
    enabled: true
    pushgateway: "http://localhost:9091"
    metrics_prefix: "paim_performance_"
  
  grafana:
    enabled: true
    dashboard_id: "paim-performance"
  
  alerting:
    enabled: true
    channels:
      - slack
      - email
      - webhook

# Environment-specific configurations
environments:
  development:
    scale_factor: 0.5  # Reduce load for dev environment
    
  staging:
    scale_factor: 0.8  # Slightly reduced load for staging
    
  production:
    scale_factor: 1.0  # Full load for production testing

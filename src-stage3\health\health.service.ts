import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class HealthService {
  constructor(private configService: ConfigService) {}

  getHealth() {
    return {
      status: 'OK',
      message: 'Sanad PAIM Stage 3 - Business Logic Running',
      timestamp: new Date().toISOString(),
      version: '0.1.0-stage3',
      stage: 'Stage 3: Business Logic Dependencies',
      framework: {
        name: 'NestJS',
        status: 'Operational',
        modules: ['AppModule', 'HealthModule', 'OpenaiModule', 'TwilioModule']
      },
      uptime: process.uptime(),
      environment: this.configService.get('NODE_ENV', 'development'),
      checks: {
        nestjs: 'OK',
        express: 'OK',
        typescript: 'OK',
        configuration: 'OK',
        validation: 'OK',
        dependencies: 'OK'
      },
      integrations: {
        openai: this.configService.get('OPENAI_API_KEY') ? 'Configured' : 'Not Configured',
        twilio: this.configService.get('TWILIO_ACCOUNT_SID') ? 'Configured' : 'Not Configured'
      }
    };
  }
}

# PAIM Operations Runbook

This runbook provides step-by-step procedures for common operational tasks, maintenance activities, and emergency responses for the PAIM system.

## 📋 Table of Contents

- [Daily Operations](#daily-operations)
- [Weekly Maintenance](#weekly-maintenance)
- [Monthly Tasks](#monthly-tasks)
- [Emergency Procedures](#emergency-procedures)
- [Scaling Operations](#scaling-operations)
- [Security Operations](#security-operations)
- [Backup Operations](#backup-operations)
- [Performance Optimization](#performance-optimization)

## 📅 Daily Operations

### Morning Health Check (9:00 AM UTC)

1. **System Status Verification**
   ```bash
   # Check application health
   curl -f https://sanad-paim.ondigitalocean.app/health/detailed
   
   # Verify monitoring stack
   curl -s http://localhost:9090/-/healthy
   curl -s http://localhost:9093/-/healthy
   
   # Check Redis cache
   docker exec paim-redis redis-cli ping
   ```

2. **Review Overnight Alerts**
   ```bash
   # Check AlertManager for active alerts
   curl -s http://localhost:9093/api/v1/alerts | jq '.data[] | select(.status.state == "active")'
   
   # Review Grafana dashboards
   # - Application Overview
   # - System Health
   # - Business Metrics
   ```

3. **Log Analysis**
   ```bash
   # Check for errors in the last 24 hours
   doctl apps logs <app-id> --since 24h | grep -E "(ERROR|FATAL|CRITICAL)"
   
   # Review security events
   grep -E "(SECURITY|AUTH|SUSPICIOUS)" logs/app.log | tail -50
   ```

4. **Performance Review**
   ```bash
   # Check response times
   curl -s http://localhost:9090/api/v1/query?query=histogram_quantile\(0.95,rate\(http_request_duration_ms_bucket\[24h\]\)\)
   
   # Review error rates
   curl -s http://localhost:9090/api/v1/query?query=rate\(http_requests_total\{status=~\"5..\"\}\[24h\]\)
   ```

### Evening Summary (6:00 PM UTC)

1. **Daily Metrics Summary**
   ```bash
   # Generate daily report
   node scripts/generate-daily-report.js
   
   # Check backup status
   ls -la backups/ | grep $(date +%Y%m%d)
   
   # Verify backup integrity
   npm run backup:verify backups/latest
   ```

2. **Capacity Planning**
   ```bash
   # Check resource utilization
   doctl apps get <app-id>
   
   # Review scaling metrics
   curl -s http://localhost:9090/api/v1/query?query=avg_over_time\(process_resident_memory_bytes\[24h\]\)
   ```

## 🔧 Weekly Maintenance

### Monday: System Updates and Security

1. **Security Updates**
   ```bash
   # Update dependencies
   npm audit
   npm update
   
   # Security scan
   npm run security:scan
   
   # Check for CVEs
   npm audit --audit-level=moderate
   ```

2. **System Updates**
   ```bash
   # Update monitoring stack
   docker-compose -f docker-compose.monitoring.yml pull
   docker-compose -f docker-compose.monitoring.yml up -d
   
   # Update Redis
   docker-compose -f docker-compose.redis.yml pull
   docker-compose -f docker-compose.redis.yml up -d
   ```

### Wednesday: Performance Review

1. **Performance Testing**
   ```bash
   # Run performance tests
   npm run perf:test
   
   # Load testing
   npm run perf:load
   
   # Analyze results
   node scripts/analyze-performance.js
   ```

2. **Optimization Review**
   ```bash
   # Check slow queries
   node scripts/analyze-slow-queries.js
   
   # Review cache hit rates
   curl -s http://localhost:9121/metrics | grep cache_hit_rate
   
   # Optimize if needed
   node scripts/optimize-performance.js
   ```

### Friday: Backup and Recovery Testing

1. **Backup Verification**
   ```bash
   # Test backup process
   npm run backup:full
   
   # Verify backup integrity
   npm run backup:verify backups/latest
   
   # Test restore process (staging)
   npm run restore:test staging
   ```

2. **Disaster Recovery Drill**
   ```bash
   # Monthly DR drill (first Friday of month)
   if [ $(date +%d) -le 7 ]; then
     bash scripts/disaster-recovery-drill.sh
   fi
   ```

## 📆 Monthly Tasks

### First Monday: Comprehensive Review

1. **Security Audit**
   ```bash
   # Full security scan
   npm run security:full-scan
   
   # Review access logs
   node scripts/analyze-access-patterns.js
   
   # Update security policies
   bash scripts/update-security-policies.sh
   ```

2. **Performance Analysis**
   ```bash
   # Monthly performance report
   node scripts/generate-monthly-report.js
   
   # Capacity planning review
   node scripts/capacity-planning.js
   
   # Cost optimization
   node scripts/cost-analysis.js
   ```

### Second Monday: Infrastructure Review

1. **Infrastructure Audit**
   ```bash
   # Review Digital Ocean resources
   doctl compute droplet list
   doctl apps list
   doctl databases list
   
   # Check resource utilization
   node scripts/infrastructure-audit.js
   ```

2. **Configuration Review**
   ```bash
   # Review environment variables
   npm run validate:env
   
   # Check configuration drift
   node scripts/config-drift-check.js
   
   # Update documentation
   bash scripts/update-docs.sh
   ```

### Third Monday: Dependency Management

1. **Dependency Updates**
   ```bash
   # Check for updates
   npm outdated
   
   # Update dependencies
   npm update
   
   # Test after updates
   npm run test:quality
   ```

2. **License Compliance**
   ```bash
   # Check license compliance
   npx license-checker
   
   # Generate license report
   node scripts/generate-license-report.js
   ```

## 🚨 Emergency Procedures

### Application Down (P1 Incident)

1. **Immediate Response (0-5 minutes)**
   ```bash
   # Check application status
   curl -f https://sanad-paim.ondigitalocean.app/health
   
   # Check Digital Ocean status
   doctl apps get <app-id>
   
   # Check recent deployments
   doctl apps list-deployments <app-id> | head -5
   ```

2. **Quick Recovery Attempts (5-15 minutes)**
   ```bash
   # Restart application
   doctl apps create-deployment <app-id>
   
   # Check logs for errors
   doctl apps logs <app-id> --follow
   
   # Rollback if recent deployment
   npm run deploy:rollback
   ```

3. **Escalation (15+ minutes)**
   ```bash
   # Notify team
   bash scripts/notify-incident.sh "Application Down" "P1"
   
   # Create incident ticket
   node scripts/create-incident.js --severity=P1 --title="Application Down"
   
   # Activate disaster recovery if needed
   bash scripts/activate-disaster-recovery.sh
   ```

### Database Issues (P1 Incident)

1. **Assessment**
   ```bash
   # Check Appwrite status
   curl -s https://cloud.appwrite.io/v1/health
   
   # Test database connectivity
   node scripts/test-appwrite-connection.js
   
   # Check for data corruption
   node scripts/verify-data-integrity.js
   ```

2. **Recovery**
   ```bash
   # Restore from backup if needed
   node scripts/restore-appwrite.js backups/latest
   
   # Verify data integrity
   node scripts/verify-data-integrity.js
   
   # Test application functionality
   npm run test:integration
   ```

### Security Incident (P1 Incident)

1. **Immediate Containment**
   ```bash
   # Block suspicious IPs
   bash scripts/block-suspicious-ips.sh
   
   # Rotate API keys
   bash scripts/rotate-api-keys.sh
   
   # Enable additional logging
   bash scripts/enable-security-logging.sh
   ```

2. **Investigation**
   ```bash
   # Analyze security logs
   node scripts/analyze-security-logs.js
   
   # Check for data breaches
   node scripts/check-data-breach.js
   
   # Generate security report
   node scripts/generate-security-report.js
   ```

## 📈 Scaling Operations

### Horizontal Scaling

1. **Scale Up**
   ```bash
   # Increase instance count
   doctl apps spec get <app-id> > app.yaml
   # Edit instance_count in app.yaml
   doctl apps update <app-id> --spec app.yaml
   
   # Monitor scaling
   doctl apps get <app-id>
   ```

2. **Scale Down**
   ```bash
   # Decrease instance count during low traffic
   # Edit app.yaml and update
   doctl apps update <app-id> --spec app.yaml
   
   # Verify performance
   npm run perf:smoke
   ```

### Vertical Scaling

1. **Increase Resources**
   ```bash
   # Change instance size
   # Edit instance_size_slug in app.yaml
   # Options: basic-xxs, basic-xs, basic-s, basic-m
   doctl apps update <app-id> --spec app.yaml
   ```

2. **Monitor Impact**
   ```bash
   # Check resource utilization
   curl -s http://localhost:9090/api/v1/query?query=process_resident_memory_bytes
   
   # Monitor performance
   npm run perf:test
   ```

## 🔒 Security Operations

### Daily Security Tasks

1. **Log Review**
   ```bash
   # Check authentication logs
   grep "auth" logs/app.log | grep -E "(FAIL|ERROR)" | tail -50
   
   # Review access patterns
   node scripts/analyze-access-patterns.js
   
   # Check for brute force attempts
   grep "rate_limit" logs/app.log | tail -20
   ```

2. **Threat Detection**
   ```bash
   # Check for suspicious activity
   node scripts/detect-threats.js
   
   # Analyze IP patterns
   node scripts/analyze-ip-patterns.js
   
   # Check for known bad IPs
   bash scripts/check-threat-intelligence.sh
   ```

### Weekly Security Tasks

1. **Vulnerability Scanning**
   ```bash
   # Scan dependencies
   npm audit --audit-level=moderate
   
   # Security scan
   npm run security:scan
   
   # Check for exposed secrets
   bash scripts/check-secrets.sh
   ```

2. **Access Review**
   ```bash
   # Review user access
   node scripts/review-user-access.js
   
   # Check API key usage
   node scripts/analyze-api-usage.js
   
   # Update access controls
   bash scripts/update-access-controls.sh
   ```

## 💾 Backup Operations

### Daily Backup Tasks

1. **Automated Backup Verification**
   ```bash
   # Check backup completion
   ls -la backups/ | grep $(date +%Y%m%d)
   
   # Verify backup integrity
   npm run backup:verify backups/latest
   
   # Check backup size
   du -sh backups/$(date +%Y%m%d)*
   ```

2. **Backup Monitoring**
   ```bash
   # Check backup alerts
   curl -s http://localhost:9093/api/v1/alerts | grep backup
   
   # Monitor backup storage
   node scripts/monitor-backup-storage.js
   ```

### Weekly Backup Tasks

1. **Restore Testing**
   ```bash
   # Test restore process in staging
   npm run restore:test staging backups/latest
   
   # Verify restored data
   node scripts/verify-restored-data.js staging
   
   # Document any issues
   node scripts/document-restore-test.js
   ```

2. **Backup Cleanup**
   ```bash
   # Clean old backups
   npm run backup:cleanup
   
   # Verify retention policy
   node scripts/verify-retention.js
   
   # Update backup documentation
   bash scripts/update-backup-docs.sh
   ```

## ⚡ Performance Optimization

### Daily Performance Tasks

1. **Performance Monitoring**
   ```bash
   # Check response times
   curl -s http://localhost:9090/api/v1/query?query=histogram_quantile\(0.95,rate\(http_request_duration_ms_bucket\[1h\]\)\)
   
   # Monitor error rates
   curl -s http://localhost:9090/api/v1/query?query=rate\(http_requests_total\{status=~\"5..\"\}\[1h\]\)
   
   # Check cache performance
   curl -s http://localhost:9121/metrics | grep cache_hit_rate
   ```

2. **Resource Optimization**
   ```bash
   # Check memory usage
   curl -s http://localhost:9090/api/v1/query?query=process_resident_memory_bytes
   
   # Monitor CPU usage
   curl -s http://localhost:9090/api/v1/query?query=rate\(process_cpu_seconds_total\[5m\]\)
   
   # Optimize if needed
   node scripts/optimize-resources.js
   ```

### Weekly Performance Tasks

1. **Performance Testing**
   ```bash
   # Run comprehensive performance tests
   npm run perf:test
   
   # Load testing
   npm run perf:load
   
   # Stress testing
   npm run perf:stress
   ```

2. **Performance Analysis**
   ```bash
   # Analyze performance trends
   node scripts/analyze-performance-trends.js
   
   # Identify bottlenecks
   node scripts/identify-bottlenecks.js
   
   # Generate optimization recommendations
   node scripts/generate-optimization-recommendations.js
   ```

---

**Document Version**: 1.0  
**Last Updated**: [Date]  
**Next Review**: [Date + 1 month]  
**Maintained by**: DevOps Team

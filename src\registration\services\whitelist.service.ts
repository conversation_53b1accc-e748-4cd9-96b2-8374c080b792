import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  IWhitelistUser,
  IRegistrationRequest,
} from '../../interfaces/registration.interface';

@Injectable()
export class WhitelistService {
  private readonly logger = new Logger(WhitelistService.name);
  private readonly whitelistUsers = new Map<string, IWhitelistUser>();
  private readonly emailToWhitelistMap = new Map<string, string>();

  async addToWhitelist(
    registrationRequest: IRegistrationRequest,
  ): Promise<IWhitelistUser> {
    const id = uuidv4();
    const now = new Date();

    const whitelistUser: IWhitelistUser = {
      id,
      email: registrationRequest.email,
      firstName: registrationRequest.firstName,
      lastName: registrationRequest.lastName,
      phoneNumber: registrationRequest.phoneNumber,
      registrationRequestId: registrationRequest.id,
      isActive: true,
      isEarlyAdopter: registrationRequest.isEarlyAdopter,
      earlyAdopterSelectedAt: registrationRequest.earlyAdopterSelectedAt,
      earlyAdopterSelectedBy: registrationRequest.earlyAdopterSelectedBy,
      createdAt: now,
      updatedAt: now,
    };

    this.whitelistUsers.set(id, whitelistUser);
    this.emailToWhitelistMap.set(whitelistUser.email, id);

    this.logger.log(`Added to whitelist: ${whitelistUser.email}`);
    return whitelistUser;
  }

  async findByEmail(email: string): Promise<IWhitelistUser | null> {
    const whitelistId = this.emailToWhitelistMap.get(
      email.toLowerCase().trim(),
    );
    if (!whitelistId) {
      return null;
    }
    return this.whitelistUsers.get(whitelistId) || null;
  }

  async findByPhoneNumber(phoneNumber: string): Promise<IWhitelistUser | null> {
    const normalizedPhone = this.normalizePhoneNumber(phoneNumber);

    for (const user of this.whitelistUsers.values()) {
      if (
        user.phoneNumber &&
        this.normalizePhoneNumber(user.phoneNumber) === normalizedPhone
      ) {
        return user;
      }
    }

    return null;
  }

  async isWhitelisted(email: string): Promise<boolean> {
    const user = await this.findByEmail(email);
    return user !== null && user.isActive;
  }

  async isEarlyAdopter(email: string): Promise<boolean> {
    const user = await this.findByEmail(email);
    return user !== null && user.isActive && user.isEarlyAdopter;
  }

  async markAsEarlyAdopter(
    email: string,
    adminUserId: string,
  ): Promise<IWhitelistUser | null> {
    const user = await this.findByEmail(email);

    if (!user) {
      return null;
    }

    user.isEarlyAdopter = true;
    user.earlyAdopterSelectedAt = new Date();
    user.earlyAdopterSelectedBy = adminUserId;
    user.updatedAt = new Date();

    this.whitelistUsers.set(user.id, user);

    this.logger.log(
      `Marked as early adopter: ${email} by admin: ${adminUserId}`,
    );
    return user;
  }

  async linkWhatsAppUser(
    email: string,
    whatsappUserId: string,
  ): Promise<IWhitelistUser | null> {
    const user = await this.findByEmail(email);

    if (!user) {
      return null;
    }

    user.whatsappUserId = whatsappUserId;
    user.updatedAt = new Date();

    this.whitelistUsers.set(user.id, user);

    this.logger.log(
      `Linked WhatsApp user: ${whatsappUserId} to whitelist user: ${email}`,
    );
    return user;
  }

  async getAllWhitelistUsers(): Promise<IWhitelistUser[]> {
    return Array.from(this.whitelistUsers.values()).filter(
      (user) => user.isActive,
    );
  }

  async getEarlyAdopters(): Promise<IWhitelistUser[]> {
    return Array.from(this.whitelistUsers.values()).filter(
      (user) => user.isActive && user.isEarlyAdopter,
    );
  }

  async getWhitelistStats() {
    const allUsers = Array.from(this.whitelistUsers.values()).filter(
      (user) => user.isActive,
    );
    const earlyAdopters = allUsers.filter((user) => user.isEarlyAdopter);
    const linkedUsers = allUsers.filter((user) => user.whatsappUserId);

    return {
      totalWhitelisted: allUsers.length,
      earlyAdopters: earlyAdopters.length,
      linkedToWhatsApp: linkedUsers.length,
      pendingActivation: allUsers.length - linkedUsers.length,
    };
  }

  private normalizePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters and normalize
    const digits = phoneNumber.replace(/\D/g, '');

    // Handle different formats (e.g., +1234567890, whatsapp:+1234567890)
    if (digits.length === 11 && digits.startsWith('1')) {
      return digits; // US number with country code
    } else if (digits.length === 10) {
      return '1' + digits; // US number without country code
    }

    return digits; // International number
  }

  // Method to check if a phone number is whitelisted (for WhatsApp integration)
  async isPhoneNumberWhitelisted(phoneNumber: string): Promise<boolean> {
    const user = await this.findByPhoneNumber(phoneNumber);
    return user !== null && user.isActive;
  }

  // Method to check if a phone number belongs to an early adopter
  async isPhoneNumberEarlyAdopter(phoneNumber: string): Promise<boolean> {
    const user = await this.findByPhoneNumber(phoneNumber);
    return user !== null && user.isActive && user.isEarlyAdopter;
  }
}

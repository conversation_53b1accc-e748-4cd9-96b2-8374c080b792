import { registerAs } from '@nestjs/config';

export default registerAs('openai', () => ({
  apiKey: process.env.OPENAI_API_KEY,
  model: process.env.OPENAI_MODEL || 'gpt-4',
  maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS, 10) || 1000,
  temperature: parseFloat(process.env.OPENAI_TEMPERATURE) || 0.7,
  whisperModel: process.env.WHISPER_MODEL || 'whisper-1',
  ttsVoice: process.env.TTS_VOICE || 'alloy',
  ttsModel: process.env.TTS_MODEL || 'tts-1',
}));

#!/usr/bin/env node

/**
 * Quick test for PAIM server
 */

const http = require('http');

function testEndpoint(path) {
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:3000${path}`, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log(`✅ ${path} - Status: ${res.statusCode}`);
        try {
          const json = JSON.parse(data);
          console.log(`   Response: ${json.status || json.message || 'OK'}`);
        } catch (e) {
          console.log(`   Response: ${data.substring(0, 100)}...`);
        }
        resolve(true);
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ ${path} - Error: ${err.message}`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log(`⏰ ${path} - Timeout`);
      req.destroy();
      resolve(false);
    });
  });
}

async function main() {
  console.log('🧪 Quick PAIM Server Test\n');
  
  const endpoints = [
    '/',
    '/health',
    '/health/detailed',
    '/api/v1/status',
    '/api/docs'
  ];
  
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
  }
  
  console.log('\n🎉 Test completed!');
  console.log('🌐 Server is running at: http://localhost:3000');
  console.log('📊 Health check: http://localhost:3000/health');
}

main().catch(console.error);

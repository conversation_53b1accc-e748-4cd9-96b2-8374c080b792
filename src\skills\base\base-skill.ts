import { Injectable } from '@nestjs/common';
import {
  ISkill,
  ISkillContext,
  ISkillResponse,
  SkillCategory,
  MessageType,
} from '../../interfaces';
import { LoggerService } from '../../core';

@Injectable()
export abstract class BaseSkill implements ISkill {
  abstract readonly id: string;
  abstract readonly name: string;
  abstract readonly description: string;
  abstract readonly triggers: string[];
  abstract readonly category: SkillCategory;

  public readonly priority: number = 1;
  public readonly enabled: boolean = true;

  constructor(protected logger: LoggerService) {}

  abstract execute(
    input: string,
    context: ISkillContext,
  ): Promise<ISkillResponse>;

  /**
   * Check if this skill can handle the given input
   */
  canHandle(input: string): boolean {
    const normalizedInput = input.toLowerCase().trim();
    return this.triggers.some((trigger) =>
      normalizedInput.includes(trigger.toLowerCase()),
    );
  }

  /**
   * Get skill metadata
   */
  getMetadata() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      triggers: this.triggers,
      category: this.category,
      priority: this.priority,
      enabled: this.enabled,
    };
  }

  /**
   * Validate input before processing
   */
  protected validateInput(input: string): boolean {
    return input && input.trim().length > 0;
  }

  /**
   * Create a standard skill response
   */
  protected createResponse(
    content: string,
    type: MessageType = MessageType.TEXT,
    tone?: string,
    metadata?: Record<string, any>,
  ): ISkillResponse {
    return {
      content,
      type,
      tone,
      metadata,
      shouldEndConversation: false,
    };
  }

  /**
   * Create an error response
   */
  protected createErrorResponse(
    error: string,
    metadata?: Record<string, any>,
  ): ISkillResponse {
    return {
      content: `I encountered an issue: ${error}`,
      type: MessageType.TEXT,
      metadata: { error: true, ...metadata },
      shouldEndConversation: false,
    };
  }

  /**
   * Log skill execution
   */
  protected logExecution(
    input: string,
    output: string,
    userId: string,
    duration: number,
    success: boolean = true,
  ): void {
    this.logger.logSkillExecution(this.id, userId, input, output, duration);

    if (!success) {
      this.logger.error(
        `Skill execution failed: ${this.id}`,
        undefined,
        'BaseSkill',
      );
    }
  }

  /**
   * Extract entities from input (basic implementation)
   */
  protected extractEntities(input: string): Record<string, string[]> {
    const entities: Record<string, string[]> = {
      mentions: [],
      hashtags: [],
      urls: [],
      emails: [],
    };

    // Extract mentions (@username)
    const mentions = input.match(/@\w+/g);
    if (mentions) {
      entities.mentions = mentions.map((m) => m.substring(1));
    }

    // Extract hashtags (#tag)
    const hashtags = input.match(/#\w+/g);
    if (hashtags) {
      entities.hashtags = hashtags.map((h) => h.substring(1));
    }

    // Extract URLs
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const urls = input.match(urlRegex);
    if (urls) {
      entities.urls = urls;
    }

    // Extract emails
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emails = input.match(emailRegex);
    if (emails) {
      entities.emails = emails;
    }

    return entities;
  }

  /**
   * Clean and normalize input text
   */
  protected normalizeInput(input: string): string {
    return input
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[^\w\s@#.-]/g, '') // Remove special characters except @, #, ., -
      .toLowerCase();
  }

  /**
   * Check if skill requires authentication
   */
  protected requiresAuth(): boolean {
    return false;
  }

  /**
   * Check if skill requires specific permissions
   */
  protected getRequiredPermissions(): string[] {
    return [];
  }
}

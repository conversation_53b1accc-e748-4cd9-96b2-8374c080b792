import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ErrorHandlerService, PaimError, SkillExecutionError, ValidationError } from './error-handler.service';
import { LoggerService } from './logger.service';

describe('ErrorHandlerService', () => {
  let service: ErrorHandlerService;
  let loggerService: jest.Mocked<LoggerService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockLoggerService = {
      error: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ErrorHandlerService,
        { provide: LoggerService, useValue: mockLoggerService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<ErrorHandlerService>(ErrorHandlerService);
    loggerService = module.get(LoggerService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleError', () => {
    it('should return PaimError as-is if already a PaimError', () => {
      const paimError = new PaimError('Test error', 'TEST_ERROR', 400);
      const result = service.handleError(paimError, 'TestContext', 'user123');

      expect(result).toBe(paimError);
      expect(loggerService.error).toHaveBeenCalledWith(
        'Test error',
        paimError.stack,
        'TestContext',
      );
    });

    it('should convert generic Error to PaimError', () => {
      const genericError = new Error('Generic error');
      const result = service.handleError(genericError, 'TestContext', 'user123');

      expect(result).toBeInstanceOf(PaimError);
      expect(result.message).toBe('Generic error');
      expect(result.code).toBe('INTERNAL_ERROR');
      expect(result.statusCode).toBe(500);
      expect(result.context).toEqual({
        originalError: 'Error',
        context: 'TestContext',
        userId: 'user123',
      });
    });

    it('should handle errors without message', () => {
      const errorWithoutMessage = new Error();
      const result = service.handleError(errorWithoutMessage);

      expect(result.message).toBe('An unexpected error occurred');
    });
  });

  describe('createUserFriendlyMessage', () => {
    beforeEach(() => {
      configService.get.mockReturnValue(false); // DETAILED_ERRORS = false
    });

    it('should return detailed message when DETAILED_ERRORS is true', () => {
      configService.get.mockReturnValue(true);
      const error = new PaimError('Test error', 'TEST_ERROR', 400);

      const result = service.createUserFriendlyMessage(error);

      expect(result).toBe('Test error (Code: TEST_ERROR)');
    });

    it('should return user-friendly message for USER_NOT_FOUND', () => {
      const error = new PaimError('User not found', 'USER_NOT_FOUND', 404);

      const result = service.createUserFriendlyMessage(error);

      expect(result).toBe("I couldn't find your user profile. Please try the onboarding process again.");
    });

    it('should return user-friendly message for SKILL_EXECUTION_ERROR', () => {
      const error = new PaimError('Skill failed', 'SKILL_EXECUTION_ERROR', 500);

      const result = service.createUserFriendlyMessage(error);

      expect(result).toBe("I encountered an issue while processing your request. Please try again.");
    });

    it('should return user-friendly message for VALIDATION_ERROR', () => {
      const error = new PaimError('Validation failed', 'VALIDATION_ERROR', 400);

      const result = service.createUserFriendlyMessage(error);

      expect(result).toBe("There was an issue with the information provided. Please check and try again.");
    });

    it('should return user-friendly message for EXTERNAL_SERVICE_ERROR', () => {
      const error = new PaimError('Service unavailable', 'EXTERNAL_SERVICE_ERROR', 502);

      const result = service.createUserFriendlyMessage(error);

      expect(result).toBe("I'm having trouble connecting to external services. Please try again later.");
    });

    it('should return user-friendly message for CONFIGURATION_ERROR', () => {
      const error = new PaimError('Config missing', 'CONFIGURATION_ERROR', 500);

      const result = service.createUserFriendlyMessage(error);

      expect(result).toBe("There's a configuration issue. Please contact support.");
    });

    it('should return generic message for unknown error codes', () => {
      const error = new PaimError('Unknown error', 'UNKNOWN_ERROR', 500);

      const result = service.createUserFriendlyMessage(error);

      expect(result).toBe("I encountered an unexpected issue. Please try again or contact support.");
    });
  });

  describe('isRetryableError', () => {
    it('should return true for retryable error codes', () => {
      const retryableCodes = [
        'EXTERNAL_SERVICE_ERROR',
        'TIMEOUT_ERROR',
        'RATE_LIMIT_ERROR',
        'NETWORK_ERROR',
      ];

      retryableCodes.forEach(code => {
        const error = new PaimError('Test', code, 500);
        expect(service.isRetryableError(error)).toBe(true);
      });
    });

    it('should return false for non-retryable error codes', () => {
      const nonRetryableCodes = [
        'VALIDATION_ERROR',
        'USER_NOT_FOUND',
        'CONFIGURATION_ERROR',
      ];

      nonRetryableCodes.forEach(code => {
        const error = new PaimError('Test', code, 500);
        expect(service.isRetryableError(error)).toBe(false);
      });
    });
  });

  describe('shouldNotifyUser', () => {
    it('should return false for silent error codes', () => {
      const silentCodes = ['VALIDATION_ERROR', 'USER_NOT_FOUND'];

      silentCodes.forEach(code => {
        const error = new PaimError('Test', code, 500);
        expect(service.shouldNotifyUser(error)).toBe(false);
      });
    });

    it('should return true for non-silent error codes', () => {
      const nonSilentCodes = [
        'SKILL_EXECUTION_ERROR',
        'EXTERNAL_SERVICE_ERROR',
        'CONFIGURATION_ERROR',
      ];

      nonSilentCodes.forEach(code => {
        const error = new PaimError('Test', code, 500);
        expect(service.shouldNotifyUser(error)).toBe(true);
      });
    });
  });
});

describe('SkillExecutionError', () => {
  it('should create error with correct properties', () => {
    const originalError = new Error('Original error');
    const error = new SkillExecutionError('test-skill', originalError, { extra: 'data' });

    expect(error.message).toBe('Skill execution failed: Original error');
    expect(error.code).toBe('SKILL_EXECUTION_ERROR');
    expect(error.statusCode).toBe(500);
    expect(error.context).toEqual({
      skillId: 'test-skill',
      originalError: 'Original error',
      extra: 'data',
    });
  });
});

describe('ValidationError', () => {
  it('should create error with correct properties', () => {
    const error = new ValidationError('email', 'invalid@', 'Invalid format');

    expect(error.message).toBe('Validation failed for email: Invalid format');
    expect(error.code).toBe('VALIDATION_ERROR');
    expect(error.statusCode).toBe(400);
    expect(error.context).toEqual({
      field: 'email',
      value: 'invalid@',
      reason: 'Invalid format',
    });
  });
});

#!/bin/bash

# =============================================================================
# Self-Hosted Appwrite Setup Script for Digital Ocean
# =============================================================================
# This script sets up a self-hosted Appwrite instance on Digital Ocean

set -e

echo "🚀 Setting up self-hosted Appwrite for Sanad..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DROPLET_NAME="sanad-appwrite"
DROPLET_SIZE="s-2vcpu-4gb"
DROPLET_REGION="nyc1"
DROPLET_IMAGE="docker-20-04"
DOMAIN="appwrite.sanad.kanousai.com"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
        error "doctl CLI not found. Please install it first."
    fi
    
    # Check if doctl is authenticated
    if ! doctl account get &> /dev/null; then
        error "doctl not authenticated. Please run: doctl auth init"
    fi
    
    # Check if Docker Compose file exists
    if [ ! -f "docker-compose.appwrite.yml" ]; then
        error "docker-compose.appwrite.yml not found in current directory"
    fi
    
    # Check if environment file exists
    if [ ! -f ".env.appwrite" ]; then
        error ".env.appwrite not found. Please create it from the template."
    fi
    
    log "✅ Prerequisites check passed"
}

# Generate secure keys
generate_keys() {
    log "Generating secure keys..."
    
    # Generate OpenSSL key
    OPENSSL_KEY=$(openssl rand -base64 32)
    EXECUTOR_SECRET=$(openssl rand -base64 32)
    DB_ROOT_PASS=$(openssl rand -base64 24)
    DB_PASS=$(openssl rand -base64 24)
    
    # Update .env.appwrite file
    sed -i "s/your-32-character-secret-key-here/$OPENSSL_KEY/g" .env.appwrite
    sed -i "s/your-executor-secret-key-here/$EXECUTOR_SECRET/g" .env.appwrite
    sed -i "s/your-strong-root-password-here/$DB_ROOT_PASS/g" .env.appwrite
    sed -i "s/your-strong-db-password-here/$DB_PASS/g" .env.appwrite
    
    log "✅ Secure keys generated and updated in .env.appwrite"
}

# Create Digital Ocean Droplet
create_droplet() {
    log "Creating Digital Ocean Droplet..."
    
    # Check if droplet already exists
    if doctl compute droplet list --format Name | grep -q "$DROPLET_NAME"; then
        warn "Droplet $DROPLET_NAME already exists. Skipping creation."
        return
    fi
    
    # Create droplet
    doctl compute droplet create "$DROPLET_NAME" \
        --size "$DROPLET_SIZE" \
        --image "$DROPLET_IMAGE" \
        --region "$DROPLET_REGION" \
        --ssh-keys $(doctl compute ssh-key list --format ID --no-header | tr '\n' ',') \
        --wait
    
    # Get droplet IP
    DROPLET_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')
    
    log "✅ Droplet created with IP: $DROPLET_IP"
    log "📝 Please update your DNS records:"
    log "   A record: $DOMAIN -> $DROPLET_IP"
    log "   A record: functions.sanad.kanousai.com -> $DROPLET_IP"
}

# Setup Appwrite on the droplet
setup_appwrite() {
    log "Setting up Appwrite on the droplet..."
    
    # Get droplet IP
    DROPLET_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')
    
    if [ -z "$DROPLET_IP" ]; then
        error "Could not find droplet IP. Please check if droplet exists."
    fi
    
    log "Connecting to droplet at $DROPLET_IP..."
    
    # Copy files to droplet
    scp -o StrictHostKeyChecking=no docker-compose.appwrite.yml root@$DROPLET_IP:/root/
    scp -o StrictHostKeyChecking=no .env.appwrite root@$DROPLET_IP:/root/
    
    # Setup Appwrite on droplet
    ssh -o StrictHostKeyChecking=no root@$DROPLET_IP << 'EOF'
        # Update system
        apt-get update
        apt-get install -y curl
        
        # Install Docker Compose if not present
        if ! command -v docker-compose &> /dev/null; then
            curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            chmod +x /usr/local/bin/docker-compose
        fi
        
        # Create appwrite directory
        mkdir -p /opt/appwrite
        cd /opt/appwrite
        
        # Move files
        mv /root/docker-compose.appwrite.yml ./docker-compose.yml
        mv /root/.env.appwrite ./.env
        
        # Start Appwrite
        docker-compose --env-file .env up -d
        
        # Wait for services to start
        sleep 30
        
        # Check if services are running
        docker-compose ps
EOF
    
    log "✅ Appwrite setup completed on droplet"
    log "🌐 Appwrite should be accessible at: https://$DOMAIN"
    log "📱 Console URL: https://$DOMAIN/console"
}

# Setup SSL with Let's Encrypt
setup_ssl() {
    log "Setting up SSL certificates..."
    
    DROPLET_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')
    
    ssh -o StrictHostKeyChecking=no root@$DROPLET_IP << EOF
        # Install certbot
        apt-get update
        apt-get install -y certbot
        
        # Stop Appwrite temporarily
        cd /opt/appwrite
        docker-compose down
        
        # Get SSL certificates
        certbot certonly --standalone -d $DOMAIN -d functions.sanad.kanousai.com --non-interactive --agree-tos --email <EMAIL>
        
        # Start Appwrite again
        docker-compose up -d
EOF
    
    log "✅ SSL certificates configured"
}

# Main execution
main() {
    log "Starting self-hosted Appwrite setup..."
    
    check_prerequisites
    
    # Ask for confirmation
    echo -e "${BLUE}This will create a new Digital Ocean droplet and set up Appwrite.${NC}"
    echo -e "${BLUE}Estimated cost: ~$24/month for the droplet${NC}"
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "Setup cancelled by user"
        exit 0
    fi
    
    generate_keys
    create_droplet
    
    # Wait for user to update DNS
    echo -e "${YELLOW}Please update your DNS records before continuing:${NC}"
    echo "  A record: $DOMAIN -> $(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')"
    echo "  A record: functions.sanad.kanousai.com -> $(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')"
    echo ""
    read -p "Press Enter after updating DNS records..."
    
    setup_appwrite
    setup_ssl
    
    log "🎉 Self-hosted Appwrite setup completed!"
    log ""
    log "📋 Next steps:"
    log "1. Access Appwrite console: https://$DOMAIN/console"
    log "2. Create your first project"
    log "3. Update your application's APPWRITE_ENDPOINT to: https://$DOMAIN/v1"
    log "4. Run the database schema setup script"
    log ""
    log "📝 Important information:"
    log "- Droplet IP: $(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')"
    log "- SSH access: ssh root@$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')"
    log "- Appwrite data is stored in Docker volumes on the droplet"
    log "- Remember to set up regular backups!"
}

# Run main function
main "$@"

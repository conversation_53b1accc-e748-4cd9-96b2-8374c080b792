import { Injectable, OnModuleInit } from '@nestjs/common';

import { ISkill, ISkillRegistry, SkillCategory } from '../../interfaces';
import { LoggerService, ValidationService } from '../../core';
import { SkillConfig, SkillPerformanceMetrics } from '../base';

@Injectable()
export class SkillRegistryService implements ISkillRegistry, OnModuleInit {
  private skills = new Map<string, ISkill>();
  private skillConfigs = new Map<string, SkillConfig>();
  private skillMetrics = new Map<string, SkillPerformanceMetrics>();
  private skillsByCategory = new Map<SkillCategory, ISkill[]>();
  private skillsByTrigger = new Map<string, ISkill[]>();

  constructor(
    private logger: LoggerService,
    private validation: ValidationService,
  ) {}

  async onModuleInit() {
    await this.discoverSkills();
    this.buildIndexes();
    this.logger.log(
      `Skill registry initialized with ${this.skills.size} skills`,
      'SkillRegistry',
    );
  }

  register(skill: ISkill): void {
    if (!this.validation.validateSkillId(skill.id)) {
      throw new Error(`Invalid skill ID: ${skill.id}`);
    }

    if (this.skills.has(skill.id)) {
      this.logger.warn(
        `Skill ${skill.id} is already registered, overwriting`,
        'SkillRegistry',
      );
    }

    this.skills.set(skill.id, skill);
    this.initializeSkillMetrics(skill.id);
    this.updateIndexes(skill);

    this.logger.log(`Registered skill: ${skill.id}`, 'SkillRegistry');
  }

  unregister(skillId: string): void {
    const skill = this.skills.get(skillId);
    if (!skill) {
      this.logger.warn(
        `Skill ${skillId} not found for unregistration`,
        'SkillRegistry',
      );
      return;
    }

    this.skills.delete(skillId);
    this.skillConfigs.delete(skillId);
    this.skillMetrics.delete(skillId);
    this.removeFromIndexes(skill);

    this.logger.log(`Unregistered skill: ${skillId}`, 'SkillRegistry');
  }

  getSkill(skillId: string): ISkill | undefined {
    return this.skills.get(skillId);
  }

  getAllSkills(): ISkill[] {
    return Array.from(this.skills.values());
  }

  getEnabledSkills(): ISkill[] {
    return this.getAllSkills().filter((skill) => {
      const config = this.skillConfigs.get(skill.id);
      return config ? config.enabled : skill.enabled;
    });
  }

  getSkillsByCategory(category: SkillCategory): ISkill[] {
    return this.skillsByCategory.get(category) || [];
  }

  findSkillByTrigger(input: string): ISkill | undefined {
    const normalizedInput = input.toLowerCase().trim();

    // Find all skills that can handle this input
    const candidateSkills: Array<{ skill: ISkill; priority: number }> = [];

    for (const skill of this.getEnabledSkills()) {
      if (
        skill.triggers.some((trigger) =>
          normalizedInput.includes(trigger.toLowerCase()),
        )
      ) {
        candidateSkills.push({
          skill,
          priority: this.getSkillPriority(skill.id),
        });
      }
    }

    // Sort by priority (higher priority first)
    candidateSkills.sort((a, b) => b.priority - a.priority);

    return candidateSkills.length > 0 ? candidateSkills[0].skill : undefined;
  }

  findSkillsByTrigger(input: string): ISkill[] {
    const normalizedInput = input.toLowerCase().trim();
    const matchingSkills: ISkill[] = [];

    for (const skill of this.getEnabledSkills()) {
      if (
        skill.triggers.some((trigger) =>
          normalizedInput.includes(trigger.toLowerCase()),
        )
      ) {
        matchingSkills.push(skill);
      }
    }

    // Sort by priority
    return matchingSkills.sort(
      (a, b) => this.getSkillPriority(b.id) - this.getSkillPriority(a.id),
    );
  }

  configureSkill(skillId: string, config: Partial<SkillConfig>): void {
    const existingConfig = this.skillConfigs.get(skillId) || {
      enabled: true,
      priority: 1,
    };

    const newConfig = { ...existingConfig, ...config };
    this.skillConfigs.set(skillId, newConfig);

    this.logger.log(`Configured skill: ${skillId}`, 'SkillRegistry');
  }

  getSkillConfig(skillId: string): SkillConfig | undefined {
    return this.skillConfigs.get(skillId);
  }

  getSkillMetrics(skillId: string): SkillPerformanceMetrics | undefined {
    return this.skillMetrics.get(skillId);
  }

  updateSkillMetrics(
    skillId: string,
    executionTime: number,
    success: boolean,
    trigger?: string,
  ): void {
    const metrics = this.skillMetrics.get(skillId);
    if (!metrics) return;

    metrics.totalExecutions++;
    if (success) {
      metrics.successfulExecutions++;
    } else {
      metrics.failedExecutions++;
    }

    // Update average execution time
    const totalTime =
      metrics.averageExecutionTime * (metrics.totalExecutions - 1) +
      executionTime;
    metrics.averageExecutionTime = totalTime / metrics.totalExecutions;

    metrics.lastExecuted = new Date();

    // Track popular triggers
    if (trigger) {
      metrics.popularTriggers[trigger] =
        (metrics.popularTriggers[trigger] || 0) + 1;
    }

    this.skillMetrics.set(skillId, metrics);
  }

  getSkillStatistics() {
    const totalSkills = this.skills.size;
    const enabledSkills = this.getEnabledSkills().length;
    const skillsByCategory = new Map<SkillCategory, number>();

    for (const skill of this.getAllSkills()) {
      const count = skillsByCategory.get(skill.category) || 0;
      skillsByCategory.set(skill.category, count + 1);
    }

    return {
      totalSkills,
      enabledSkills,
      disabledSkills: totalSkills - enabledSkills,
      skillsByCategory: Object.fromEntries(skillsByCategory),
      totalExecutions: Array.from(this.skillMetrics.values()).reduce(
        (sum, metrics) => sum + metrics.totalExecutions,
        0,
      ),
    };
  }

  private async discoverSkills(): Promise<void> {
    // This would typically scan for skill modules and auto-register them
    // For now, we'll rely on manual registration through dependency injection
    this.logger.log('Skill discovery completed', 'SkillRegistry');
  }

  private buildIndexes(): void {
    this.skillsByCategory.clear();
    this.skillsByTrigger.clear();

    for (const skill of this.getAllSkills()) {
      this.updateIndexes(skill);
    }
  }

  private updateIndexes(skill: ISkill): void {
    // Update category index
    const categorySkills = this.skillsByCategory.get(skill.category) || [];
    if (!categorySkills.includes(skill)) {
      categorySkills.push(skill);
      this.skillsByCategory.set(skill.category, categorySkills);
    }

    // Update trigger index
    for (const trigger of skill.triggers) {
      const triggerSkills =
        this.skillsByTrigger.get(trigger.toLowerCase()) || [];
      if (!triggerSkills.includes(skill)) {
        triggerSkills.push(skill);
        this.skillsByTrigger.set(trigger.toLowerCase(), triggerSkills);
      }
    }
  }

  private removeFromIndexes(skill: ISkill): void {
    // Remove from category index
    const categorySkills = this.skillsByCategory.get(skill.category);
    if (categorySkills) {
      const index = categorySkills.indexOf(skill);
      if (index > -1) {
        categorySkills.splice(index, 1);
      }
    }

    // Remove from trigger index
    for (const trigger of skill.triggers) {
      const triggerSkills = this.skillsByTrigger.get(trigger.toLowerCase());
      if (triggerSkills) {
        const index = triggerSkills.indexOf(skill);
        if (index > -1) {
          triggerSkills.splice(index, 1);
        }
      }
    }
  }

  private initializeSkillMetrics(skillId: string): void {
    if (!this.skillMetrics.has(skillId)) {
      this.skillMetrics.set(skillId, {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageExecutionTime: 0,
        lastExecuted: new Date(),
        popularTriggers: {},
      });
    }
  }

  private getSkillPriority(skillId: string): number {
    const config = this.skillConfigs.get(skillId);
    if (config && config.priority !== undefined) {
      return config.priority;
    }

    const skill = this.skills.get(skillId);
    return skill ? skill.priority : 1;
  }
}

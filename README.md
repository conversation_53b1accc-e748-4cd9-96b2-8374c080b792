# 🧠 Sanad (Personal AI Manager)

> **Your second brain inside WhatsApp** - A modular, personal AI system with user registration and whitelist management, built to capture, organize, and retrieve thoughts while assisting with routine tasks.

[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![NestJS](https://img.shields.io/badge/NestJS-E0234E?style=for-the-badge&logo=nestjs&logoColor=white)](https://nestjs.com/)
[![WhatsApp](https://img.shields.io/badge/WhatsApp-25D366?style=for-the-badge&logo=whatsapp&logoColor=white)](https://www.whatsapp.com/)
[![Vercel](https://img.shields.io/badge/Vercel-000000?style=for-the-badge&logo=vercel&logoColor=white)](https://vercel.com/)
[![Status](https://img.shields.io/badge/Status-Ready%20for%20Deployment-brightgreen?style=for-the-badge)](docs/PROJECT_STATUS.md)

## 🌟 Features

### ✅ Phase 1.5: MVP + Registration System (Ready for Deployment)
- 🌐 **Web Registration** - Beautiful registration form with email confirmation
- 📧 **Email Whitelist** - Secure email verification and user approval system
- 👥 **Early Adopter Program** - Admin interface for selecting early access users
- 🔐 **Access Control** - WhatsApp integration checks whitelist before allowing access
- 🤖 **WhatsApp Integration** - Complete webhook handling with Twilio
- 🧠 **Memory System** - Capture, categorize, and store user memories
- 🎭 **Personality Tones** - Friendly, Professional, or Witty responses
- 🔧 **Modular Skills** - Extensible skill-based architecture with 4 core skills
- 🔄 **Session Management** - Persistent conversation context
- 🛡️ **Security** - Webhook validation, rate limiting, and error handling
- 📊 **Logging & Monitoring** - Comprehensive logging and performance tracking
- ☁️ **Production Ready** - Vercel deployment with Digital Ocean Spaces storage

### 🔄 Current Skills Available
- **Onboarding** - User setup and configuration
- **Memory Capture** - Save thoughts, ideas, and notes with auto-categorization
- **Reminders** - Set and manage time-based reminders
- **Roadmap** - View development progress and upcoming features

### 🚀 Future Phases
- 🗣️ **Voice Processing** - Speech-to-text and text-to-speech
- 🔗 **MCP Integration** - Connect to Slack, Google Suite, Notion
- 👥 **Multi-User Support** - Team and group functionality
- ☁️ **Cloud Sync** - Google Drive, Dropbox, iCloud integration
- 🌍 **Arabic Language** - Full Arabic language support

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 9+
- OpenAI API key
- Twilio account for WhatsApp integration

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/kanousei/paim-core.git
   cd paim-core
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

4. **Start development server**
   ```bash
   npm run start:dev
   ```

5. **Access the application**
   - API: http://localhost:3000
   - Documentation: http://localhost:3000/api/docs

## ⚙️ Configuration

### Required Environment Variables

```env
# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your-twilio-auth-token-here
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
TWILIO_WEBHOOK_URL=https://your-domain.com/api/v1/webhook/whatsapp

# Security
JWT_SECRET=your-super-secret-jwt-key-min-32-characters-long
ENCRYPTION_KEY=your-super-secret-encryption-key-min-32-characters
```

See `.env.example` for complete configuration options.

## 🏗️ Architecture

PAIM follows a modular, skill-based architecture built with NestJS:

```
src/
├── core/            # Core services (logging, validation, error handling)
├── skills/          # Modular skill implementations
│   ├── base/        # Base classes and interfaces
│   ├── implementations/  # Concrete skill implementations
│   ├── services/    # Skill registry, router, and context
│   └── testing/     # Testing utilities
├── webhook/         # WhatsApp integration
│   ├── controllers/ # Webhook endpoints
│   ├── services/    # Message processing, Twilio integration
│   └── guards/      # Security and validation
├── interfaces/      # TypeScript type definitions
├── config/          # Configuration management
└── app.module.ts    # Main application module
```

### Core Concepts

- **Skills**: Modular capabilities with automatic routing and context
- **Context**: Rich skill execution environment with state management
- **Sessions**: Persistent conversation state with automatic cleanup
- **Memory**: Intelligent categorization and storage of user data
- **Security**: Webhook validation, rate limiting, and comprehensive error handling

## 🛠️ Development

### Available Scripts

```bash
npm run start:dev    # Start development server with hot reload
npm run build        # Build for production
npm run test         # Run unit tests
npm run test:e2e     # Run end-to-end tests
npm run lint         # Lint code
npm run format       # Format code with Prettier
```

### Creating a New Skill

1. Create a new file in `src/skills/implementations/`
2. Extend the `BaseSkill` class
3. Register the skill in the skills module
4. Add comprehensive tests

Example skill structure:
```typescript
import { Injectable } from '@nestjs/common';
import { BaseSkill } from '../base';
import { ISkillContext, ISkillResponse, SkillCategory, MessageType } from '../../interfaces';
import { LoggerService } from '../../core';

@Injectable()
export class MySkill extends BaseSkill {
  readonly id = 'my-skill';
  readonly name = 'My Custom Skill';
  readonly description = 'Description of what this skill does';
  readonly triggers = ['keyword1', 'keyword2'];
  readonly category = SkillCategory.UTILITY;
  readonly priority = 5;

  constructor(logger: LoggerService) {
    super(logger);
  }

  async execute(input: string, context: ISkillContext): Promise<ISkillResponse> {
    // Your skill logic here
    const response = context.reply('Hello from my skill!');

    return this.createResponse(response, MessageType.TEXT, context.user.tone);
  }
}
```

### Testing Skills

Use the provided testing utilities:
```typescript
import { SkillTestHelper } from '../skills/testing/skill-test.helper';

describe('MySkill', () => {
  it('should respond correctly', async () => {
    const { response, context } = await SkillTestHelper.testSkillExecution(
      skill,
      'keyword1 test input'
    );

    SkillTestHelper.expectValidResponse(response);
    expect(response.content).toContain('Hello from my skill!');
  });
});
```

## 📚 Documentation

### API Documentation
- **Swagger UI**: http://localhost:3000/api/docs (when running in development)
- **API Guide**: [docs/API.md](docs/API.md)

### Development Documentation
- **Development Guide**: [docs/DEVELOPMENT.md](docs/DEVELOPMENT.md)
- **Deployment Guide**: [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)
- **Project Status**: [docs/PROJECT_STATUS.md](docs/PROJECT_STATUS.md)

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:cov

# Run tests in watch mode
npm run test:watch

# Run end-to-end tests
npm run test:e2e
```

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run start:prod
```

### Docker Production Deployment

1. **Prepare Production Environment**
   ```bash
   cp .env.production .env.production.local
   # Edit .env.production.local with your production values
   ```

2. **Deploy with Docker Compose**
   ```bash
   # Build and start all services
   docker-compose -f docker-compose.prod.yml up -d

   # Or use the deployment script
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh deploy
   ```

3. **Verify Deployment**
   ```bash
   # Check health
   curl http://localhost:3000/health

   # Check detailed health with dependencies
   curl http://localhost:3000/health/detailed
   ```

### Manual Production Deployment

```bash
# Build for production
npm run build

# Start production server
NODE_ENV=production npm run start:prod
```

### Production Features

- **Health Monitoring**: `/health` and `/health/detailed` endpoints
- **Metrics**: Prometheus metrics at `/metrics`
- **Logging**: Structured JSON logging with configurable levels
- **Security**: Rate limiting, CORS, and webhook signature verification
- **Docker Support**: Multi-stage builds with security best practices
- **Monitoring Stack**: Prometheus + Grafana dashboards included

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for GPT-4 and Whisper APIs
- **Twilio** for WhatsApp integration
- **NestJS** for the robust framework
- **The AI Community** for inspiration and support

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/kanousei/paim-core/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/kanousei/paim-core/discussions)

---

**Made with ❤️ by [Kanousei Technology](https://kanousei.tech)**

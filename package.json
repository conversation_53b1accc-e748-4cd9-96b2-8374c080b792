{"name": "paim-core", "version": "0.1.0", "description": "PAIM (Personal AI Manager) - A modular, personal AI system built to act as a user's second brain through WhatsApp", "author": "<PERSON> Jr. <<EMAIL>>", "license": "MIT", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:quality": "node scripts/test-quality.js", "test:ci": "npm run test:quality", "validate:env": "node scripts/validate-environment.js", "env:check": "npm run validate:env", "monitoring:setup": "bash scripts/setup-monitoring.sh", "monitoring:start": "docker-compose -f docker-compose.monitoring.yml up -d", "monitoring:stop": "docker-compose -f docker-compose.monitoring.yml down", "monitoring:logs": "docker-compose -f docker-compose.monitoring.yml logs -f", "perf:test": "node scripts/run-performance-tests.js", "perf:smoke": "npx artillery quick --count 10 --num 5 http://localhost:3000/health", "perf:load": "npx artillery run load-testing/artillery-config.yml", "perf:baseline": "node scripts/run-performance-tests.js baseline", "redis:setup": "bash scripts/setup-redis.sh", "redis:start": "docker-compose -f docker-compose.redis.yml up -d", "redis:stop": "docker-compose -f docker-compose.redis.yml down", "redis:logs": "docker-compose -f docker-compose.redis.yml logs -f redis", "redis:backup": "bash scripts/setup-redis.sh backup", "alerts:setup": "bash scripts/setup-alerting.sh", "alerts:test": "bash scripts/setup-alerting.sh test", "alerts:validate": "bash scripts/setup-alerting.sh validate", "alerts:silence": "bash scripts/setup-alerting.sh silence", "backup:appwrite": "node scripts/backup-appwrite.js", "backup:full": "bash scripts/backup-automation.sh backup", "backup:install-cron": "bash scripts/backup-automation.sh install-cron", "backup:cleanup": "bash scripts/backup-automation.sh cleanup", "restore:appwrite": "node scripts/restore-appwrite.js", "restore:verify": "bash scripts/backup-automation.sh verify", "typecheck": "tsc --noEmit", "dev": "npm run start:dev", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "prepare": "husky install", "deploy:do": "powershell -ExecutionPolicy Bypass -File scripts/deploy-do.ps1", "deploy:selfhosted": "bash scripts/deploy-selfhosted.sh", "deploy:production": "npm run deploy:do", "docker:build": "docker build -f Dockerfile.digitalocean -t sanad-paim .", "docker:run": "docker run -p 3000:3000 --env-file .env.digitalocean sanad-paim", "docker:dev": "docker-compose -f docker-compose.digitalocean.yml up -d", "docker:logs": "docker-compose -f docker-compose.digitalocean.yml logs -f", "docker:stop": "docker-compose -f docker-compose.digitalocean.yml down"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.0.1", "archiver": "^7.0.1", "axios": "^1.6.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "crypto-js": "^4.2.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "nest-winston": "^1.9.4", "node-appwrite": "^17.0.0", "openai": "^4.20.1", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "twilio": "^4.19.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/compression": "^1.7.5", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "js-yaml": "^4.1.0", "lint-staged": "^15.2.0", "prettier": "^3.0.0", "rimraf": "^5.0.5", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ai", "assistant", "whatsapp", "personal-ai", "<PERSON><PERSON><PERSON>", "typescript", "chatbot", "memory", "skills"], "repository": {"type": "git", "url": "https://github.com/kanousei/paim-core.git"}, "bugs": {"url": "https://github.com/kanousei/paim-core/issues"}, "homepage": "https://github.com/kanousei/paim-core#readme"}
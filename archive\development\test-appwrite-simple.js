#!/usr/bin/env node

/**
 * Simple Appwrite connection test
 */

const { Client, Databases } = require('node-appwrite');
const fs = require('fs');

// Load environment variables from .env.digitalocean
const envContent = fs.readFileSync('.env.digitalocean', 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  line = line.trim();
  if (line && !line.startsWith('#') && line.includes('=')) {
    const [key, ...valueParts] = line.split('=');
    envVars[key] = valueParts.join('=');
  }
});

const client = new Client();
const databases = new Databases(client);

// Configure Appwrite client
client
  .setEndpoint(envVars.APPWRITE_ENDPOINT)
  .setProject(envVars.APPWRITE_PROJECT_ID)
  .setKey(envVars.APPWRITE_API_KEY);

async function testConnection() {
  console.log('🔍 Testing Appwrite Connection...\n');
  
  try {
    // Test database connection
    console.log('📡 Connecting to Appwrite...');
    const database = await databases.get(envVars.APPWRITE_DATABASE_ID);
    console.log(`✅ Connected to database: ${database.name}`);
    
    // List collections
    console.log('\n📊 Checking collections...');
    const collectionsResponse = await databases.listCollections(envVars.APPWRITE_DATABASE_ID);
    console.log(`✅ Found ${collectionsResponse.collections.length} collections`);
    
    collectionsResponse.collections.forEach(collection => {
      console.log(`   - ${collection.name} (${collection.$id})`);
    });
    
    console.log('\n🎉 Appwrite connection successful!');
    
  } catch (error) {
    console.error('❌ Appwrite connection failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    process.exit(1);
  }
}

testConnection();

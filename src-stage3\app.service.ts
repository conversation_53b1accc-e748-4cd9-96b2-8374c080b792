import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private configService: ConfigService) {}

  getStatus() {
    return {
      application: 'Sanad PAIM',
      stage: 'Stage 3',
      framework: 'NestJS',
      status: 'Running',
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + ' MB',
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + ' MB'
      },
      process: {
        pid: process.pid,
        uptime: Math.round(process.uptime()) + ' seconds',
        version: process.version
      },
      dependencies: {
        '@nestjs/core': 'Loaded',
        '@nestjs/common': 'Loaded',
        '@nestjs/config': 'Loaded',
        '@nestjs/platform-express': 'Loaded',
        'class-transformer': 'Loaded',
        'class-validator': 'Loaded',
        'openai': 'Loaded',
        'twilio': 'Loaded',
        'reflect-metadata': 'Loaded',
        'rxjs': 'Loaded'
      },
      configuration: {
        nodeEnv: this.configService.get('NODE_ENV', 'development'),
        port: this.configService.get('PORT', '3000'),
        openaiConfigured: !!this.configService.get('OPENAI_API_KEY'),
        twilioConfigured: !!this.configService.get('TWILIO_ACCOUNT_SID')
      },
      timestamp: new Date().toISOString()
    };
  }
}

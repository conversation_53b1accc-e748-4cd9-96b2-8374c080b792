import { Injectable } from '@nestjs/common';
import {
  ISkillContext,
  ISkillResponse,
  SkillCategory,
  MessageType,
} from '../../interfaces';
import { BaseSkill } from '../base';
import { LoggerService } from '../../core';

@Injectable()
export class OnboardingSkill extends BaseSkill {
  readonly id = 'onboarding';
  readonly name = 'User Onboarding';
  readonly description = 'Handles user onboarding and initial setup process';
  readonly triggers = ['hello', 'start', 'setup', 'onboard', 'hi', 'hey'];
  readonly category = SkillCategory.CORE;
  readonly priority = 10; // High priority for onboarding

  constructor(logger: LoggerService) {
    super(logger);
  }

  async execute(
    input: string,
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    const startTime = Date.now();

    try {
      if (!this.validateInput(input)) {
        return this.createErrorResponse('Invalid input provided');
      }

      const normalizedInput = this.normalizeInput(input);

      // Check if user wants to restart onboarding
      if (
        normalizedInput.includes('restart') &&
        normalizedInput.includes('onboarding')
      ) {
        return await this.restartOnboarding(context);
      }

      // Check current onboarding state
      const onboardingStep = await context.getStateWithDefault(
        'onboarding:step',
        0,
      );
      const config = await context.getUserConfig();

      // If user is already onboarded, provide a friendly response
      if (config.name && onboardingStep === 0) {
        return this.createResponse(
          context.reply(
            `Hey ${config.name}! I'm your PAIM and I'm here to help. What can I do for you today?`,
          ),
          MessageType.TEXT,
          context.user.tone,
        );
      }

      // Handle different onboarding steps
      switch (onboardingStep) {
        case 0:
          return await this.startOnboarding(context);
        case 1:
          return await this.handleNameInput(input, context);
        case 2:
          return await this.handleToneSelection(input, context);
        case 3:
          return await this.completeOnboarding(context);
        default:
          return await this.startOnboarding(context);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logExecution(input, errorMessage, context.user.id, duration, false);
      return this.createErrorResponse('Failed to process onboarding request');
    }
  }

  private async startOnboarding(
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    await context.setState('onboarding:step', 1);

    const welcomeMessage = `👋 Hey there! I'm PAIM, your Personal AI Manager.

I'm here to be your second brain - capturing your thoughts, managing your memories, and helping you stay organized.

Let's get you set up! What would you like me to call you?`;

    return this.createResponse(
      context.reply(welcomeMessage, 'friendly'),
      MessageType.TEXT,
      'friendly',
    );
  }

  private async handleNameInput(
    input: string,
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    const name = input.trim();

    if (name.length < 2 || name.length > 50) {
      return this.createResponse(
        context.reply('Please provide a name between 2 and 50 characters.'),
        MessageType.TEXT,
      );
    }

    await context.setState('onboarding:name', name);
    await context.setState('onboarding:step', 2);

    const toneMessage = `Nice to meet you, ${name}! 

Now, how would you like me to communicate with you?

1. 😊 **Friendly** - Warm and casual
2. 💼 **Professional** - Clear and business-like  
3. 😄 **Witty** - Fun with a sense of humor

Just type the number or name of your preferred style.`;

    return this.createResponse(
      context.reply(toneMessage, 'friendly'),
      MessageType.TEXT,
      'friendly',
    );
  }

  private async handleToneSelection(
    input: string,
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    const normalizedInput = this.normalizeInput(input);
    let selectedTone: string;

    if (normalizedInput.includes('1') || normalizedInput.includes('friendly')) {
      selectedTone = 'friendly';
    } else if (
      normalizedInput.includes('2') ||
      normalizedInput.includes('professional') ||
      normalizedInput.includes('pro')
    ) {
      selectedTone = 'pro';
    } else if (
      normalizedInput.includes('3') ||
      normalizedInput.includes('witty') ||
      normalizedInput.includes('fun')
    ) {
      selectedTone = 'witty';
    } else {
      return this.createResponse(
        context.reply(
          'Please choose 1 (Friendly), 2 (Professional), or 3 (Witty).',
        ),
        MessageType.TEXT,
      );
    }

    await context.setState('onboarding:tone', selectedTone);
    await context.setState('onboarding:step', 3);

    const name = await context.getState('onboarding:name');

    const confirmationMessage = `Perfect! I'll be ${selectedTone} in our conversations.

Here's what I can help you with:
🧠 **Remember things** - Just say "remember" followed by anything
⏰ **Set reminders** - Say "remind me to..." 
📝 **Take notes** - I'll capture your thoughts and ideas
🗣️ **Voice messages** - Send me voice notes anytime

Ready to get started, ${name}?`;

    return this.createResponse(
      context.reply(confirmationMessage, selectedTone),
      MessageType.TEXT,
      selectedTone,
    );
  }

  private async completeOnboarding(
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    const name = await context.getState('onboarding:name');
    const tone = await context.getState('onboarding:tone');

    // Save user configuration
    // Note: This would typically update the user's profile in the database
    // For now, we'll just clear the onboarding state

    await context.removeState('onboarding:step');
    await context.removeState('onboarding:name');
    await context.removeState('onboarding:tone');

    const completionMessage = `🎉 All set, ${name}! 

I'm now configured to be your ${tone} AI assistant. I'm ready to help you capture memories, set reminders, and stay organized.

Try saying:
• "Remember that I love coffee in the morning"
• "Remind me to call mom tomorrow"
• "What can you do?"

What would you like to start with?`;

    return this.createResponse(
      context.reply(completionMessage, tone),
      MessageType.TEXT,
      tone,
      { onboardingCompleted: true },
    );
  }

  private async restartOnboarding(
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    // Clear all onboarding state
    await context.removeState('onboarding:step');
    await context.removeState('onboarding:name');
    await context.removeState('onboarding:tone');

    return await this.startOnboarding(context);
  }
}

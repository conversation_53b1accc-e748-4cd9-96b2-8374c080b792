import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IR<PERSON>inder,
  MemoryCategory,
  MemoryImportance,
  ReminderStatus,
  ReminderType,
} from '../../interfaces';
import { LoggerService } from '../../core';
import { AppwriteService } from '../../appwrite/appwrite.service';

@Injectable()
export class MemoryAppwriteService {
  constructor(
    private logger: LoggerService,
    private appwriteService: AppwriteService,
  ) {}

  async saveMemory(memory: Partial<IMemory>): Promise<IMemory> {
    try {
      const memoryId = memory.id || uuidv4();
      const now = new Date();

      const memoryData: IMemory = {
        id: memoryId,
        userId: memory.userId!,
        content: memory.content!,
        tags: memory.tags || [],
        category: memory.category || MemoryCategory.OTHER,
        importance: memory.importance || MemoryImportance.MEDIUM,
        metadata: memory.metadata || {
          source: 'whatsapp',
          relatedMemories: [],
        },
        createdAt: memory.createdAt || now,
        updatedAt: now,
        accessCount: 0,
        lastAccessedAt: now,
      };

      // Store memory in Appwrite
      await this.appwriteService.createDocument(
        this.appwriteService.getCollectionId('memories'),
        memoryId,
        {
          userId: memoryData.userId,
          content: memoryData.content,
          tags: JSON.stringify(memoryData.tags),
          category: memoryData.category,
          importance: memoryData.importance,
          metadata: JSON.stringify(memoryData.metadata),
          accessCount: memoryData.accessCount,
          lastAccessedAt: memoryData.lastAccessedAt.toISOString(),
          createdAt: memoryData.createdAt.toISOString(),
          updatedAt: memoryData.updatedAt.toISOString(),
        },
      );

      this.logger.log(
        `Saved memory ${memoryId} for user ${memoryData.userId}`,
        'MemoryAppwriteService',
      );
      return memoryData;
    } catch (error) {
      this.logger.error(
        `Failed to save memory for user ${memory.userId}`,
        error instanceof Error ? error.stack : undefined,
        'MemoryAppwriteService',
      );
      throw error;
    }
  }

  async saveReminder(reminder: Partial<IReminder>): Promise<IReminder> {
    try {
      const reminderId = reminder.id || uuidv4();
      const now = new Date();

      const reminderData: IReminder = {
        id: reminderId,
        userId: reminder.userId!,
        title: reminder.title!,
        description: reminder.description,
        scheduledAt: reminder.scheduledAt!,
        status: reminder.status || ReminderStatus.PENDING,
        type: reminder.type || ReminderType.ONE_TIME,
        recurrence: reminder.recurrence,
        metadata: reminder.metadata || {
          source: 'whatsapp',
          priority: 1,
          tags: [],
        },
        createdAt: reminder.createdAt || now,
        updatedAt: now,
        completedAt: reminder.completedAt,
      };

      // Store reminder in Appwrite
      await this.appwriteService.createDocument(
        this.appwriteService.getCollectionId('reminders'),
        reminderId,
        {
          userId: reminderData.userId,
          title: reminderData.title,
          description: reminderData.description || '',
          scheduledAt: reminderData.scheduledAt.toISOString(),
          status: reminderData.status,
          type: reminderData.type,
          recurrence: JSON.stringify(reminderData.recurrence || {}),
          metadata: JSON.stringify(reminderData.metadata),
          completedAt: reminderData.completedAt?.toISOString() || '',
          createdAt: reminderData.createdAt.toISOString(),
          updatedAt: reminderData.updatedAt.toISOString(),
        },
      );

      this.logger.log(
        `Saved reminder ${reminderId} for user ${reminderData.userId}`,
        'MemoryAppwriteService',
      );
      return reminderData;
    } catch (error) {
      this.logger.error(
        `Failed to save reminder for user ${reminder.userId}`,
        error instanceof Error ? error.stack : undefined,
        'MemoryAppwriteService',
      );
      throw error;
    }
  }

  async getMemoriesByUserId(
    userId: string,
    limit?: number,
  ): Promise<IMemory[]> {
    try {
      const queries = [`equal("userId", "${userId}")`];
      if (limit) {
        queries.push(`limit(${limit})`);
      }
      queries.push('orderDesc("createdAt")');

      const result = await this.appwriteService.listDocuments(
        this.appwriteService.getCollectionId('memories'),
        queries,
      );

      return result.documents.map((doc) => this.mapDocumentToMemory(doc));
    } catch (error) {
      this.logger.error(
        `Failed to get memories for user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'MemoryAppwriteService',
      );
      return [];
    }
  }

  private mapDocumentToMemory(document: any): IMemory {
    return {
      id: document.$id,
      userId: document.userId,
      content: document.content,
      tags: JSON.parse(document.tags || '[]'),
      category: document.category,
      importance: document.importance,
      metadata: JSON.parse(document.metadata || '{}'),
      accessCount: document.accessCount || 0,
      lastAccessedAt: new Date(document.lastAccessedAt),
      createdAt: new Date(document.createdAt),
      updatedAt: new Date(document.updatedAt),
    };
  }
}

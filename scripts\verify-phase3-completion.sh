#!/bin/bash

# Phase 3 Completion Verification Script
# Verifies that all Phase 3 (Monitoring & Performance) components are in place

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "${MAGENTA}$1${NC}"
}

# Check if file exists
check_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        success "$description exists: $file"
        return 0
    else
        error "$description missing: $file"
        return 1
    fi
}

# Check if directory exists
check_directory() {
    local dir=$1
    local description=$2
    
    if [ -d "$dir" ]; then
        success "$description exists: $dir"
        return 0
    else
        error "$description missing: $dir"
        return 1
    fi
}

# Main verification function
verify_phase3_completion() {
    header "🔍 Phase 3 Completion Verification"
    header "======================================"
    
    local failures=0
    
    # 3.1 Prometheus/Grafana Monitoring
    header "\n📊 3.1 Checking Prometheus/Grafana Monitoring Setup"
    
    check_file "monitoring/prometheus.yml" "Prometheus configuration" || ((failures++))
    check_file "monitoring/alert_rules.yml" "Prometheus alert rules" || ((failures++))
    check_file "docker-compose.monitoring.yml" "Monitoring Docker Compose" || ((failures++))
    check_directory "monitoring/grafana" "Grafana configuration directory" || ((failures++))
    check_file "monitoring/grafana/provisioning/datasources/datasources.yml" "Grafana datasources" || ((failures++))
    check_file "monitoring/grafana/provisioning/dashboards/dashboards.yml" "Grafana dashboards config" || ((failures++))
    check_directory "monitoring/grafana/dashboards" "Grafana dashboards directory" || ((failures++))
    
    # 3.2 Performance Load Testing
    header "\n⚡ 3.2 Checking Performance Load Testing Setup"
    
    check_directory "load-testing" "Load testing directory" || ((failures++))
    check_file "load-testing/artillery-config.yml" "Artillery configuration" || ((failures++))
    check_file "load-testing/performance-baseline.yml" "Performance baseline config" || ((failures++))
    check_file "load-testing/processor.js" "Load testing processor" || ((failures++))
    
    # 3.3 Redis Caching
    header "\n🗄️ 3.3 Checking Redis Caching Implementation"
    
    check_directory "src/cache" "Cache module directory" || ((failures++))
    check_file "src/cache/cache.module.ts" "Cache module" || ((failures++))
    check_file "src/cache/cache.service.ts" "Cache service" || ((failures++))
    check_file "src/cache/redis.service.ts" "Redis service" || ((failures++))
    
    # Check if Redis is configured in monitoring
    if grep -q "redis-exporter" docker-compose.monitoring.yml; then
        success "Redis monitoring configured in docker-compose.monitoring.yml"
    else
        warning "Redis monitoring not found in docker-compose.monitoring.yml"
    fi
    
    # 3.4 Real-time Alerting
    header "\n🚨 3.4 Checking Real-time Alerting Configuration"
    
    check_file "monitoring/alertmanager.yml" "AlertManager configuration" || ((failures++))
    check_directory "monitoring/alerting" "Alerting configuration directory" || ((failures++))
    
    # Check alert rules content
    if [ -f "monitoring/alert_rules.yml" ]; then
        local alert_count=$(grep -c "alert:" monitoring/alert_rules.yml || echo "0")
        if [ "$alert_count" -gt 0 ]; then
            success "Found $alert_count alert rules configured"
        else
            warning "No alert rules found in alert_rules.yml"
        fi
    fi
    
    # Additional Infrastructure Checks
    header "\n🏗️ Additional Infrastructure Verification"
    
    # Check CI/CD workflows
    check_directory ".github/workflows" "GitHub Actions workflows" || ((failures++))
    check_file ".github/workflows/ci-cd.yml" "Main CI/CD workflow" || ((failures++))
    
    # Check application monitoring integration
    if grep -q "metrics" src/app.module.ts; then
        success "Metrics integration found in app module"
    else
        warning "Metrics integration not found in app module"
    fi
    
    # Check registration system
    check_directory "src/registration" "Registration module" || ((failures++))
    check_file "src/registration/controllers/registration.controller.ts" "Registration controller" || ((failures++))
    check_file "src/registration/services/registration.service.ts" "Registration service" || ((failures++))
    
    # Summary
    header "\n📋 Verification Summary"
    header "====================="
    
    if [ $failures -eq 0 ]; then
        success "🎉 All Phase 3 components verified successfully!"
        success "✅ Prometheus/Grafana monitoring setup complete"
        success "✅ Performance load testing framework ready"
        success "✅ Redis caching implementation complete"
        success "✅ Real-time alerting configuration complete"
        success ""
        success "🚀 Phase 3 (Monitoring & Performance) is COMPLETE!"
        success "🎯 System is ready for production use"
        success "👥 Users can now register and use the system"
        
        return 0
    else
        error "❌ $failures component(s) missing or incomplete"
        error "🔧 Please address the missing components before marking Phase 3 complete"
        
        return 1
    fi
}

# Check if running in CI environment
if [ "$CI" = "true" ]; then
    log "Running in CI environment"
fi

# Run verification
verify_phase3_completion

exit_code=$?

if [ $exit_code -eq 0 ]; then
    header "\n🎊 PHASE 3 COMPLETION VERIFIED!"
    log "All monitoring and performance components are in place"
    log "The system is ready for production use"
else
    header "\n❌ PHASE 3 VERIFICATION FAILED"
    log "Please fix the missing components and run verification again"
fi

exit $exit_code

#!/usr/bin/env node

/**
 * Create remaining Appwrite Collections
 */

const { Client, Databases } = require('node-appwrite');
const fs = require('fs');

// Load environment variables
const envContent = fs.readFileSync('.env.digitalocean', 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  line = line.trim();
  if (line && !line.startsWith('#') && line.includes('=')) {
    const [key, ...valueParts] = line.split('=');
    envVars[key] = valueParts.join('=');
  }
});

const client = new Client();
const databases = new Databases(client);

client
  .setEndpoint(envVars.APPWRITE_ENDPOINT)
  .setProject(envVars.APPWRITE_PROJECT_ID)
  .setKey(envVars.APPWRITE_API_KEY);

const databaseId = envVars.APPWRITE_DATABASE_ID;

// Remaining collections to create
const collections = [
  {
    id: 'sessions',
    name: 'Sessions',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'sessionId', type: 'string', size: 100, required: true },
      { key: 'isActive', type: 'boolean', required: false, default: true },
      { key: 'startedAt', type: 'datetime', required: true },
      { key: 'lastMessageAt', type: 'datetime', required: false },
      { key: 'messageCount', type: 'integer', required: false, default: 0 },
      { key: 'context', type: 'string', size: 5000, required: false }
    ]
  },
  {
    id: 'memories',
    name: 'Memories',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'content', type: 'string', size: 5000, required: true },
      { key: 'type', type: 'string', size: 50, required: false, default: 'general' },
      { key: 'importance', type: 'integer', required: false, default: 1 },
      { key: 'tags', type: 'string', size: 500, required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'lastAccessedAt', type: 'datetime', required: false }
    ]
  },
  {
    id: 'reminders',
    name: 'Reminders',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'title', type: 'string', size: 200, required: true },
      { key: 'description', type: 'string', size: 1000, required: false },
      { key: 'scheduledFor', type: 'datetime', required: true },
      { key: 'isCompleted', type: 'boolean', required: false, default: false },
      { key: 'isSent', type: 'boolean', required: false, default: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'completedAt', type: 'datetime', required: false }
    ]
  },
  {
    id: 'conversations',
    name: 'Conversations',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'sessionId', type: 'string', size: 100, required: true },
      { key: 'messageId', type: 'string', size: 100, required: true },
      { key: 'role', type: 'string', size: 20, required: true },
      { key: 'content', type: 'string', size: 5000, required: true },
      { key: 'timestamp', type: 'datetime', required: true },
      { key: 'messageType', type: 'string', size: 50, required: false, default: 'text' },
      { key: 'metadata', type: 'string', size: 1000, required: false }
    ]
  },
  {
    id: 'emails',
    name: 'Emails',
    attributes: [
      { key: 'to', type: 'string', size: 255, required: true },
      { key: 'from', type: 'string', size: 255, required: true },
      { key: 'subject', type: 'string', size: 500, required: true },
      { key: 'body', type: 'string', size: 10000, required: true },
      { key: 'type', type: 'string', size: 50, required: true },
      { key: 'status', type: 'string', size: 20, required: false, default: 'pending' },
      { key: 'sentAt', type: 'datetime', required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'errorMessage', type: 'string', size: 1000, required: false }
    ]
  },
  {
    id: 'email_confirmations',
    name: 'Email Confirmations',
    attributes: [
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'token', type: 'string', size: 255, required: true },
      { key: 'isUsed', type: 'boolean', required: false, default: false },
      { key: 'expiresAt', type: 'datetime', required: true },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'usedAt', type: 'datetime', required: false }
    ]
  }
];

async function createRemainingCollections() {
  console.log('🏗️ Creating remaining Appwrite Collections...\n');
  
  try {
    for (const collection of collections) {
      console.log(`📊 Creating collection: ${collection.name} (${collection.id})`);
      
      try {
        // Create collection
        const createdCollection = await databases.createCollection(
          databaseId,
          collection.id,
          collection.name
        );
        
        console.log(`✅ Collection created: ${createdCollection.name}`);
        
        // Add attributes
        for (const attr of collection.attributes) {
          console.log(`   Adding attribute: ${attr.key}`);
          
          if (attr.type === 'string') {
            await databases.createStringAttribute(
              databaseId,
              collection.id,
              attr.key,
              attr.size,
              attr.required,
              attr.default
            );
          } else if (attr.type === 'boolean') {
            await databases.createBooleanAttribute(
              databaseId,
              collection.id,
              attr.key,
              attr.required,
              attr.default
            );
          } else if (attr.type === 'datetime') {
            await databases.createDatetimeAttribute(
              databaseId,
              collection.id,
              attr.key,
              attr.required,
              attr.default
            );
          } else if (attr.type === 'integer') {
            await databases.createIntegerAttribute(
              databaseId,
              collection.id,
              attr.key,
              attr.required,
              undefined, // min
              undefined, // max
              attr.default
            );
          }
          
          // Wait between attribute creation
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log(`✅ All attributes added for ${collection.name}\n`);
        
      } catch (error) {
        if (error.code === 409) {
          console.log(`⚠️  Collection ${collection.name} already exists, skipping...\n`);
        } else {
          throw error;
        }
      }
    }
    
    console.log('🎉 All remaining collections processed!');
    
  } catch (error) {
    console.error('❌ Error creating collections:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    process.exit(1);
  }
}

createRemainingCollections();

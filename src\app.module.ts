import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';
import * as Jo<PERSON> from 'joi';

// Configuration imports
import { appConfig, openaiConfig, twilioConfig, appwriteConfig } from './config';
import storageConfig from './config/storage.config';

// Core module
import { CoreModule } from './core/core.module';

// Feature modules
import { SkillsModule } from './skills/skills.module';
import { WebhookModule } from './webhook/webhook.module';
import { HealthModule } from './health/health.module';
import { RegistrationModule } from './registration/registration.module';

// Feature modules (will be uncommented as they are created)
// import { ServicesModule } from './services/services.module';
// import { AiModule } from './ai/ai.module';
// import { StorageModule } from './storage/storage.module';

@Module({
  imports: [
    // Configuration module with validation
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      load: [appConfig, openaiConfig, twilioConfig, appwriteConfig, storageConfig],
      validationSchema: Joi.object({
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test')
          .default('development'),
        PORT: Joi.number().default(3000),

        // OpenAI Configuration
        OPENAI_API_KEY: Joi.string().required(),
        OPENAI_MODEL: Joi.string().default('gpt-4'),
        OPENAI_MAX_TOKENS: Joi.number().default(1000),
        OPENAI_TEMPERATURE: Joi.number().default(0.7),

        // Twilio WhatsApp Configuration
        TWILIO_ACCOUNT_SID: Joi.string().required(),
        TWILIO_AUTH_TOKEN: Joi.string().required(),
        TWILIO_WHATSAPP_NUMBER: Joi.string().required(),
        TWILIO_WEBHOOK_URL: Joi.string().uri().required(),

        // Appwrite Configuration
        APPWRITE_ENDPOINT: Joi.string().uri().optional(),
        APPWRITE_PROJECT_ID: Joi.string().optional(),
        APPWRITE_API_KEY: Joi.string().optional(),
        APPWRITE_DATABASE_ID: Joi.string().optional(),

        // Security
        JWT_SECRET: Joi.string().min(32).required(),
        ENCRYPTION_KEY: Joi.string().min(32).required(),

        // Storage
        STORAGE_PATH: Joi.string().default('./storage'),
        MAX_FILE_SIZE: Joi.number().default(********),
        STORAGE_PROVIDER: Joi.string()
          .valid('local', 'digitalocean')
          .default('local'),

        // Digital Ocean Spaces
        DO_SPACES_ACCESS_KEY_ID: Joi.string().when('STORAGE_PROVIDER', {
          is: 'digitalocean',
          then: Joi.required(),
          otherwise: Joi.optional(),
        }),
        DO_SPACES_SECRET_ACCESS_KEY: Joi.string().when('STORAGE_PROVIDER', {
          is: 'digitalocean',
          then: Joi.required(),
          otherwise: Joi.optional(),
        }),
        DO_SPACES_ENDPOINT: Joi.string().uri().when('STORAGE_PROVIDER', {
          is: 'digitalocean',
          then: Joi.required(),
          otherwise: Joi.optional(),
        }),
        DO_SPACES_REGION: Joi.string().default('nyc3'),
        DO_SPACES_BUCKET: Joi.string().when('STORAGE_PROVIDER', {
          is: 'digitalocean',
          then: Joi.required(),
          otherwise: Joi.optional(),
        }),
        DO_SPACES_CDN_ENDPOINT: Joi.string().uri().optional(),

        // Email & Registration
        EMAIL_SERVICE_PROVIDER: Joi.string()
          .valid('sendgrid')
          .default('sendgrid'),
        SENDGRID_API_KEY: Joi.string().optional(),
        FROM_EMAIL: Joi.string().email().optional(),
        ADMIN_EMAIL: Joi.string().email().optional(),
        REGISTRATION_ENABLED: Joi.boolean().default(true),
        REQUIRE_EMAIL_CONFIRMATION: Joi.boolean().default(true),
        AUTO_APPROVE_WHITELIST: Joi.boolean().default(false),
        ADMIN_API_KEY: Joi.string().min(16).default('admin-key-change-me'),

        // Rate Limiting
        THROTTLE_TTL: Joi.number().default(60),
        THROTTLE_LIMIT: Joi.number().default(10),

        // CORS
        CORS_ORIGIN: Joi.string().default('*'),

        // Logging
        LOG_LEVEL: Joi.string().default('info'),
        LOG_FILE_PATH: Joi.string().default('./logs'),

        // Features
        ENABLE_SWAGGER: Joi.boolean().default(true),
        DETAILED_ERRORS: Joi.boolean().default(true),
      }),
    }),

    // Winston logging module
    WinstonModule.forRoot({
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json(),
          ),
        }),
      ],
    }),

    // Schedule module for cron jobs and reminders
    ScheduleModule.forRoot(),

    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 10, // 10 requests per minute
      },
    ]),

    // Core module with global services
    CoreModule,

    // Feature modules
    SkillsModule,
    WebhookModule,
    HealthModule,
    RegistrationModule,

    // Feature modules (to be uncommented as they are created)
    // ServicesModule,
    // AiModule,
    // StorageModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}

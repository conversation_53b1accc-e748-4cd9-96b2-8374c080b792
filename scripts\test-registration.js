#!/usr/bin/env node

/**
 * Registration System Test Script
 * Tests the user registration functionality to ensure Phase 3 completion
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Configuration
const CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  testEmail: process.env.TEST_EMAIL || `test-${uuidv4()}@example.com`,
  timeout: 10000,
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testHealthCheck() {
  log('\n🏥 Testing health check endpoint...', 'blue');

  try {
    const response = await axios.get(`${CONFIG.baseUrl}/api/v1/health`, {
      timeout: CONFIG.timeout,
    });
    
    if (response.status === 200) {
      log('✅ Health check passed', 'green');
      log(`   Status: ${response.data.status}`, 'cyan');
      log(`   Uptime: ${response.data.uptime}s`, 'cyan');
      return true;
    } else {
      log(`❌ Health check failed with status: ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Health check failed: ${error.message}`, 'red');
    return false;
  }
}

async function testRegistrationForm() {
  log('\n📝 Testing registration form endpoint...', 'blue');

  try {
    const response = await axios.get(`${CONFIG.baseUrl}/api/v1/register`, {
      timeout: CONFIG.timeout,
      headers: {
        'Accept': 'text/html',
      },
    });
    
    if (response.status === 200 && response.data.includes('<form')) {
      log('✅ Registration form loads successfully', 'green');
      log('   Form contains registration fields', 'cyan');
      return true;
    } else {
      log(`❌ Registration form failed: Invalid response`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Registration form failed: ${error.message}`, 'red');
    return false;
  }
}

async function testRegistrationSubmission() {
  log('\n📤 Testing registration submission...', 'blue');
  
  const testData = {
    firstName: 'Test',
    lastName: 'User',
    email: CONFIG.testEmail,
    phoneNumber: '+1234567890',
    reason: 'Testing registration system for Phase 3 completion',
  };
  
  try {
    const response = await axios.post(`${CONFIG.baseUrl}/api/v1/register`, testData, {
      timeout: CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/html',
      },
      validateStatus: (status) => status < 500, // Accept 4xx responses
    });
    
    if (response.status === 201 || response.status === 409) {
      if (response.status === 201) {
        log('✅ Registration submission successful', 'green');
        log(`   Email: ${testData.email}`, 'cyan');
        log('   Confirmation email should be sent', 'cyan');
      } else {
        log('✅ Registration system working (email already exists)', 'green');
        log('   Duplicate email handling works correctly', 'cyan');
      }
      return true;
    } else {
      log(`❌ Registration submission failed with status: ${response.status}`, 'red');
      log(`   Response: ${response.data}`, 'yellow');
      return false;
    }
  } catch (error) {
    log(`❌ Registration submission failed: ${error.message}`, 'red');
    return false;
  }
}

async function testAdminEndpoint() {
  log('\n👑 Testing admin dashboard endpoint...', 'blue');
  
  try {
    const response = await axios.get(`${CONFIG.baseUrl}/api/v1/admin/dashboard`, {
      timeout: CONFIG.timeout,
      headers: {
        'x-admin-key': process.env.ADMIN_API_KEY || 'admin-key-change-me',
      },
      validateStatus: (status) => status < 500,
    });
    
    if (response.status === 200) {
      log('✅ Admin dashboard accessible', 'green');
      log(`   Total registrations: ${response.data.stats?.totalRequests || 'N/A'}`, 'cyan');
      return true;
    } else {
      log(`⚠️  Admin dashboard returned status: ${response.status}`, 'yellow');
      return true; // Not critical for basic functionality
    }
  } catch (error) {
    log(`⚠️  Admin dashboard test failed: ${error.message}`, 'yellow');
    return true; // Not critical for basic functionality
  }
}

async function testMetricsEndpoint() {
  log('\n📊 Testing metrics endpoint...', 'blue');
  
  try {
    const response = await axios.get(`${CONFIG.baseUrl}/api/v1/metrics`, {
      timeout: CONFIG.timeout,
      validateStatus: (status) => status < 500,
    });
    
    if (response.status === 200) {
      log('✅ Metrics endpoint accessible', 'green');
      log('   Prometheus metrics available', 'cyan');
      return true;
    } else {
      log(`⚠️  Metrics endpoint returned status: ${response.status}`, 'yellow');
      return true; // Not critical for basic functionality
    }
  } catch (error) {
    log(`⚠️  Metrics endpoint test failed: ${error.message}`, 'yellow');
    return true; // Not critical for basic functionality
  }
}

async function runAllTests() {
  log('🚀 Starting Registration System Tests', 'magenta');
  log(`📍 Testing against: ${CONFIG.baseUrl}`, 'cyan');
  log(`📧 Test email: ${CONFIG.testEmail}`, 'cyan');
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck, critical: true },
    { name: 'Registration Form', fn: testRegistrationForm, critical: true },
    { name: 'Registration Submission', fn: testRegistrationSubmission, critical: true },
    { name: 'Admin Dashboard', fn: testAdminEndpoint, critical: false },
    { name: 'Metrics Endpoint', fn: testMetricsEndpoint, critical: false },
  ];
  
  const results = [];
  let criticalFailures = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result, critical: test.critical });
      
      if (!result && test.critical) {
        criticalFailures++;
      }
    } catch (error) {
      log(`💥 Test "${test.name}" threw an error: ${error.message}`, 'red');
      results.push({ name: test.name, passed: false, critical: test.critical });
      
      if (test.critical) {
        criticalFailures++;
      }
    }
  }
  
  // Summary
  log('\n📋 Test Results Summary:', 'magenta');
  results.forEach(result => {
    const icon = result.passed ? '✅' : '❌';
    const criticality = result.critical ? '(Critical)' : '(Optional)';
    log(`  ${icon} ${result.name} ${criticality}`, result.passed ? 'green' : 'red');
  });
  
  log(`\n🎯 Overall Result:`, 'magenta');
  if (criticalFailures === 0) {
    log('✅ All critical tests passed! Registration system is operational.', 'green');
    log('🎉 Phase 3 completion verified - Users can now register!', 'green');
    process.exit(0);
  } else {
    log(`❌ ${criticalFailures} critical test(s) failed.`, 'red');
    log('🔧 Please fix the issues before considering Phase 3 complete.', 'red');
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  log('Registration System Test Script', 'cyan');
  log('Usage: node test-registration.js [options]', 'blue');
  log('');
  log('Environment Variables:', 'yellow');
  log('  TEST_BASE_URL    - Base URL to test (default: http://localhost:3000)', 'cyan');
  log('  TEST_EMAIL       - Email to use for testing (default: random)', 'cyan');
  log('  ADMIN_API_KEY    - Admin API key for admin tests', 'cyan');
  log('');
  log('Examples:', 'yellow');
  log('  node test-registration.js', 'cyan');
  log('  TEST_BASE_URL=https://your-app.com node test-registration.js', 'cyan');
  process.exit(0);
}

// Run tests
runAllTests().catch(error => {
  log(`💥 Test suite failed with error: ${error.message}`, 'red');
  process.exit(1);
});

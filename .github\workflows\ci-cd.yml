name: CI/CD Pipeline

on:
  push:
    branches: [ master, develop, main ]
  pull_request:
    branches: [ master, develop, main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Security Checks
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: TypeScript compilation check
      run: npm run build
      
    - name: Run ESLint
      run: npm run lint
      continue-on-error: true
      
    - name: Security audit
      run: npm audit --audit-level=moderate
      continue-on-error: true
      
    - name: Check for outdated dependencies
      run: npm outdated
      continue-on-error: true

  # Unit and Integration Tests
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: quality-checks
    
    strategy:
      matrix:
        test-type: [unit, e2e]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run unit tests
      if: matrix.test-type == 'unit'
      run: npm run test:cov
      
    - name: Run e2e tests
      if: matrix.test-type == 'e2e'
      run: npm run test:e2e
      env:
        NODE_ENV: test
        
    - name: Upload coverage reports
      if: matrix.test-type == 'unit'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  # Build and Docker Image Creation
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [quality-checks, test]
    
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.digitalocean
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64

  # Performance Testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && (github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Start application for testing
      run: |
        npm run build
        npm run start:prod &
        sleep 30
      env:
        NODE_ENV: test
        PORT: 3000
        
    - name: Run performance tests
      run: node archive/development/test-performance.js
      
    - name: Stop application
      run: pkill -f "node dist/main.js" || true

  # Deployment Notification
  deployment-status:
    name: Deployment Status
    runs-on: ubuntu-latest
    needs: [build, performance-test]
    if: always() && github.event_name == 'push' && (github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main')
    
    steps:
    - name: Deployment Success Notification
      if: needs.build.result == 'success' && needs.performance-test.result == 'success'
      run: |
        echo "✅ Build and tests successful. Digital Ocean will auto-deploy from master branch."
        echo "🚀 Deployment URL: https://sanad-paim-*.ondigitalocean.app"
        
    - name: Deployment Failure Notification
      if: needs.build.result == 'failure' || needs.performance-test.result == 'failure'
      run: |
        echo "❌ Build or tests failed. Deployment blocked."
        exit 1

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

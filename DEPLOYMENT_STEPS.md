# 🚀 Manual Self-Hosted Deployment Steps

Since the automated scripts are experiencing issues, here are the manual steps to complete your self-hosted deployment:

## ✅ Current Status
- ✅ Self-hosted Appwrite infrastructure files created
- ✅ Database schema scripts ready
- ✅ Application configuration updated
- ✅ doctl CLI installed and authenticated
- ✅ Existing Digital Ocean app: `sanad-paim`

## 🎯 Step-by-Step Deployment

### Step 1: Create Appwrite Droplet

Run these commands in your terminal:

```powershell
# Check current droplets
doctl compute droplet list

# Create Appwrite droplet (if not exists)
doctl compute droplet create sanad-appwrite --size s-2vcpu-4gb --image docker-20-04 --region nyc1 --ssh-keys $(doctl compute ssh-key list --format ID --no-header | tr '\n' ',') --wait

# Get the droplet IP
doctl compute droplet list --format Name,PublicIPv4 | findstr sanad-appwrite
```

### Step 2: Update DNS Records

Once you have the droplet IP, update your DNS:
- **A record**: `appwrite.sanad.kanousai.com` → `[DROPLET_IP]`
- **A record**: `functions.sanad.kanousai.com` → `[DROPLET_IP]`

### Step 3: Set Up Appwrite on Droplet

```powershell
# SSH to the droplet (replace IP with your actual IP)
ssh root@[DROPLET_IP]

# On the droplet, run these commands:
apt-get update
apt-get install -y curl

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create appwrite directory
mkdir -p /opt/appwrite
cd /opt/appwrite
```

### Step 4: Upload Appwrite Configuration

From your local machine, copy the files to the droplet:

```powershell
# Copy Docker Compose file
scp docker-compose.appwrite.yml root@[DROPLET_IP]:/opt/appwrite/docker-compose.yml

# Copy environment file
scp .env.appwrite root@[DROPLET_IP]:/opt/appwrite/.env
```

### Step 5: Generate Secure Keys

On the droplet, update the environment file:

```bash
# Generate secure keys
OPENSSL_KEY=$(openssl rand -base64 32)
EXECUTOR_SECRET=$(openssl rand -base64 32)
DB_ROOT_PASS=$(openssl rand -base64 24)
DB_PASS=$(openssl rand -base64 24)

# Update .env file
sed -i "s/your-32-character-secret-key-here/$OPENSSL_KEY/g" .env
sed -i "s/your-executor-secret-key-here/$EXECUTOR_SECRET/g" .env
sed -i "s/your-strong-root-password-here/$DB_ROOT_PASS/g" .env
sed -i "s/your-strong-db-password-here/$DB_PASS/g" .env
```

### Step 6: Start Appwrite Services

```bash
# Start Appwrite
docker-compose --env-file .env up -d

# Wait for services to start
sleep 30

# Check if services are running
docker-compose ps
```

### Step 7: Set Up SSL (Optional but Recommended)

```bash
# Install certbot
apt-get install -y certbot

# Stop Appwrite temporarily
docker-compose down

# Get SSL certificates
certbot certonly --standalone -d appwrite.sanad.kanousai.com -d functions.sanad.kanousai.com --non-interactive --agree-tos --email <EMAIL>

# Start Appwrite again
docker-compose up -d
```

### Step 8: Create Appwrite Project

1. Open browser to: `https://appwrite.sanad.kanousai.com/console`
2. Create account and first project
3. Note down:
   - **Project ID**
   - **API Key** (create with full permissions)

### Step 9: Set Up Database Schema

From your local machine:

```powershell
# Set environment variables
$env:APPWRITE_ENDPOINT = "https://appwrite.sanad.kanousai.com/v1"
$env:APPWRITE_PROJECT_ID = "your-project-id"
$env:APPWRITE_API_KEY = "your-api-key"
$env:APPWRITE_DATABASE_ID = "sanad-production"
$env:APPWRITE_STORAGE_BUCKET_ID = "sanad-files"

# Run schema setup
node scripts/setup-appwrite-schema.js
```

### Step 10: Update Application Configuration

Update your Digital Ocean app environment variables:

1. Go to Digital Ocean Dashboard → Apps → sanad-paim → Settings → Environment Variables
2. Update `APPWRITE_ENDPOINT` to: `https://appwrite.sanad.kanousai.com/v1`
3. Add your project credentials

### Step 11: Deploy Application

```powershell
# Build application
npm run build

# Deploy to Digital Ocean
doctl apps update 1439002e-9be2-4c44-b695-6ddcee5740cd --spec .do/app.yaml
```

### Step 12: Verify Deployment

```powershell
# Check app status
doctl apps list

# Test health endpoints
curl https://your-app.ondigitalocean.app/health
curl https://appwrite.sanad.kanousai.com/health
```

## 🎉 Success Indicators

- ✅ Appwrite console accessible at `https://appwrite.sanad.kanousai.com/console`
- ✅ Application health check passes
- ✅ Database collections created successfully
- ✅ SSL certificates working

## 🔧 Troubleshooting

### If Appwrite won't start:
```bash
# Check logs
docker-compose logs -f

# Restart services
docker-compose restart
```

### If SSL fails:
```bash
# Check DNS propagation
nslookup appwrite.sanad.kanousai.com

# Manual certificate renewal
certbot renew
```

### If database schema fails:
- Verify project ID and API key
- Check network connectivity
- Ensure Appwrite is fully started

## 📞 Need Help?

If you encounter issues:
1. Check the logs: `docker-compose logs -f`
2. Verify DNS settings
3. Ensure all environment variables are set correctly
4. Test network connectivity between services

## 🎯 Next Steps After Deployment

1. **Set up monitoring** for both services
2. **Configure backups** for Appwrite data
3. **Test all application features**
4. **Set up alerts** for service health
5. **Document your configuration** for future reference

---

**Total estimated time: 30-45 minutes**
**Monthly cost: ~$30 (Appwrite droplet + App Platform)**

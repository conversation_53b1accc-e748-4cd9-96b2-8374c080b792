import { Injectable } from '@nestjs/common';
import {
  ISkillContext,
  ISkillResponse,
  SkillCategory,
  MessageType,
  ReminderType,
  ReminderStatus,
} from '../../interfaces';
import { BaseSkill } from '../base';
import { LoggerService } from '../../core';

@Injectable()
export class ReminderSkill extends BaseSkill {
  readonly id = 'remind.me';
  readonly name = 'Reminder Management';
  readonly description = 'Creates and manages reminders for users';
  readonly triggers = ['remind', 'remind me', 'reminder', 'alert', 'notify'];
  readonly category = SkillCategory.PRODUCTIVITY;
  readonly priority = 7;

  constructor(logger: LoggerService) {
    super(logger);
  }

  async execute(
    input: string,
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    const startTime = Date.now();

    try {
      if (!this.validateInput(input)) {
        return this.createErrorResponse('Please provide reminder details');
      }

      // Parse the reminder request
      const reminderData = this.parseReminderInput(input);

      if (!reminderData.title) {
        return this.createResponse(
          context.reply(
            "Please tell me what you'd like to be reminded about. For example: 'Remind me to call mom tomorrow at 3pm'",
          ),
          MessageType.TEXT,
        );
      }

      // Create reminder object
      const reminder = {
        title: reminderData.title,
        description: reminderData.description,
        scheduledAt: reminderData.scheduledAt,
        status: ReminderStatus.PENDING,
        type: reminderData.type,
        metadata: {
          source: 'whatsapp',
          priority: reminderData.priority,
          tags: reminderData.tags,
          context: {
            originalInput: input,
            parsedTime: reminderData.timeExpression,
          },
        },
      };

      // Save the reminder
      await context.saveReminder(reminder);

      // Generate response
      const response = this.generateReminderResponse(reminderData, context);

      const duration = Date.now() - startTime;
      this.logExecution(input, response, context.user.id, duration, true);

      return this.createResponse(
        response,
        MessageType.TEXT,
        context.user.tone,
        {
          reminderType: reminderData.type,
          scheduledAt: reminderData.scheduledAt,
          hasSpecificTime: !!reminderData.timeExpression,
        },
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logExecution(input, errorMessage, context.user.id, duration, false);
      return this.createErrorResponse(
        'Failed to create reminder. Please try again.',
      );
    }
  }

  private parseReminderInput(input: string): {
    title: string;
    description?: string;
    scheduledAt: Date;
    type: ReminderType;
    priority: number;
    tags: string[];
    timeExpression?: string;
  } {
    // Remove trigger words
    let content = input
      .replace(/^(remind|remind me|reminder|alert|notify)\s+/i, '')
      .trim();

    // Handle "remind me to..." pattern
    content = content.replace(/^(me\s+)?to\s+/i, '');

    // Extract time expressions
    const timeData = this.extractTimeExpression(content);

    // Extract the actual task/title
    const title = timeData.cleanContent || content;

    // Determine priority based on keywords
    const priority = this.determinePriority(content);

    // Generate tags
    const tags = this.generateReminderTags(content);

    return {
      title: title.trim(),
      scheduledAt: timeData.scheduledAt,
      type: ReminderType.ONE_TIME,
      priority,
      tags,
      timeExpression: timeData.timeExpression,
    };
  }

  private extractTimeExpression(content: string): {
    scheduledAt: Date;
    cleanContent: string;
    timeExpression?: string;
  } {
    const now = new Date();
    let scheduledAt = new Date(now.getTime() + 60 * 60 * 1000); // Default: 1 hour from now
    let cleanContent = content;
    let timeExpression: string | undefined;

    // Time patterns to match
    const timePatterns = [
      // Relative times
      {
        pattern: /\bin (\d+) (minute|minutes|min|mins)\b/i,
        handler: (match) => {
          const minutes = parseInt(match[1]);
          scheduledAt = new Date(now.getTime() + minutes * 60 * 1000);
          timeExpression = match[0];
          cleanContent = content.replace(match[0], '').trim();
        },
      },
      {
        pattern: /\bin (\d+) (hour|hours|hr|hrs)\b/i,
        handler: (match) => {
          const hours = parseInt(match[1]);
          scheduledAt = new Date(now.getTime() + hours * 60 * 60 * 1000);
          timeExpression = match[0];
          cleanContent = content.replace(match[0], '').trim();
        },
      },
      {
        pattern: /\bin (\d+) (day|days)\b/i,
        handler: (match) => {
          const days = parseInt(match[1]);
          scheduledAt = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
          timeExpression = match[0];
          cleanContent = content.replace(match[0], '').trim();
        },
      },

      // Specific times
      {
        pattern: /\btomorrow\b/i,
        handler: (match) => {
          scheduledAt = new Date(now);
          scheduledAt.setDate(scheduledAt.getDate() + 1);
          scheduledAt.setHours(9, 0, 0, 0); // Default to 9 AM
          timeExpression = match[0];
          cleanContent = content.replace(match[0], '').trim();
        },
      },
      {
        pattern: /\btoday\b/i,
        handler: (match) => {
          scheduledAt = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
          timeExpression = match[0];
          cleanContent = content.replace(match[0], '').trim();
        },
      },
      {
        pattern: /\bnext week\b/i,
        handler: (match) => {
          scheduledAt = new Date(now);
          scheduledAt.setDate(scheduledAt.getDate() + 7);
          scheduledAt.setHours(9, 0, 0, 0);
          timeExpression = match[0];
          cleanContent = content.replace(match[0], '').trim();
        },
      },

      // Specific times with "at"
      {
        pattern: /\bat (\d{1,2})(:\d{2})?\s*(am|pm)?\b/i,
        handler: (match) => {
          let hour = parseInt(match[1]);
          const minute = match[2] ? parseInt(match[2].substring(1)) : 0;
          const ampm = match[3]?.toLowerCase();

          if (ampm === 'pm' && hour !== 12) hour += 12;
          if (ampm === 'am' && hour === 12) hour = 0;

          scheduledAt = new Date(now);
          scheduledAt.setHours(hour, minute, 0, 0);

          // If the time has passed today, schedule for tomorrow
          if (scheduledAt <= now) {
            scheduledAt.setDate(scheduledAt.getDate() + 1);
          }

          timeExpression = match[0];
          cleanContent = content.replace(match[0], '').trim();
        },
      },
    ];

    // Try to match time patterns
    for (const { pattern, handler } of timePatterns) {
      const match = content.match(pattern);
      if (match) {
        handler(match);
        break;
      }
    }

    return { scheduledAt, cleanContent, timeExpression };
  }

  private determinePriority(content: string): number {
    const lowerContent = content.toLowerCase();

    if (
      lowerContent.includes('urgent') ||
      lowerContent.includes('important') ||
      lowerContent.includes('asap')
    ) {
      return 3; // High priority
    }

    if (
      lowerContent.includes('when you can') ||
      lowerContent.includes('sometime')
    ) {
      return 1; // Low priority
    }

    return 2; // Normal priority
  }

  private generateReminderTags(content: string): string[] {
    const tags = new Set<string>();
    const lowerContent = content.toLowerCase();

    // Add context-based tags
    if (
      lowerContent.includes('work') ||
      lowerContent.includes('office') ||
      lowerContent.includes('meeting')
    ) {
      tags.add('work');
    }
    if (
      lowerContent.includes('personal') ||
      lowerContent.includes('home') ||
      lowerContent.includes('family')
    ) {
      tags.add('personal');
    }
    if (lowerContent.includes('call') || lowerContent.includes('phone')) {
      tags.add('call');
    }
    if (lowerContent.includes('email') || lowerContent.includes('message')) {
      tags.add('communication');
    }
    if (
      lowerContent.includes('buy') ||
      lowerContent.includes('purchase') ||
      lowerContent.includes('shop')
    ) {
      tags.add('shopping');
    }
    if (
      lowerContent.includes('appointment') ||
      lowerContent.includes('doctor') ||
      lowerContent.includes('dentist')
    ) {
      tags.add('appointment');
    }

    return Array.from(tags);
  }

  private generateReminderResponse(
    reminderData: {
      title: string;
      scheduledAt: Date;
      timeExpression?: string;
      priority: number;
    },
    context: ISkillContext,
  ): string {
    const { title, scheduledAt, timeExpression, priority } = reminderData;

    // Format the scheduled time
    const timeString = this.formatScheduledTime(scheduledAt, timeExpression);

    // Generate response based on priority and timing
    let baseResponse: string;

    if (priority >= 3) {
      baseResponse = `Got it! I'll make sure to remind you about "${title}" ${timeString}. I've marked this as high priority.`;
    } else if (timeExpression) {
      baseResponse = `Perfect! I'll remind you to "${title}" ${timeString}.`;
    } else {
      baseResponse = `Reminder set! I'll remind you to "${title}" ${timeString}.`;
    }

    return context.reply(baseResponse);
  }

  private formatScheduledTime(
    scheduledAt: Date,
    timeExpression?: string,
  ): string {
    const now = new Date();
    const diffMs = scheduledAt.getTime() - now.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    if (timeExpression) {
      return timeExpression;
    }

    if (diffHours < 1) {
      const minutes = Math.round(diffMs / (1000 * 60));
      return `in ${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }

    if (diffHours < 24) {
      const hours = Math.round(diffHours);
      return `in ${hours} hour${hours !== 1 ? 's' : ''}`;
    }

    if (diffDays < 7) {
      const days = Math.round(diffDays);
      return `in ${days} day${days !== 1 ? 's' : ''}`;
    }

    return `on ${scheduledAt.toLocaleDateString()}`;
  }
}

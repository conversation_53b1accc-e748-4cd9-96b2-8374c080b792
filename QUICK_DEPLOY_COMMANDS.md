# 🚀 Quick Deploy Commands

Since the automated scripts are having issues, here are the exact commands to run:

## Step 1: Check Current Status

```powershell
# Check existing droplets
doctl compute droplet list

# Check existing apps
doctl apps list
```

## Step 2: Create Appwrite Droplet (if needed)

```powershell
# Get your SSH keys
doctl compute ssh-key list

# Create droplet (replace SSH_KEY_ID with your actual key ID)
doctl compute droplet create sanad-appwrite --size s-2vcpu-4gb --image docker-20-04 --region nyc1 --ssh-keys YOUR_SSH_KEY_ID --wait
```

## Step 3: Get Droplet IP

```powershell
# Get the IP address
doctl compute droplet list --format Name,PublicIPv4
```

**📝 Update DNS Records:**
- A record: `appwrite.sanad.kanousai.com` → `[DROPLET_IP]`
- A record: `functions.sanad.kanousai.com` → `[DROPLET_IP]`

## Step 4: Build and Deploy Application

```powershell
# Build the application
npm run build

# Deploy to Digital Ocean (update existing app)
doctl apps update 1439002e-9be2-4c44-b695-6ddcee5740cd --spec .do/app.yaml
```

## Step 5: Set Up Appwrite on Droplet

```bash
# SSH to droplet (replace with your IP)
ssh root@YOUR_DROPLET_IP

# On the droplet:
apt-get update
apt-get install -y curl

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create directory
mkdir -p /opt/appwrite
cd /opt/appwrite
```

## Step 6: Upload Appwrite Files

From your local machine:

```powershell
# Copy files to droplet (replace with your IP)
scp docker-compose.appwrite.yml root@YOUR_DROPLET_IP:/opt/appwrite/docker-compose.yml
scp .env.appwrite root@YOUR_DROPLET_IP:/opt/appwrite/.env
```

## Step 7: Start Appwrite

On the droplet:

```bash
# Generate secure keys
OPENSSL_KEY=$(openssl rand -base64 32)
EXECUTOR_SECRET=$(openssl rand -base64 32)
DB_ROOT_PASS=$(openssl rand -base64 24)
DB_PASS=$(openssl rand -base64 24)

# Update environment file
sed -i "s/your-32-character-secret-key-here/$OPENSSL_KEY/g" .env
sed -i "s/your-executor-secret-key-here/$EXECUTOR_SECRET/g" .env
sed -i "s/your-strong-root-password-here/$DB_ROOT_PASS/g" .env
sed -i "s/your-strong-db-password-here/$DB_PASS/g" .env

# Start services
docker-compose --env-file .env up -d

# Check status
docker-compose ps
```

## Step 8: Verify Deployment

```powershell
# Check app status
doctl apps list

# Test endpoints (replace with your URLs)
curl https://your-app.ondigitalocean.app/health
curl https://appwrite.sanad.kanousai.com/health
```

---

**Run these commands one by one and let me know the results!**

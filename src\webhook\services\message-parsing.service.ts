import { Injectable } from '@nestjs/common';
import {
  IWhatsAppMessage,
  IMessage,
  MessageType,
  MessageDirection,
  IMessageMetadata,
} from '../../interfaces';
import { LoggerService, ValidationService } from '../../core';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class MessageParsingService {
  constructor(
    private logger: LoggerService,
    private validation: ValidationService,
  ) {}

  parseIncomingMessage(
    whatsappMessage: IWhatsAppMessage,
    userId: string,
    sessionId: string,
  ): IMessage {
    try {
      // Determine message type
      const messageType = this.determineMessageType(whatsappMessage);

      // Extract and clean content
      const content = this.extractMessageContent(whatsappMessage, messageType);

      // Create metadata
      const metadata = this.createMessageMetadata(whatsappMessage);

      // Create the parsed message
      const message: IMessage = {
        id: uuidv4(),
        userId,
        sessionId,
        content,
        type: messageType,
        direction: MessageDirection.INBOUND,
        metadata,
        timestamp: new Date(),
        processed: false,
      };

      this.logger.log(
        `Parsed incoming message: ${messageType} from ${whatsappMessage.From}`,
        'MessageParsingService',
      );

      return message;
    } catch (error) {
      this.logger.error(
        'Failed to parse incoming message',
        error instanceof Error ? error.stack : undefined,
        'MessageParsingService',
      );
      throw error;
    }
  }

  private determineMessageType(whatsappMessage: IWhatsAppMessage): MessageType {
    const numMedia = parseInt(whatsappMessage.NumMedia || '0');

    if (numMedia > 0 && whatsappMessage.MediaContentType0) {
      const contentType = whatsappMessage.MediaContentType0.toLowerCase();

      if (contentType.startsWith('audio/')) {
        return MessageType.VOICE;
      } else if (contentType.startsWith('image/')) {
        return MessageType.IMAGE;
      } else if (
        contentType.startsWith('application/') ||
        contentType.startsWith('text/')
      ) {
        return MessageType.DOCUMENT;
      }
    }

    return MessageType.TEXT;
  }

  private extractMessageContent(
    whatsappMessage: IWhatsAppMessage,
    messageType: MessageType,
  ): string {
    switch (messageType) {
      case MessageType.TEXT:
        return this.validation.sanitizeInput(whatsappMessage.Body || '');

      case MessageType.VOICE:
        // For voice messages, we'll store the media URL and process it later
        return whatsappMessage.MediaUrl0 || '';

      case MessageType.IMAGE:
        // For images, we might want to store the URL and any caption
        const imageCaption = whatsappMessage.Body || '';
        return imageCaption
          ? this.validation.sanitizeInput(imageCaption)
          : 'Image received';

      case MessageType.DOCUMENT:
        // For documents, store any accompanying text
        const docCaption = whatsappMessage.Body || '';
        return docCaption
          ? this.validation.sanitizeInput(docCaption)
          : 'Document received';

      default:
        return this.validation.sanitizeInput(whatsappMessage.Body || '');
    }
  }

  private createMessageMetadata(
    whatsappMessage: IWhatsAppMessage,
  ): IMessageMetadata {
    const metadata: IMessageMetadata = {
      whatsappMessageId: whatsappMessage.MessageSid,
      processingTime: 0,
      retryCount: 0,
    };

    // Add media information if present
    const numMedia = parseInt(whatsappMessage.NumMedia || '0');
    if (numMedia > 0) {
      metadata.mediaUrl = whatsappMessage.MediaUrl0;
      metadata.mediaType = whatsappMessage.MediaContentType0;
    }

    return metadata;
  }

  extractUserPhoneNumber(whatsappMessage: IWhatsAppMessage): string {
    // Extract phone number from WhatsApp format (whatsapp:+1234567890)
    const from = whatsappMessage.From;

    if (from.startsWith('whatsapp:')) {
      return from.substring(9); // Remove 'whatsapp:' prefix
    }

    return from;
  }

  isValidMessage(whatsappMessage: IWhatsAppMessage): boolean {
    // Basic validation
    if (
      !whatsappMessage.From ||
      !whatsappMessage.To ||
      !whatsappMessage.MessageSid
    ) {
      return false;
    }

    // Validate phone number format
    if (!this.validation.validatePhoneNumber(whatsappMessage.From)) {
      this.logger.warn(
        `Invalid phone number format: ${whatsappMessage.From}`,
        'MessageParsingService',
      );
      return false;
    }

    // Check if it's a valid message type
    const numMedia = parseInt(whatsappMessage.NumMedia || '0');
    if (
      numMedia === 0 &&
      (!whatsappMessage.Body || whatsappMessage.Body.trim().length === 0)
    ) {
      this.logger.warn('Empty message with no media', 'MessageParsingService');
      return false;
    }

    return true;
  }

  extractMessageIntent(content: string): {
    intent: string;
    confidence: number;
    entities: Record<string, any>;
  } {
    // Simple intent extraction - in production, you might use NLP services
    const normalizedContent = content.toLowerCase().trim();

    // Define intent patterns
    const intentPatterns = [
      {
        intent: 'greeting',
        patterns: ['hello', 'hi', 'hey', 'start'],
        confidence: 0.9,
      },
      {
        intent: 'memory_capture',
        patterns: ['remember', 'note', 'save', 'capture'],
        confidence: 0.8,
      },
      {
        intent: 'reminder',
        patterns: ['remind', 'reminder', 'alert'],
        confidence: 0.8,
      },
      {
        intent: 'help',
        patterns: ['help', 'what can you do', 'commands'],
        confidence: 0.9,
      },
      {
        intent: 'roadmap',
        patterns: ['roadmap', 'features', "what's next"],
        confidence: 0.8,
      },
      {
        intent: 'search',
        patterns: ['find', 'search', 'look for'],
        confidence: 0.7,
      },
    ];

    // Find matching intent
    for (const { intent, patterns, confidence } of intentPatterns) {
      for (const pattern of patterns) {
        if (normalizedContent.includes(pattern)) {
          return {
            intent,
            confidence,
            entities: this.extractEntities(content),
          };
        }
      }
    }

    // Default intent
    return {
      intent: 'general',
      confidence: 0.5,
      entities: this.extractEntities(content),
    };
  }

  private extractEntities(content: string): Record<string, any> {
    const entities: Record<string, any> = {};

    // Extract time entities
    const timePatterns = [
      /\b(tomorrow|today|yesterday)\b/gi,
      /\bin (\d+) (minutes?|hours?|days?)\b/gi,
      /\bat (\d{1,2})(:\d{2})?\s*(am|pm)?\b/gi,
    ];

    const timeEntities: string[] = [];
    timePatterns.forEach((pattern) => {
      const matches = content.match(pattern);
      if (matches) {
        timeEntities.push(...matches);
      }
    });

    if (timeEntities.length > 0) {
      entities.time = timeEntities;
    }

    // Extract mentions and hashtags
    const mentions = content.match(/@\w+/g);
    if (mentions) {
      entities.mentions = mentions.map((m) => m.substring(1));
    }

    const hashtags = content.match(/#\w+/g);
    if (hashtags) {
      entities.hashtags = hashtags.map((h) => h.substring(1));
    }

    // Extract URLs
    const urls = content.match(/(https?:\/\/[^\s]+)/g);
    if (urls) {
      entities.urls = urls;
    }

    // Extract emails
    const emails = content.match(
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    );
    if (emails) {
      entities.emails = emails;
    }

    return entities;
  }

  createOutgoingMessage(
    content: string,
    userId: string,
    sessionId: string,
    type: MessageType = MessageType.TEXT,
    metadata?: Partial<IMessageMetadata>,
  ): IMessage {
    return {
      id: uuidv4(),
      userId,
      sessionId,
      content,
      type,
      direction: MessageDirection.OUTBOUND,
      metadata: {
        processingTime: 0,
        retryCount: 0,
        ...metadata,
      },
      timestamp: new Date(),
      processed: false,
    };
  }
}

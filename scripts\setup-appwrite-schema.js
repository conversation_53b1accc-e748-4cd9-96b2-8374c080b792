#!/usr/bin/env node

/**
 * Appwrite Schema Setup Script for Self-Hosted Instance
 * Creates all required databases, collections, and indexes for Sanad
 */

const { Client, Databases, Storage, ID } = require('node-appwrite');

// Configuration
const CONFIG = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'http://appwrite.sanad.kanousai.com/v1',
  projectId: process.env.APPWRITE_PROJECT_ID,
  apiKey: process.env.APPWRITE_API_KEY,
  databaseId: process.env.APPWRITE_DATABASE_ID || 'sanad-production',
  bucketId: process.env.APPWRITE_STORAGE_BUCKET_ID || 'sanad-files',
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Collection schemas
const collections = {
  users: {
    name: 'users',
    attributes: [
      { key: 'phone', type: 'string', size: 20, required: true },
      { key: 'name', type: 'string', size: 100, required: false },
      { key: 'preferences', type: 'string', size: 1000, required: false },
      { key: 'timezone', type: 'string', size: 50, required: false },
      { key: 'language', type: 'string', size: 10, required: false },
      { key: 'isActive', type: 'boolean', required: true, default: true },
      { key: 'lastSeen', type: 'datetime', required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'phone_index', type: 'unique', attributes: ['phone'] },
      { key: 'active_users', type: 'key', attributes: ['isActive'] },
      { key: 'last_seen', type: 'key', attributes: ['lastSeen'] },
    ],
  },
  
  registrations: {
    name: 'registrations',
    attributes: [
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'name', type: 'string', size: 100, required: true },
      { key: 'phone', type: 'string', size: 20, required: false },
      { key: 'interests', type: 'string', size: 500, required: false },
      { key: 'source', type: 'string', size: 50, required: false },
      { key: 'status', type: 'string', size: 20, required: true, default: 'pending' },
      { key: 'confirmationToken', type: 'string', size: 100, required: false },
      { key: 'confirmedAt', type: 'datetime', required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'email_index', type: 'unique', attributes: ['email'] },
      { key: 'status_index', type: 'key', attributes: ['status'] },
      { key: 'confirmation_token', type: 'key', attributes: ['confirmationToken'] },
    ],
  },
  
  whitelist: {
    name: 'whitelist',
    attributes: [
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'phone', type: 'string', size: 20, required: false },
      { key: 'status', type: 'string', size: 20, required: true, default: 'approved' },
      { key: 'approvedBy', type: 'string', size: 100, required: false },
      { key: 'approvedAt', type: 'datetime', required: false },
      { key: 'notes', type: 'string', size: 500, required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'email_index', type: 'unique', attributes: ['email'] },
      { key: 'phone_index', type: 'key', attributes: ['phone'] },
      { key: 'status_index', type: 'key', attributes: ['status'] },
    ],
  },
  
  sessions: {
    name: 'sessions',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'phone', type: 'string', size: 20, required: true },
      { key: 'context', type: 'string', size: 5000, required: false },
      { key: 'lastActivity', type: 'datetime', required: true },
      { key: 'isActive', type: 'boolean', required: true, default: true },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'user_sessions', type: 'key', attributes: ['userId'] },
      { key: 'phone_sessions', type: 'key', attributes: ['phone'] },
      { key: 'active_sessions', type: 'key', attributes: ['isActive'] },
      { key: 'last_activity', type: 'key', attributes: ['lastActivity'] },
    ],
  },
  
  memories: {
    name: 'memories',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'content', type: 'string', size: 2000, required: true },
      { key: 'category', type: 'string', size: 50, required: false },
      { key: 'tags', type: 'string', size: 500, required: false },
      { key: 'importance', type: 'integer', required: false, min: 1, max: 5 },
      { key: 'isPrivate', type: 'boolean', required: true, default: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'user_memories', type: 'key', attributes: ['userId'] },
      { key: 'category_index', type: 'key', attributes: ['category'] },
      { key: 'importance_index', type: 'key', attributes: ['importance'] },
      { key: 'created_date', type: 'key', attributes: ['createdAt'] },
    ],
  },
  
  reminders: {
    name: 'reminders',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'title', type: 'string', size: 200, required: true },
      { key: 'description', type: 'string', size: 1000, required: false },
      { key: 'dueDate', type: 'datetime', required: true },
      { key: 'isCompleted', type: 'boolean', required: true, default: false },
      { key: 'priority', type: 'string', size: 20, required: false, default: 'medium' },
      { key: 'reminderSent', type: 'boolean', required: true, default: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'updatedAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'user_reminders', type: 'key', attributes: ['userId'] },
      { key: 'due_date', type: 'key', attributes: ['dueDate'] },
      { key: 'completed_status', type: 'key', attributes: ['isCompleted'] },
      { key: 'priority_index', type: 'key', attributes: ['priority'] },
    ],
  },
  
  conversations: {
    name: 'conversations',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'messageId', type: 'string', size: 100, required: true },
      { key: 'direction', type: 'string', size: 10, required: true },
      { key: 'content', type: 'string', size: 2000, required: true },
      { key: 'messageType', type: 'string', size: 20, required: false },
      { key: 'skillUsed', type: 'string', size: 50, required: false },
      { key: 'processingTime', type: 'integer', required: false },
      { key: 'createdAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'user_conversations', type: 'key', attributes: ['userId'] },
      { key: 'message_direction', type: 'key', attributes: ['direction'] },
      { key: 'skill_usage', type: 'key', attributes: ['skillUsed'] },
      { key: 'conversation_date', type: 'key', attributes: ['createdAt'] },
    ],
  },
  
  emails: {
    name: 'emails',
    attributes: [
      { key: 'to', type: 'string', size: 255, required: true },
      { key: 'from', type: 'string', size: 255, required: true },
      { key: 'subject', type: 'string', size: 200, required: true },
      { key: 'body', type: 'string', size: 5000, required: true },
      { key: 'type', type: 'string', size: 50, required: true },
      { key: 'status', type: 'string', size: 20, required: true, default: 'pending' },
      { key: 'sentAt', type: 'datetime', required: false },
      { key: 'error', type: 'string', size: 500, required: false },
      { key: 'createdAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'recipient_index', type: 'key', attributes: ['to'] },
      { key: 'email_type', type: 'key', attributes: ['type'] },
      { key: 'email_status', type: 'key', attributes: ['status'] },
      { key: 'sent_date', type: 'key', attributes: ['sentAt'] },
    ],
  },
  
  email_confirmations: {
    name: 'email_confirmations',
    attributes: [
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'token', type: 'string', size: 100, required: true },
      { key: 'type', type: 'string', size: 50, required: true },
      { key: 'isUsed', type: 'boolean', required: true, default: false },
      { key: 'expiresAt', type: 'datetime', required: true },
      { key: 'usedAt', type: 'datetime', required: false },
      { key: 'createdAt', type: 'datetime', required: true },
    ],
    indexes: [
      { key: 'token_index', type: 'unique', attributes: ['token'] },
      { key: 'email_index', type: 'key', attributes: ['email'] },
      { key: 'confirmation_type', type: 'key', attributes: ['type'] },
      { key: 'expiry_date', type: 'key', attributes: ['expiresAt'] },
    ],
  },
};

// Initialize Appwrite client
function initializeClient() {
  if (!CONFIG.projectId || !CONFIG.apiKey) {
    log('❌ Missing required environment variables:', 'red');
    log('   APPWRITE_PROJECT_ID', 'red');
    log('   APPWRITE_API_KEY', 'red');
    process.exit(1);
  }

  const client = new Client()
    .setEndpoint(CONFIG.endpoint)
    .setProject(CONFIG.projectId)
    .setKey(CONFIG.apiKey);

  return {
    databases: new Databases(client),
    storage: new Storage(client),
  };
}

// Create database
async function createDatabase(databases) {
  log('📊 Creating database...', 'blue');
  
  try {
    await databases.create(CONFIG.databaseId, 'Sanad Production Database');
    log(`✅ Database '${CONFIG.databaseId}' created successfully`, 'green');
  } catch (error) {
    if (error.code === 409) {
      log(`✅ Database '${CONFIG.databaseId}' already exists`, 'yellow');
    } else {
      throw error;
    }
  }
}

// Create storage bucket
async function createStorageBucket(storage) {
  log('🗄️ Creating storage bucket...', 'blue');
  
  try {
    await storage.createBucket(
      CONFIG.bucketId,
      'Sanad Files',
      ['read("any")', 'write("users")'],
      false, // fileSecurity
      true,  // enabled
      10485760, // maximumFileSize (10MB)
      ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt'], // allowedFileExtensions
      'gzip', // compression
      false,  // encryption
      true    // antivirus
    );
    log(`✅ Storage bucket '${CONFIG.bucketId}' created successfully`, 'green');
  } catch (error) {
    if (error.code === 409) {
      log(`✅ Storage bucket '${CONFIG.bucketId}' already exists`, 'yellow');
    } else {
      throw error;
    }
  }
}

// Create collection with attributes and indexes
async function createCollection(databases, collectionConfig) {
  const { name, attributes, indexes } = collectionConfig;
  
  log(`📋 Creating collection '${name}'...`, 'blue');
  
  try {
    // Create collection
    await databases.createCollection(
      CONFIG.databaseId,
      name,
      name.charAt(0).toUpperCase() + name.slice(1),
      ['read("any")', 'write("users")']
    );
    log(`✅ Collection '${name}' created`, 'green');
    
    // Add attributes
    for (const attr of attributes) {
      try {
        if (attr.type === 'string') {
          await databases.createStringAttribute(
            CONFIG.databaseId,
            name,
            attr.key,
            attr.size,
            attr.required,
            attr.default,
            false // array
          );
        } else if (attr.type === 'integer') {
          await databases.createIntegerAttribute(
            CONFIG.databaseId,
            name,
            attr.key,
            attr.required,
            attr.min,
            attr.max,
            attr.default,
            false // array
          );
        } else if (attr.type === 'boolean') {
          await databases.createBooleanAttribute(
            CONFIG.databaseId,
            name,
            attr.key,
            attr.required,
            attr.default,
            false // array
          );
        } else if (attr.type === 'datetime') {
          await databases.createDatetimeAttribute(
            CONFIG.databaseId,
            name,
            attr.key,
            attr.required,
            attr.default,
            false // array
          );
        }
        
        log(`  ✅ Attribute '${attr.key}' added`, 'green');
      } catch (error) {
        if (error.code === 409) {
          log(`  ✅ Attribute '${attr.key}' already exists`, 'yellow');
        } else {
          log(`  ❌ Failed to create attribute '${attr.key}': ${error.message}`, 'red');
        }
      }
    }
    
    // Wait for attributes to be ready
    log(`  ⏳ Waiting for attributes to be ready...`, 'yellow');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Add indexes
    for (const index of indexes) {
      try {
        await databases.createIndex(
          CONFIG.databaseId,
          name,
          index.key,
          index.type,
          index.attributes
        );
        log(`  ✅ Index '${index.key}' created`, 'green');
      } catch (error) {
        if (error.code === 409) {
          log(`  ✅ Index '${index.key}' already exists`, 'yellow');
        } else {
          log(`  ❌ Failed to create index '${index.key}': ${error.message}`, 'red');
        }
      }
    }
    
  } catch (error) {
    if (error.code === 409) {
      log(`✅ Collection '${name}' already exists`, 'yellow');
    } else {
      throw error;
    }
  }
}

// Main setup function
async function setupSchema() {
  log('🚀 Starting Appwrite schema setup...', 'magenta');
  log(`📍 Endpoint: ${CONFIG.endpoint}`, 'cyan');
  log(`📍 Project: ${CONFIG.projectId}`, 'cyan');
  log(`📍 Database: ${CONFIG.databaseId}`, 'cyan');
  
  try {
    const { databases, storage } = initializeClient();
    
    // Create database
    await createDatabase(databases);
    
    // Create storage bucket
    await createStorageBucket(storage);
    
    // Create collections
    for (const [key, collectionConfig] of Object.entries(collections)) {
      await createCollection(databases, collectionConfig);
    }
    
    log('\n🎉 Schema setup completed successfully!', 'green');
    log('\n📋 Next steps:', 'blue');
    log('1. Update your application environment variables', 'cyan');
    log('2. Test the connection with your application', 'cyan');
    log('3. Deploy your application with the new endpoint', 'cyan');
    
  } catch (error) {
    log(`\n❌ Schema setup failed: ${error.message}`, 'red');
    throw error;
  }
}

// Run the setup
if (require.main === module) {
  setupSchema().catch(error => {
    console.error(error);
    process.exit(1);
  });
}

module.exports = { setupSchema };

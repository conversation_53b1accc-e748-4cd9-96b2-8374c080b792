# PAIM DevOps Infrastructure Guide

This comprehensive guide covers all aspects of the PAIM (Personal AI Manager) DevOps infrastructure, including deployment, monitoring, security, and operational procedures.

## 📋 Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [Infrastructure Components](#infrastructure-components)
- [Deployment Guide](#deployment-guide)
- [Monitoring and Observability](#monitoring-and-observability)
- [Security and Compliance](#security-and-compliance)
- [Operational Procedures](#operational-procedures)
- [Troubleshooting](#troubleshooting)
- [Best Practices](#best-practices)

## 🎯 Overview

The PAIM DevOps infrastructure is designed for scalability, reliability, and maintainability. It follows modern DevOps practices including Infrastructure as Code, continuous integration/deployment, comprehensive monitoring, and automated backup strategies.

### Architecture Overview

```mermaid
graph TB
    subgraph "External Services"
        TW[Twilio WhatsApp]
        OAI[OpenAI API]
        AW[Appwrite Backend]
    end
    
    subgraph "Digital Ocean"
        subgraph "App Platform"
            APP[PAIM Application]
        end
        
        subgraph "Monitoring Stack"
            PROM[Prometheus]
            GRAF[Grafana]
            ALERT[AlertManager]
        end
        
        subgraph "Caching Layer"
            REDIS[Redis Cache]
        end
    end
    
    subgraph "CI/CD"
        GHA[GitHub Actions]
        REG[Container Registry]
    end
    
    subgraph "Backup & Recovery"
        BACKUP[Automated Backups]
        DR[Disaster Recovery]
    end
    
    APP --> TW
    APP --> OAI
    APP --> AW
    APP --> REDIS
    
    PROM --> APP
    GRAF --> PROM
    ALERT --> PROM
    
    GHA --> REG
    GHA --> APP
    
    BACKUP --> AW
    BACKUP --> REDIS
```

### Key Features

- **Automated CI/CD**: GitHub Actions workflows for testing, building, and deployment
- **Comprehensive Monitoring**: Prometheus, Grafana, and AlertManager stack
- **High Availability**: Redis caching and load balancing
- **Security**: Automated security scanning and compliance checks
- **Backup & Recovery**: Automated backups with disaster recovery procedures
- **Performance Optimization**: Load testing and performance monitoring
- **Infrastructure as Code**: All infrastructure defined in code

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ and npm
- Git
- Digital Ocean account
- Appwrite project setup

### Initial Setup

1. **Clone the Repository**
   ```bash
   git clone https://github.com/HDickenson/sanad.git
   cd sanad
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env.development
   # Edit .env.development with your configuration
   npm run validate:env
   ```

4. **Start Development Environment**
   ```bash
   # Start the application
   npm run start:dev
   
   # Start monitoring stack
   npm run monitoring:setup
   
   # Start Redis cache
   npm run redis:setup
   ```

5. **Verify Setup**
   ```bash
   # Health check
   curl http://localhost:3000/health
   
   # Run tests
   npm run test:quality
   ```

### Production Deployment

1. **Configure Secrets**
   - Set up GitHub repository secrets
   - Configure Digital Ocean access tokens
   - Set up external service credentials

2. **Deploy to Production**
   ```bash
   # Trigger deployment via GitHub Actions
   git push origin master
   
   # Or deploy manually
   npm run deploy:production
   ```

3. **Verify Deployment**
   ```bash
   # Check application health
   curl https://your-app.ondigitalocean.app/health
   
   # Run smoke tests
   npm run test:smoke
   ```

## 🏗️ Infrastructure Components

### Application Layer

#### PAIM Core Application
- **Technology**: Node.js, NestJS, TypeScript
- **Deployment**: Digital Ocean App Platform
- **Scaling**: Horizontal auto-scaling
- **Health Checks**: Built-in health endpoints

#### External Integrations
- **Twilio**: WhatsApp messaging integration
- **OpenAI**: AI processing and responses
- **Appwrite**: Backend-as-a-Service for data storage

### Data Layer

#### Primary Database
- **Service**: Appwrite Database
- **Type**: NoSQL document database
- **Backup**: Automated daily backups
- **Recovery**: Point-in-time recovery available

#### Caching Layer
- **Service**: Redis
- **Deployment**: Docker containers
- **Configuration**: Optimized for caching workloads
- **Monitoring**: Redis Exporter for Prometheus

### Monitoring and Observability

#### Metrics Collection
- **Prometheus**: Time-series metrics database
- **Node Exporter**: System metrics
- **Redis Exporter**: Cache metrics
- **Custom Metrics**: Application-specific metrics

#### Visualization
- **Grafana**: Dashboards and visualization
- **Pre-built Dashboards**: Application, system, and business metrics
- **Alerting**: Integrated with AlertManager

#### Logging
- **Loki**: Log aggregation
- **Promtail**: Log collection
- **Structured Logging**: JSON format with correlation IDs

#### Tracing
- **Jaeger**: Distributed tracing
- **OpenTelemetry**: Instrumentation
- **Performance Monitoring**: Request tracing and analysis

### Security

#### Access Control
- **Authentication**: JWT-based authentication
- **Authorization**: Role-based access control
- **API Security**: Rate limiting and input validation

#### Network Security
- **HTTPS**: TLS encryption for all traffic
- **Firewall**: Digital Ocean firewall rules
- **VPC**: Isolated network environment

#### Compliance
- **Security Scanning**: Automated vulnerability scanning
- **Dependency Scanning**: NPM audit integration
- **Code Analysis**: Static code analysis

## 📚 Documentation Index

### Setup and Configuration
- [Environment Setup Guide](./ENVIRONMENT_SETUP.md)
- [Deployment Guide](./DEPLOYMENT.md)
- [Configuration Reference](./CONFIGURATION.md)

### Monitoring and Operations
- [Monitoring Setup](./MONITORING.md)
- [Alert Response Procedures](./ALERT_RESPONSE.md)
- [Performance Tuning](./PERFORMANCE.md)

### Security and Compliance
- [Security Guide](./SECURITY.md)
- [Compliance Checklist](./COMPLIANCE.md)
- [Incident Response](./INCIDENT_RESPONSE.md)

### Backup and Recovery
- [Backup Strategy](./BACKUP_STRATEGY.md)
- [Disaster Recovery Plan](./DISASTER_RECOVERY.md)
- [Data Recovery Procedures](./DATA_RECOVERY.md)

### Development and Testing
- [Development Workflow](./DEVELOPMENT.md)
- [Testing Strategy](./TESTING.md)
- [Code Quality Standards](./CODE_QUALITY.md)

### Troubleshooting
- [Common Issues](./TROUBLESHOOTING.md)
- [Performance Issues](./PERFORMANCE_TROUBLESHOOTING.md)
- [Network Issues](./NETWORK_TROUBLESHOOTING.md)

## 🛠️ Available Commands

### Development Commands
```bash
npm run start:dev          # Start development server
npm run start:debug        # Start with debugging
npm run build              # Build application
npm run test               # Run unit tests
npm run test:e2e           # Run end-to-end tests
npm run test:quality       # Run quality checks
npm run lint               # Run ESLint
```

### Environment Management
```bash
npm run validate:env       # Validate environment configuration
npm run env:check          # Check environment variables
```

### Monitoring Commands
```bash
npm run monitoring:setup   # Setup monitoring stack
npm run monitoring:start   # Start monitoring services
npm run monitoring:stop    # Stop monitoring services
npm run monitoring:logs    # View monitoring logs
```

### Performance Testing
```bash
npm run perf:test          # Run performance tests
npm run perf:smoke         # Run smoke tests
npm run perf:load          # Run load tests
npm run perf:baseline      # Run baseline tests
```

### Caching Commands
```bash
npm run redis:setup        # Setup Redis cache
npm run redis:start        # Start Redis services
npm run redis:stop         # Stop Redis services
npm run redis:backup       # Backup Redis data
```

### Alerting Commands
```bash
npm run alerts:setup       # Setup alerting
npm run alerts:test        # Test alerting
npm run alerts:validate    # Validate alert rules
npm run alerts:silence     # Manage alert silences
```

### Backup Commands
```bash
npm run backup:appwrite    # Backup Appwrite data
npm run backup:full        # Full system backup
npm run backup:install-cron # Install backup cron job
npm run restore:appwrite   # Restore Appwrite data
```

## 🔧 Configuration Management

### Environment Variables

All configuration is managed through environment variables. See [Environment Setup Guide](./ENVIRONMENT_SETUP.md) for detailed configuration instructions.

### Secrets Management

- **Development**: Local `.env` files (not committed)
- **Staging/Production**: GitHub Secrets and Digital Ocean environment variables
- **Rotation**: Regular secret rotation procedures

### Feature Flags

Feature flags are managed through environment variables and can be toggled without deployment:

```bash
FEATURE_VOICE_MESSAGES=true
FEATURE_IMAGE_PROCESSING=false
FEATURE_ANALYTICS=true
```

## 📊 Monitoring and Alerting

### Key Metrics

- **Application Metrics**: Response time, error rate, throughput
- **System Metrics**: CPU, memory, disk usage
- **Business Metrics**: User activity, message volume, registration rate

### Alert Thresholds

- **Critical**: Immediate response required (< 5 minutes)
- **Warning**: Response within 30 minutes
- **Info**: Response within 24 hours

### Dashboards

- **Application Overview**: High-level application metrics
- **System Health**: Infrastructure and resource metrics
- **Business Intelligence**: User activity and business metrics

## 🔒 Security Considerations

### Security Checklist

- [ ] All secrets properly configured
- [ ] HTTPS enabled for all endpoints
- [ ] Rate limiting configured
- [ ] Input validation implemented
- [ ] Security headers configured
- [ ] Dependency scanning enabled
- [ ] Access logs monitored

### Compliance

- **Data Protection**: GDPR compliance for user data
- **Security Standards**: Following OWASP guidelines
- **Audit Trail**: Comprehensive logging and monitoring

## 🚨 Emergency Procedures

### Incident Response

1. **Immediate Response**: Acknowledge and assess
2. **Escalation**: Follow escalation procedures
3. **Communication**: Update stakeholders
4. **Resolution**: Implement fixes
5. **Post-Mortem**: Document and improve

### Disaster Recovery

- **RTO**: 4 hours maximum downtime
- **RPO**: 1 hour maximum data loss
- **Backup Frequency**: Daily automated backups
- **Testing**: Monthly disaster recovery drills

## 📞 Support and Contacts

### Internal Team
- **DevOps Lead**: [Contact Information]
- **Technical Lead**: [Contact Information]
- **On-Call Engineer**: [Contact Information]

### External Support
- **Digital Ocean Support**: [Support Portal]
- **Appwrite Support**: [Support Portal]
- **Twilio Support**: [Support Portal]

---

**Document Version**: 1.0  
**Last Updated**: [Date]  
**Next Review**: [Date + 3 months]  
**Maintained by**: DevOps Team

#!/usr/bin/env node

/**
 * Performance Testing Runner
 * Orchestrates performance tests and generates reports
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Configuration
const CONFIG = {
  artilleryConfig: 'load-testing/artillery-config.yml',
  baselineConfig: 'load-testing/performance-baseline.yml',
  outputDir: 'performance-reports',
  reportFormat: 'json',
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Ensure output directory exists
function createOutputDir() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
}

// Load baseline configuration
function loadBaseline() {
  try {
    const baselineContent = fs.readFileSync(CONFIG.baselineConfig, 'utf8');
    return yaml.load(baselineContent);
  } catch (error) {
    log(`Warning: Could not load baseline config: ${error.message}`, 'yellow');
    return null;
  }
}

// Run Artillery test
function runArtilleryTest(scenario = 'default') {
  log(`\n🚀 Running performance test: ${scenario}`, 'blue');
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(CONFIG.outputDir, `report-${scenario}-${timestamp}.json`);
  
  try {
    const command = `npx artillery run ${CONFIG.artilleryConfig} --output ${reportFile}`;
    log(`Executing: ${command}`, 'cyan');
    
    execSync(command, { stdio: 'inherit' });
    
    log(`✅ Test completed. Report saved to: ${reportFile}`, 'green');
    return reportFile;
  } catch (error) {
    log(`❌ Test failed: ${error.message}`, 'red');
    throw error;
  }
}

// Analyze test results
function analyzeResults(reportFile, baseline) {
  log(`\n📊 Analyzing results from: ${reportFile}`, 'blue');
  
  try {
    const reportContent = fs.readFileSync(reportFile, 'utf8');
    const report = JSON.parse(reportContent);
    
    const analysis = {
      summary: extractSummary(report),
      performance: analyzePerformance(report, baseline),
      errors: analyzeErrors(report),
      recommendations: generateRecommendations(report, baseline),
    };
    
    return analysis;
  } catch (error) {
    log(`❌ Failed to analyze results: ${error.message}`, 'red');
    throw error;
  }
}

// Extract summary metrics
function extractSummary(report) {
  const aggregate = report.aggregate || {};
  
  return {
    requestsCompleted: aggregate.requestsCompleted || 0,
    requestsFailed: aggregate.requestsFailed || 0,
    rps: aggregate.rps?.mean || 0,
    latency: {
      min: aggregate.latency?.min || 0,
      max: aggregate.latency?.max || 0,
      median: aggregate.latency?.median || 0,
      p95: aggregate.latency?.p95 || 0,
      p99: aggregate.latency?.p99 || 0,
    },
    duration: aggregate.duration || 0,
  };
}

// Analyze performance against baseline
function analyzePerformance(report, baseline) {
  if (!baseline) {
    return { status: 'no_baseline', message: 'No baseline available for comparison' };
  }
  
  const summary = extractSummary(report);
  const issues = [];
  
  // Check response times
  const responseTimeBaseline = baseline.baseline?.response_times?.api_endpoints;
  if (responseTimeBaseline) {
    if (summary.latency.p95 > responseTimeBaseline.p95) {
      issues.push(`P95 latency (${summary.latency.p95}ms) exceeds baseline (${responseTimeBaseline.p95}ms)`);
    }
    if (summary.latency.p99 > responseTimeBaseline.p99) {
      issues.push(`P99 latency (${summary.latency.p99}ms) exceeds baseline (${responseTimeBaseline.p99}ms)`);
    }
  }
  
  // Check throughput
  const throughputBaseline = baseline.baseline?.throughput;
  if (throughputBaseline && summary.rps < throughputBaseline.minimum) {
    issues.push(`RPS (${summary.rps}) below minimum baseline (${throughputBaseline.minimum})`);
  }
  
  // Check error rate
  const errorRate = (summary.requestsFailed / (summary.requestsCompleted + summary.requestsFailed)) * 100;
  const errorBaseline = baseline.baseline?.error_rates;
  if (errorBaseline && errorRate > errorBaseline.warning) {
    issues.push(`Error rate (${errorRate.toFixed(2)}%) exceeds warning threshold (${errorBaseline.warning}%)`);
  }
  
  return {
    status: issues.length === 0 ? 'pass' : 'fail',
    issues,
    metrics: {
      errorRate,
      throughput: summary.rps,
      latency: summary.latency,
    }
  };
}

// Analyze errors
function analyzeErrors(report) {
  const errors = report.aggregate?.errors || {};
  const errorAnalysis = [];
  
  Object.entries(errors).forEach(([errorType, count]) => {
    errorAnalysis.push({
      type: errorType,
      count,
      percentage: ((count / (report.aggregate?.requestsCompleted || 1)) * 100).toFixed(2),
    });
  });
  
  return errorAnalysis;
}

// Generate recommendations
function generateRecommendations(report, baseline) {
  const recommendations = [];
  const summary = extractSummary(report);
  
  // High latency recommendations
  if (summary.latency.p95 > 1000) {
    recommendations.push({
      type: 'performance',
      priority: 'high',
      message: 'High P95 latency detected. Consider optimizing database queries and adding caching.',
    });
  }
  
  // Low throughput recommendations
  if (summary.rps < 50) {
    recommendations.push({
      type: 'scalability',
      priority: 'medium',
      message: 'Low throughput detected. Consider horizontal scaling or performance optimization.',
    });
  }
  
  // Error rate recommendations
  const errorRate = (summary.requestsFailed / (summary.requestsCompleted + summary.requestsFailed)) * 100;
  if (errorRate > 5) {
    recommendations.push({
      type: 'reliability',
      priority: 'high',
      message: 'High error rate detected. Review application logs and fix underlying issues.',
    });
  }
  
  return recommendations;
}

// Generate HTML report
function generateHtmlReport(analysis, reportFile) {
  const timestamp = new Date().toISOString();
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>PAIM Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .warning { color: #ffc107; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; }
        .recommendation { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .high { background: #f8d7da; border-left: 4px solid #dc3545; }
        .medium { background: #fff3cd; border-left: 4px solid #ffc107; }
        .low { background: #d4edda; border-left: 4px solid #28a745; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PAIM Performance Test Report</h1>
        <p>Generated: ${timestamp}</p>
        <p>Report File: ${path.basename(reportFile)}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Requests</h3>
            <div>${analysis.summary.requestsCompleted + analysis.summary.requestsFailed}</div>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <div class="${analysis.performance.status === 'pass' ? 'pass' : 'fail'}">
                ${(100 - (analysis.summary.requestsFailed / (analysis.summary.requestsCompleted + analysis.summary.requestsFailed)) * 100).toFixed(2)}%
            </div>
        </div>
        <div class="metric">
            <h3>RPS</h3>
            <div>${analysis.summary.rps.toFixed(2)}</div>
        </div>
        <div class="metric">
            <h3>P95 Latency</h3>
            <div>${analysis.summary.latency.p95}ms</div>
        </div>
    </div>
    
    <div class="section">
        <h2>Performance Analysis</h2>
        <p class="${analysis.performance.status === 'pass' ? 'pass' : 'fail'}">
            Status: ${analysis.performance.status.toUpperCase()}
        </p>
        ${analysis.performance.issues.length > 0 ? `
            <h3>Issues Found:</h3>
            <ul>
                ${analysis.performance.issues.map(issue => `<li>${issue}</li>`).join('')}
            </ul>
        ` : '<p class="pass">No performance issues detected.</p>'}
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        ${analysis.recommendations.map(rec => `
            <div class="recommendation ${rec.priority}">
                <strong>${rec.type.toUpperCase()}</strong> (${rec.priority}): ${rec.message}
            </div>
        `).join('')}
    </div>
    
    <div class="section">
        <h2>Detailed Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Total Requests</td><td>${analysis.summary.requestsCompleted + analysis.summary.requestsFailed}</td></tr>
            <tr><td>Successful Requests</td><td>${analysis.summary.requestsCompleted}</td></tr>
            <tr><td>Failed Requests</td><td>${analysis.summary.requestsFailed}</td></tr>
            <tr><td>Requests per Second</td><td>${analysis.summary.rps.toFixed(2)}</td></tr>
            <tr><td>Min Latency</td><td>${analysis.summary.latency.min}ms</td></tr>
            <tr><td>Max Latency</td><td>${analysis.summary.latency.max}ms</td></tr>
            <tr><td>Median Latency</td><td>${analysis.summary.latency.median}ms</td></tr>
            <tr><td>P95 Latency</td><td>${analysis.summary.latency.p95}ms</td></tr>
            <tr><td>P99 Latency</td><td>${analysis.summary.latency.p99}ms</td></tr>
            <tr><td>Test Duration</td><td>${analysis.summary.duration}s</td></tr>
        </table>
    </div>
</body>
</html>`;

  const htmlFile = reportFile.replace('.json', '.html');
  fs.writeFileSync(htmlFile, htmlContent);
  log(`📄 HTML report generated: ${htmlFile}`, 'cyan');
  
  return htmlFile;
}

// Main execution
async function main() {
  const scenario = process.argv[2] || 'default';
  
  log('🎯 PAIM Performance Testing Suite', 'magenta');
  log(`Scenario: ${scenario}`, 'blue');
  
  try {
    createOutputDir();
    
    // Load baseline configuration
    const baseline = loadBaseline();
    
    // Run performance test
    const reportFile = runArtilleryTest(scenario);
    
    // Analyze results
    const analysis = analyzeResults(reportFile, baseline);
    
    // Generate HTML report
    const htmlReport = generateHtmlReport(analysis, reportFile);
    
    // Display summary
    log('\n📋 Test Summary:', 'magenta');
    log(`Status: ${analysis.performance.status}`, analysis.performance.status === 'pass' ? 'green' : 'red');
    log(`Requests: ${analysis.summary.requestsCompleted + analysis.summary.requestsFailed}`, 'blue');
    log(`RPS: ${analysis.summary.rps.toFixed(2)}`, 'blue');
    log(`P95 Latency: ${analysis.summary.latency.p95}ms`, 'blue');
    
    if (analysis.performance.issues.length > 0) {
      log('\n⚠️ Issues Found:', 'yellow');
      analysis.performance.issues.forEach(issue => log(`  - ${issue}`, 'yellow'));
    }
    
    if (analysis.recommendations.length > 0) {
      log('\n💡 Recommendations:', 'cyan');
      analysis.recommendations.forEach(rec => {
        log(`  - [${rec.priority.toUpperCase()}] ${rec.message}`, 'cyan');
      });
    }
    
    log(`\n📊 Reports saved to: ${CONFIG.outputDir}/`, 'green');
    
    // Exit with appropriate code
    process.exit(analysis.performance.status === 'pass' ? 0 : 1);
    
  } catch (error) {
    log(`\n💥 Performance testing failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`\n💥 Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    log(`\n💥 Script failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

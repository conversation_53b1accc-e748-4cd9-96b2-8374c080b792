import { Controller, Get, Post, Body } from '@nestjs/common';
import { OpenaiService } from './openai.service';

@Controller('ai')
export class OpenaiController {
  constructor(private readonly openaiService: OpenaiService) {}

  @Get('test')
  async testConnection() {
    return this.openaiService.testConnection();
  }

  @Post('chat')
  async chat(@Body() body: { message: string }) {
    return this.openaiService.chat(body.message);
  }
}

# Simple Self-Hosted Deployment Script for Sanad
Write-Host "Starting self-hosted deployment for Sanad..." -ForegroundColor Green

# Configuration
$APPWRITE_DROPLET_NAME = "sanad-appwrite"
$APP_NAME = "sanad-paim"

Write-Host ""
Write-Host "Deployment Plan:" -ForegroundColor Blue
Write-Host "1. Check prerequisites" -ForegroundColor Cyan
Write-Host "2. Create Appwrite droplet" -ForegroundColor Cyan
Write-Host "3. Deploy application" -ForegroundColor Cyan
Write-Host ""

$continue = Read-Host "Continue? (y/N)"
if ($continue -ne "y" -and $continue -ne "Y") {
    Write-Host "Cancelled" -ForegroundColor Yellow
    exit
}

# Check doctl
Write-Host "Checking doctl..." -ForegroundColor Blue
try {
    doctl version | Out-Null
    Write-Host "✅ doctl found" -ForegroundColor Green
} catch {
    Write-Host "❌ doctl not found" -ForegroundColor Red
    exit 1
}

# Check authentication
Write-Host "Checking authentication..." -ForegroundColor Blue
try {
    doctl account get | Out-Null
    Write-Host "✅ doctl authenticated" -ForegroundColor Green
} catch {
    Write-Host "❌ doctl not authenticated" -ForegroundColor Red
    exit 1
}

# Check if droplet exists
Write-Host "Checking for existing droplet..." -ForegroundColor Blue
$existingDroplet = doctl compute droplet list --format Name --no-header | Where-Object { $_ -eq $APPWRITE_DROPLET_NAME }

if ($existingDroplet) {
    Write-Host "✅ Droplet already exists" -ForegroundColor Yellow
} else {
    Write-Host "Creating Appwrite droplet..." -ForegroundColor Blue
    
    # Get SSH keys
    $sshKeys = (doctl compute ssh-key list --format ID --no-header) -join ","
    
    # Create droplet
    doctl compute droplet create $APPWRITE_DROPLET_NAME --size s-2vcpu-4gb --image docker-20-04 --region nyc1 --ssh-keys $sshKeys --wait
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Droplet created successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create droplet" -ForegroundColor Red
        exit 1
    }
}

# Get droplet IP
Write-Host "Getting droplet IP..." -ForegroundColor Blue
$dropletInfo = doctl compute droplet list --format Name,PublicIPv4 --no-header | Where-Object { $_.StartsWith($APPWRITE_DROPLET_NAME) }
if ($dropletInfo) {
    $dropletIP = ($dropletInfo -split '\s+')[1]
    Write-Host "✅ Droplet IP: $dropletIP" -ForegroundColor Green
    Write-Host ""
    Write-Host "📝 DNS Records to create:" -ForegroundColor Yellow
    Write-Host "   A record: appwrite.sanad.kanousai.com -> $dropletIP" -ForegroundColor Cyan
    Write-Host "   A record: functions.sanad.kanousai.com -> $dropletIP" -ForegroundColor Cyan
    Write-Host ""
} else {
    Write-Host "❌ Could not get droplet IP" -ForegroundColor Red
    exit 1
}

# Generate keys for .env.appwrite
Write-Host "Generating secure keys..." -ForegroundColor Blue
if (Test-Path ".env.appwrite") {
    $opensslKey = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString() + (New-Guid).ToString()))
    $executorSecret = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString() + (New-Guid).ToString()))
    $dbRootPass = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString()))
    $dbPass = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString()))
    
    $envContent = Get-Content ".env.appwrite"
    $envContent = $envContent -replace "your-32-character-secret-key-here", $opensslKey
    $envContent = $envContent -replace "your-executor-secret-key-here", $executorSecret
    $envContent = $envContent -replace "your-strong-root-password-here", $dbRootPass
    $envContent = $envContent -replace "your-strong-db-password-here", $dbPass
    $envContent | Set-Content ".env.appwrite"
    
    Write-Host "✅ Keys generated" -ForegroundColor Green
}

# Build application
Write-Host "Building application..." -ForegroundColor Blue
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}
Write-Host "✅ Build completed" -ForegroundColor Green

# Deploy application
Write-Host "Deploying application..." -ForegroundColor Blue
$existingApp = doctl apps list --format ID,Spec.Name --no-header | Where-Object { $_.Contains($APP_NAME) }

if ($existingApp) {
    $appId = ($existingApp -split '\s+')[0]
    Write-Host "Updating existing app: $appId" -ForegroundColor Yellow
    doctl apps update $appId --spec .do/app.yaml
} else {
    Write-Host "Creating new app..." -ForegroundColor Yellow
    doctl apps create --spec .do/app.yaml
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Application deployed" -ForegroundColor Green
} else {
    Write-Host "❌ Application deployment failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Blue
Write-Host "1. Update DNS records with IP: $dropletIP" -ForegroundColor Cyan
Write-Host "2. SSH to droplet: ssh root@$dropletIP" -ForegroundColor Cyan
Write-Host "3. Set up Appwrite services on the droplet" -ForegroundColor Cyan
Write-Host ""

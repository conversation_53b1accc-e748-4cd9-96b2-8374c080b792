const express = require('express');
const cors = require('cors');

// Create Express application
const app = express();

// Get port from environment or default to 3000
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Sanad PAIM Stage 1 - Minimal Application Running',
    timestamp: new Date().toISOString(),
    version: '0.1.0-stage1',
    stage: 'Stage 1: Minimal Working Application',
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    message: 'Welcome to Sanad PAIM - Stage 1',
    description: 'Minimal working application for staged deployment',
    endpoints: {
      health: '/health',
      status: '/status'
    },
    stage: 'Stage 1: Minimal Working Application'
  });
});

// Status endpoint
app.get('/status', (req, res) => {
  res.status(200).json({
    application: 'Sanad PAIM',
    stage: 'Stage 1',
    status: 'Running',
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + ' MB',
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) + ' MB'
    },
    process: {
      pid: process.pid,
      uptime: Math.round(process.uptime()) + ' seconds',
      version: process.version
    },
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested endpoint does not exist',
    availableEndpoints: ['/', '/health', '/status'],
    stage: 'Stage 1'
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong',
    stage: 'Stage 1',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Sanad PAIM Stage 1 server running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/health`);
  console.log(`📍 Status: http://localhost:${PORT}/status`);
  console.log(`🎯 Stage: Stage 1 - Minimal Working Application`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  process.exit(0);
});

module.exports = app;

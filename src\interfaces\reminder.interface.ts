export interface IReminder {
  id: string;
  userId: string;
  title: string;
  description?: string;
  scheduledAt: Date;
  status: ReminderStatus;
  type: ReminderType;
  recurrence?: IReminderRecurrence;
  metadata: IReminderMetadata;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export enum ReminderStatus {
  PENDING = 'pending',
  SENT = 'sent',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

export enum ReminderType {
  ONE_TIME = 'one_time',
  RECURRING = 'recurring',
  LOCATION_BASED = 'location_based',
  CONTEXT_BASED = 'context_based',
}

export interface IReminderRecurrence {
  pattern: RecurrencePattern;
  interval: number;
  daysOfWeek?: number[];
  endDate?: Date;
  maxOccurrences?: number;
}

export enum RecurrencePattern {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export interface IReminderMetadata {
  source: string;
  priority: number;
  tags: string[];
  relatedMemoryId?: string;
  location?: string;
  context?: Record<string, any>;
}

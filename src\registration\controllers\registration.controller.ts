import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Res,
  HttpStatus,
  Logger,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { RegistrationService } from '../services/registration.service';
import { CreateRegistrationRequestDto } from '../../interfaces/registration.interface';

@ApiTags('registration')
@Controller('register')
@UseGuards(ThrottlerGuard)
export class RegistrationController {
  private readonly logger = new Logger(RegistrationController.name);

  constructor(private readonly registrationService: RegistrationService) {}

  @Get()
  @ApiOperation({ summary: 'Show registration form' })
  @ApiResponse({ status: 200, description: 'Registration form displayed' })
  showRegistrationForm(@Res() res: Response): void {
    // Serve the registration HTML form
    const html = this.getRegistrationFormHTML();
    res.setHeader('Content-Type', 'text/html');
    res.status(HttpStatus.OK).send(html);
  }

  @Post()
  @ApiOperation({ summary: 'Submit registration request' })
  @ApiResponse({
    status: 201,
    description: 'Registration request created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid registration data' })
  @ApiResponse({ status: 409, description: 'Email already registered' })
  async submitRegistration(
    @Body() createRegistrationDto: CreateRegistrationRequestDto,
    @Res() res: Response,
  ): Promise<void> {
    try {
      this.logger.log(
        `New registration request: ${createRegistrationDto.email}`,
      );

      // Check if email is already registered
      const existingRequest = await this.registrationService.findByEmail(
        createRegistrationDto.email,
      );

      if (existingRequest) {
        const html = this.getRegistrationFormHTML(
          'This email is already registered. Please check your email for confirmation instructions.',
          'warning',
        );
        res.setHeader('Content-Type', 'text/html');
        res.status(HttpStatus.CONFLICT).send(html);
        return;
      }

      // Create registration request
      const registrationRequest =
        await this.registrationService.createRegistrationRequest(
          createRegistrationDto,
        );

      // Send confirmation email
      await this.registrationService.sendConfirmationEmail(registrationRequest);

      this.logger.log(
        `Registration request created: ${registrationRequest.id}`,
      );

      const html = this.getRegistrationFormHTML(
        'Thank you for registering! Please check your email to confirm your registration.',
        'success',
      );
      res.setHeader('Content-Type', 'text/html');
      res.status(HttpStatus.CREATED).send(html);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to process registration: ${errorMessage}`,
        errorStack,
      );

      const html = this.getRegistrationFormHTML(
        'An error occurred while processing your registration. Please try again.',
        'error',
      );
      res.setHeader('Content-Type', 'text/html');
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send(html);
    }
  }

  @Get('confirm')
  @ApiOperation({ summary: 'Confirm email address' })
  @ApiResponse({ status: 200, description: 'Email confirmed successfully' })
  @ApiResponse({
    status: 400,
    description: 'Invalid or expired confirmation token',
  })
  async confirmEmail(
    @Query('token') token: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      if (!token) {
        const html = this.getConfirmationHTML(
          'Invalid confirmation link. Please check your email for the correct link.',
          'error',
        );
        res.setHeader('Content-Type', 'text/html');
        res.status(HttpStatus.BAD_REQUEST).send(html);
        return;
      }

      const result = await this.registrationService.confirmEmail(token);

      if (result.success) {
        this.logger.log(`Email confirmed: ${result.registrationRequest.email}`);

        const html = this.getConfirmationHTML(
          'Your email has been confirmed! You have been added to our whitelist. We will notify you when early access becomes available.',
          'success',
        );
        res.setHeader('Content-Type', 'text/html');
        res.status(HttpStatus.OK).send(html);
      } else {
        const html = this.getConfirmationHTML(
          result.message || 'Invalid or expired confirmation token.',
          'error',
        );
        res.setHeader('Content-Type', 'text/html');
        res.status(HttpStatus.BAD_REQUEST).send(html);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Failed to confirm email: ${errorMessage}`, errorStack);

      const html = this.getConfirmationHTML(
        'An error occurred while confirming your email. Please try again or contact support.',
        'error',
      );
      res.setHeader('Content-Type', 'text/html');
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send(html);
    }
  }

  @Get('status')
  @ApiOperation({ summary: 'Check registration status' })
  @ApiResponse({ status: 200, description: 'Registration status retrieved' })
  async checkStatus(
    @Query('email') email: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      if (!email) {
        res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Email parameter is required',
        });
        return;
      }

      const registrationRequest =
        await this.registrationService.findByEmail(email);

      if (!registrationRequest) {
        res.status(HttpStatus.NOT_FOUND).json({
          error: 'No registration found for this email',
        });
        return;
      }

      res.status(HttpStatus.OK).json({
        status: registrationRequest.status,
        emailConfirmed: registrationRequest.emailConfirmed,
        isEarlyAdopter: registrationRequest.isEarlyAdopter,
        createdAt: registrationRequest.createdAt,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to check registration status: ${errorMessage}`,
        errorStack,
      );

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'An error occurred while checking registration status',
      });
    }
  }

  private getRegistrationFormHTML(
    message?: string,
    messageType?: 'success' | 'error' | 'warning',
  ): string {
    const messageHtml = message
      ? `
      <div class="message ${messageType}">
        ${message}
      </div>
    `
      : '';

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join Sanad - Early Access Registration</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #333;
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
        }
        .logo p {
            color: #666;
            font-size: 1.1em;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>Sanad</h1>
            <p>Your Personal AI Manager</p>
        </div>
        
        ${messageHtml}
        
        <form method="POST" action="/register">
            <div class="form-group">
                <label for="firstName">First Name *</label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name *</label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phoneNumber">Phone Number (Optional)</label>
                <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="+1234567890">
            </div>
            
            <div class="form-group">
                <label for="reason">Why are you interested in Sanad? (Optional)</label>
                <textarea id="reason" name="reason" placeholder="Tell us what excites you about having a personal AI manager..."></textarea>
            </div>
            
            <button type="submit" class="submit-btn">Join the Waitlist</button>
        </form>
        
        <div class="footer">
            <p>We'll send you a confirmation email and notify you when early access is available.</p>
        </div>
    </div>
</body>
</html>
    `;
  }

  private getConfirmationHTML(
    message: string,
    messageType: 'success' | 'error',
  ): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Confirmation - Sanad</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        .logo h1 {
            color: #333;
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 30px;
        }
        .message {
            padding: 20px;
            border-radius: 8px;
            font-size: 18px;
            line-height: 1.6;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .back-link {
            margin-top: 30px;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>Sanad</h1>
        </div>
        
        <div class="message ${messageType}">
            ${message}
        </div>
        
        <div class="back-link">
            <a href="/register">← Back to Registration</a>
        </div>
    </div>
</body>
</html>
    `;
  }
}

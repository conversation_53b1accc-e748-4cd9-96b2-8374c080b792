import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AppwriteService } from './appwrite.service';
import { LoggerService } from '../core';

// Mock the node-appwrite module
jest.mock('node-appwrite', () => ({
  Client: jest.fn().mockImplementation(() => ({
    setEndpoint: jest.fn().mockReturnThis(),
    setProject: jest.fn().mockReturnThis(),
    setKey: jest.fn().mockReturnThis(),
  })),
  Databases: jest.fn().mockImplementation(() => ({
    list: jest.fn(),
    createDocument: jest.fn(),
    getDocument: jest.fn(),
    updateDocument: jest.fn(),
    deleteDocument: jest.fn(),
    listDocuments: jest.fn(),
  })),
  Storage: jest.fn().mockImplementation(() => ({
    createFile: jest.fn(),
    getFile: jest.fn(),
    deleteFile: jest.fn(),
    getFilePreview: jest.fn(),
  })),
  Users: jest.fn().mockImplementation(() => ({
    create: jest.fn(),
    get: jest.fn(),
    updateName: jest.fn(),
    delete: jest.fn(),
    list: jest.fn(),
  })),
}));

describe('AppwriteService', () => {
  let service: AppwriteService;
  let loggerService: LoggerService;
  let mockDatabases: any;

  const mockAppwriteConfig = {
    endpoint: 'https://cloud.appwrite.io/v1',
    projectId: 'test-project-id',
    apiKey: 'test-api-key',
    databaseId: 'test-database-id',
    collections: {
      users: 'users-collection-id',
      registrations: 'registrations-collection-id',
      whitelist: 'whitelist-collection-id',
    },
    storage: {
      bucketId: 'test-bucket-id',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppwriteService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === 'appwrite') {
                return mockAppwriteConfig;
              }
              return undefined;
            }),
          },
        },
        {
          provide: LoggerService,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AppwriteService>(AppwriteService);
    loggerService = module.get<LoggerService>(LoggerService);

    // Get the mocked instances
    mockDatabases = (service as any).databases;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should test connection successfully', async () => {
      mockDatabases.list.mockResolvedValue({ databases: [], total: 0 });

      await service.onModuleInit();

      expect(mockDatabases.list).toHaveBeenCalled();
      expect(loggerService.log).toHaveBeenCalledWith('✅ Connected to Appwrite successfully');
    });

    it('should handle connection failure', async () => {
      const error = new Error('Connection failed');
      mockDatabases.list.mockRejectedValue(error);

      await expect(service.onModuleInit()).rejects.toThrow('Connection failed');
      expect(loggerService.error).toHaveBeenCalledWith('❌ Failed to connect to Appwrite:', error);
    });
  });

  describe('Database operations', () => {
    describe('createDocument', () => {
      it('should create document successfully', async () => {
        const mockDocument = {
          $id: 'doc-id',
          name: 'Test Document',
          $collectionId: 'collection-id',
          $databaseId: 'test-database-id',
          $createdAt: '2023-01-01T00:00:00.000Z',
          $updatedAt: '2023-01-01T00:00:00.000Z',
          $permissions: []
        };
        mockDatabases.createDocument.mockResolvedValue(mockDocument);

        const result = await service.createDocument('collection-id', 'doc-id', { name: 'Test Document' });

        expect(mockDatabases.createDocument).toHaveBeenCalledWith(
          mockAppwriteConfig.databaseId,
          'collection-id',
          'doc-id',
          { name: 'Test Document' }
        );
        expect(result).toEqual(mockDocument);
      });

      it('should handle create document error', async () => {
        const error = new Error('Create failed');
        mockDatabases.createDocument.mockRejectedValue(error);

        await expect(service.createDocument('collection-id', 'doc-id', {})).rejects.toThrow('Create failed');
        expect(loggerService.error).toHaveBeenCalledWith(
          'Failed to create document in collection collection-id',
          error.stack,
          'AppwriteService'
        );
      });
    });

    describe('getDocument', () => {
      it('should get document successfully', async () => {
        const mockDocument = {
          $id: 'doc-id',
          name: 'Test Document',
          $collectionId: 'collection-id',
          $databaseId: 'test-database-id',
          $createdAt: '2023-01-01T00:00:00.000Z',
          $updatedAt: '2023-01-01T00:00:00.000Z',
          $permissions: []
        };
        mockDatabases.getDocument.mockResolvedValue(mockDocument);

        const result = await service.getDocument('collection-id', 'doc-id');

        expect(mockDatabases.getDocument).toHaveBeenCalledWith(
          mockAppwriteConfig.databaseId,
          'collection-id',
          'doc-id'
        );
        expect(result).toEqual(mockDocument);
      });

      it('should handle get document error', async () => {
        const error = new Error('Document not found');
        mockDatabases.getDocument.mockRejectedValue(error);

        await expect(service.getDocument('collection-id', 'doc-id')).rejects.toThrow('Document not found');
        expect(loggerService.error).toHaveBeenCalledWith(
          'Failed to get document doc-id from collection collection-id',
          error.stack,
          'AppwriteService'
        );
      });
    });
  });
});

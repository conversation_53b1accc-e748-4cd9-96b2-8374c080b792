# Digital Ocean App Platform Optimized Dockerfile
# Multi-stage build optimized for Digital Ocean App Platform

# Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init curl

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S paim -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder --chown=paim:nodejs /app/dist ./dist

# Create necessary directories with proper permissions
RUN mkdir -p logs storage tmp && \
    chown -R paim:nodejs logs storage tmp && \
    chmod 755 logs storage tmp

# Copy health check script
COPY --chown=paim:nodejs src/health-check.ts ./health-check.js

# Switch to non-root user
USER paim

# Expose port (Digital Ocean App Platform uses PORT env var)
EXPOSE 3000

# Health check optimized for Digital Ocean
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/main.js"]

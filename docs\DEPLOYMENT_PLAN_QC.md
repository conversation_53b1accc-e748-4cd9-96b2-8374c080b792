# 🚀 Sanad Production Deployment Plan & QC Checklist

## 📋 **Deployment Overview**

**Target Platform**: Digital Ocean App Platform  
**Email Provider**: Appwrite (instead of SendGrid)  
**Storage**: Digital Ocean Spaces (blr1 region)  
**Repository**: HDickenson/sanad  
**Deployment Method**: Auto-deploy from GitHub master branch  

---

## ✅ **Pre-Deployment Verification Checklist**

### 🔧 **Code Quality & Build**
- [x] **TypeScript Compilation**: Zero errors/warnings
- [x] **Build Process**: Successful `npm run build`
- [x] **Dependencies**: All packages installed and compatible
- [x] **Git Repository**: Latest changes committed and pushed
- [x] **Docker Configuration**: Dockerfile.digitalocean optimized

### 📁 **Configuration Files**
- [x] **Digital Ocean App Spec**: `.do/app.yaml` configured
- [x] **Environment Template**: `.env.digitalocean` ready
- [x] **Deployment Scripts**: `scripts/deploy-do.ps1` and `.sh` available
- [x] **Package.json**: Scripts updated for DO deployment

### 🔐 **Security Preparation**
- [ ] **JWT Secret**: 64+ character secure key generated
- [ ] **Encryption Key**: 64+ character secure key generated
- [ ] **API Keys**: OpenAI, Twilio, Appwrite keys ready
- [ ] **DO Spaces**: Access keys configured (✅ Already set)

---

## 🌐 **Environment Variables Configuration**

### **Required Variables Checklist**

#### **Core Application**
- [ ] `NODE_ENV=production`
- [ ] `PORT=3000`

#### **OpenAI Integration**
- [ ] `OPENAI_API_KEY` (Required)
- [ ] `OPENAI_MODEL=gpt-4`
- [ ] `OPENAI_MAX_TOKENS=1000`
- [ ] `OPENAI_TEMPERATURE=0.7`

#### **Twilio WhatsApp**
- [ ] `TWILIO_ACCOUNT_SID` (Required)
- [ ] `TWILIO_AUTH_TOKEN` (Required)
- [ ] `TWILIO_WHATSAPP_NUMBER` (Required)
- [ ] `TWILIO_WEBHOOK_URL` (Set after deployment)

#### **Security**
- [ ] `JWT_SECRET` (64+ characters)
- [ ] `ENCRYPTION_KEY` (64+ characters)

#### **Digital Ocean Spaces**
- [x] `STORAGE_PROVIDER=digitalocean`
- [x] `DO_SPACES_ACCESS_KEY_ID=********************`
- [x] `DO_SPACES_SECRET_ACCESS_KEY=fkN9G0PRzD01VGhU3I37HW3iB49VUQGAqxOb5UP633U`
- [x] `DO_SPACES_ENDPOINT=https://blr1.digitaloceanspaces.com`
- [x] `DO_SPACES_REGION=blr1`
- [x] `DO_SPACES_BUCKET=mvs-vr-space`
- [x] `DO_SPACES_CDN_ENDPOINT=https://mvs-vr-space.blr1.digitaloceanspaces.com`

#### **Email Service (Appwrite)**
- [x] `EMAIL_SERVICE_PROVIDER=appwrite`
- [ ] `FROM_EMAIL=<EMAIL>`
- [ ] `ADMIN_EMAIL=<EMAIL>`

#### **Appwrite Configuration**
- [ ] `APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1`
- [ ] `APPWRITE_PROJECT_ID` (Required)
- [ ] `APPWRITE_API_KEY` (Required)
- [ ] `APPWRITE_DATABASE_ID` (Required)
- [ ] `APPWRITE_STORAGE_BUCKET_ID` (Required)

#### **Appwrite Collections**
- [ ] `APPWRITE_USERS_COLLECTION_ID=users`
- [ ] `APPWRITE_REGISTRATIONS_COLLECTION_ID=registrations`
- [ ] `APPWRITE_WHITELIST_COLLECTION_ID=whitelist`
- [ ] `APPWRITE_SESSIONS_COLLECTION_ID=sessions`
- [ ] `APPWRITE_MEMORIES_COLLECTION_ID=memories`
- [ ] `APPWRITE_REMINDERS_COLLECTION_ID=reminders`
- [ ] `APPWRITE_CONVERSATIONS_COLLECTION_ID=conversations`
- [ ] `APPWRITE_EMAILS_COLLECTION_ID=emails`
- [ ] `APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID=email_confirmations`

#### **Registration & Features**
- [ ] `REGISTRATION_ENABLED=true`
- [ ] `REQUIRE_EMAIL_CONFIRMATION=true`
- [ ] `AUTO_APPROVE_WHITELIST=false`
- [ ] `ADMIN_API_KEY` (Secure admin key)

#### **Feature Flags**
- [ ] `FEATURE_VOICE_ENABLED=true`
- [ ] `FEATURE_MEDIA_ENABLED=true`
- [ ] `FEATURE_REMINDERS_ENABLED=true`
- [ ] `FEATURE_ANALYTICS_ENABLED=true`

---

## 🏗️ **Appwrite Backend Setup Checklist**

### **Database Collections to Create**

#### **1. Users Collection (`users`)**
- [ ] Collection created with proper permissions
- [ ] Attributes: id, phoneNumber, name, tone, isOnboarded, isWhitelisted, isEarlyAdopter, registrationSource, preferences, createdAt, updatedAt, lastActiveAt

#### **2. Registrations Collection (`registrations`)**
- [ ] Collection created for registration requests
- [ ] Attributes: id, email, firstName, lastName, phoneNumber, status, confirmationToken, earlyAdopterSelectedAt, createdAt, updatedAt

#### **3. Whitelist Collection (`whitelist`)**
- [ ] Collection for whitelisted users
- [ ] Attributes: id, email, phoneNumber, addedBy, reason, createdAt

#### **4. Email Collections**
- [ ] `emails` collection for email queue
- [ ] `email_confirmations` collection for confirmation tokens
- [ ] Proper indexing and permissions set

#### **5. Application Collections**
- [ ] `sessions` collection for user sessions
- [ ] `memories` collection for AI memories
- [ ] `reminders` collection for user reminders
- [ ] `conversations` collection for chat history

### **Storage Configuration**
- [ ] Storage bucket created and configured
- [ ] Proper file upload permissions
- [ ] CDN configuration if needed

---

## 🔍 **Deployment Verification Checklist**

### **Application Health**
- [ ] Health endpoint (`/health`) responds with 200
- [ ] Application starts without errors
- [ ] All services initialize properly
- [ ] Database connections established

### **API Endpoints**
- [ ] `/api/v1/webhook/whatsapp` endpoint accessible
- [ ] `/register` registration page loads
- [ ] `/admin` admin dashboard accessible
- [ ] CORS configuration working

### **File Storage**
- [ ] DO Spaces connection successful
- [ ] File upload functionality working
- [ ] CDN serving files correctly

### **Email System**
- [ ] Appwrite email queue functional
- [ ] Email templates rendering correctly
- [ ] Confirmation emails being queued

---

## 🔐 **Security Validation Checklist**

### **SSL & HTTPS**
- [ ] SSL certificate automatically provisioned
- [ ] All traffic redirected to HTTPS
- [ ] Security headers configured

### **Authentication & Authorization**
- [ ] JWT tokens working correctly
- [ ] Admin authentication functional
- [ ] Rate limiting active

### **Data Protection**
- [ ] Sensitive data encrypted
- [ ] Environment variables secure
- [ ] No secrets in logs

---

## 📊 **Performance & Monitoring**

### **Application Performance**
- [ ] Response times under 2 seconds
- [ ] Memory usage within limits
- [ ] No memory leaks detected

### **Monitoring Setup**
- [ ] Digital Ocean monitoring enabled
- [ ] Log aggregation configured
- [ ] Error tracking active
- [ ] Uptime monitoring set up

---

## 🧪 **Testing Checklist**

### **WhatsApp Integration**
- [ ] Webhook URL configured in Twilio
- [ ] Test message sent and received
- [ ] AI responses working
- [ ] Media handling functional

### **Registration Flow**
- [ ] User can register via web form
- [ ] Email confirmation sent (queued in Appwrite)
- [ ] Admin can approve/reject registrations
- [ ] WhatsApp onboarding working

### **Admin Functions**
- [ ] Admin dashboard accessible
- [ ] User management working
- [ ] Registration oversight functional
- [ ] Analytics data available

---

## 🚀 **Go-Live Checklist**

### **Final Verification**
- [ ] All environment variables set
- [ ] All tests passing
- [ ] Performance metrics acceptable
- [ ] Security scan completed
- [ ] Backup strategy in place

### **Launch Preparation**
- [ ] DNS configured (if custom domain)
- [ ] Monitoring alerts set up
- [ ] Support documentation ready
- [ ] Rollback plan prepared

### **Post-Launch**
- [ ] Monitor application logs
- [ ] Check error rates
- [ ] Verify user registrations
- [ ] Monitor performance metrics
- [ ] Test critical user flows

---

## 📞 **Emergency Contacts & Resources**

- **Digital Ocean Support**: [cloud.digitalocean.com/support](https://cloud.digitalocean.com/support)
- **Appwrite Documentation**: [appwrite.io/docs](https://appwrite.io/docs)
- **Repository**: [github.com/HDickenson/sanad](https://github.com/HDickenson/sanad)

---

**Deployment Status**: 🟡 In Progress  
**Last Updated**: 2025-06-29  
**Next Review**: After environment variables configuration

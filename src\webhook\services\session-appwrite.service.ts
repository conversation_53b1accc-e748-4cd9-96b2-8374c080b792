import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { IUser, IUserSession, IMessage } from '../../interfaces';
import { LoggerService } from '../../core';
import { AppwriteService } from '../../appwrite/appwrite.service';

interface SessionConfig {
  maxConversationHistory: number;
  sessionTimeoutMinutes: number;
  maxConcurrentSessions: number;
}

@Injectable()
export class SessionAppwriteService {
  private readonly config: SessionConfig = {
    maxConversationHistory: 50,
    sessionTimeoutMinutes: 60,
    maxConcurrentSessions: 3,
  };

  constructor(
    private logger: LoggerService,
    private appwriteService: AppwriteService,
  ) {}

  async getOrCreateSession(user: IUser): Promise<IUserSession> {
    try {
      // Try to find an active session for the user
      const activeSession = await this.findActiveSession(user.id);

      if (activeSession) {
        // Update session activity
        const updatedSession = {
          ...activeSession,
          expiresAt: this.calculateExpirationTime(),
        };

        await this.updateSession(activeSession.sessionId, updatedSession);

        this.logger.debug(
          `Using existing session ${activeSession.sessionId} for user ${user.id}`,
          'SessionAppwriteService',
        );

        return updatedSession;
      }

      // Create new session
      const newSession = await this.createNewSession(user);

      this.logger.log(
        `Created new session ${newSession.sessionId} for user ${user.id}`,
        'SessionAppwriteService',
      );

      return newSession;
    } catch (error) {
      this.logger.error(
        `Failed to get or create session for user ${user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SessionAppwriteService',
      );
      throw error;
    }
  }

  async addMessageToSession(
    sessionId: string,
    message: IMessage,
  ): Promise<void> {
    try {
      const session = await this.getSession(sessionId);

      if (!session) {
        this.logger.warn(
          `Attempted to add message to non-existent session: ${sessionId}`,
          'SessionAppwriteService',
        );
        return;
      }

      // Add message to conversation history
      const updatedHistory = [...session.conversationHistory, message];

      // Trim history if it exceeds the limit
      if (updatedHistory.length > this.config.maxConversationHistory) {
        updatedHistory.splice(
          0,
          updatedHistory.length - this.config.maxConversationHistory,
        );
      }

      // Update session with new message
      const updatedSession = {
        ...session,
        conversationHistory: updatedHistory,
        lastMessageAt: new Date(),
        messageCount: updatedHistory.length,
      };

      await this.updateSession(sessionId, updatedSession);

      this.logger.debug(
        `Added message ${message.id} to session ${sessionId}`,
        'SessionAppwriteService',
      );
    } catch (error) {
      this.logger.error(
        `Failed to add message to session ${sessionId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionAppwriteService',
      );
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<IUserSession | undefined> {
    try {
      const document = await this.appwriteService.getDocument(
        this.appwriteService.getCollectionId('sessions'),
        sessionId,
      );

      const session = this.mapDocumentToSession(document);

      if (this.isSessionExpired(session)) {
        await this.endSession(sessionId);
        return undefined;
      }

      return session;
    } catch (error: any) {
      if (error.code === 404) {
        return undefined;
      }
      throw error;
    }
  }

  private async findActiveSession(
    userId: string,
  ): Promise<IUserSession | undefined> {
    try {
      const result = await this.appwriteService.listDocuments(
        this.appwriteService.getCollectionId('sessions'),
        [`equal("userId", "${userId}")`, `equal("isActive", true)`],
      );

      if (result.documents.length === 0) {
        return undefined;
      }

      // Return the most recent active session
      const sessions = result.documents
        .map((doc) => this.mapDocumentToSession(doc))
        .filter((session) => !this.isSessionExpired(session))
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );

      return sessions[0];
    } catch (error) {
      this.logger.error(
        `Failed to find active session for user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionAppwriteService',
      );
      return undefined;
    }
  }

  private async createNewSession(user: IUser): Promise<IUserSession> {
    const sessionId = uuidv4();
    const now = new Date();

    // Clean up old sessions if user has too many
    await this.cleanupUserSessions(user.id);

    const session: IUserSession = {
      userId: user.id,
      sessionId,
      context: {},
      state: {},
      conversationHistory: [],
      createdAt: now,
      expiresAt: this.calculateExpirationTime(),
    };

    // Store session in Appwrite
    await this.appwriteService.createDocument(
      this.appwriteService.getCollectionId('sessions'),
      sessionId,
      {
        userId: user.id,
        phoneNumber: user.phoneNumber,
        sessionId,
        isActive: true,
        startedAt: now.toISOString(),
        lastMessageAt: now.toISOString(),
        messageCount: 0,
        context: JSON.stringify(session.context),
        state: JSON.stringify(session.state),
        conversationHistory: JSON.stringify(session.conversationHistory),
        expiresAt: session.expiresAt.toISOString(),
        createdAt: now.toISOString(),
      },
    );

    return session;
  }

  private async updateSession(
    sessionId: string,
    session: IUserSession,
  ): Promise<void> {
    await this.appwriteService.updateDocument(
      this.appwriteService.getCollectionId('sessions'),
      sessionId,
      {
        lastMessageAt: new Date().toISOString(),
        messageCount: session.conversationHistory.length,
        context: JSON.stringify(session.context),
        state: JSON.stringify(session.state),
        conversationHistory: JSON.stringify(session.conversationHistory),
        expiresAt: session.expiresAt.toISOString(),
      },
    );
  }

  private async endSession(sessionId: string): Promise<void> {
    try {
      await this.appwriteService.updateDocument(
        this.appwriteService.getCollectionId('sessions'),
        sessionId,
        {
          isActive: false,
          endedAt: new Date().toISOString(),
        },
      );

      this.logger.debug(`Ended session ${sessionId}`, 'SessionAppwriteService');
    } catch (error) {
      this.logger.error(
        `Failed to end session ${sessionId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionAppwriteService',
      );
    }
  }

  private async cleanupUserSessions(userId: string): Promise<void> {
    try {
      const result = await this.appwriteService.listDocuments(
        this.appwriteService.getCollectionId('sessions'),
        [`equal("userId", "${userId}")`, `equal("isActive", true)`],
      );

      if (result.documents.length >= this.config.maxConcurrentSessions) {
        // Sort by creation date and end the oldest sessions
        const sessions = result.documents.sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );

        const sessionsToEnd = sessions.slice(
          0,
          sessions.length - this.config.maxConcurrentSessions + 1,
        );

        for (const session of sessionsToEnd) {
          await this.endSession(session.$id);
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to cleanup sessions for user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionAppwriteService',
      );
    }
  }

  private calculateExpirationTime(): Date {
    return new Date(Date.now() + this.config.sessionTimeoutMinutes * 60 * 1000);
  }

  private isSessionExpired(session: IUserSession): boolean {
    return new Date() > new Date(session.expiresAt);
  }

  private mapDocumentToSession(document: any): IUserSession {
    return {
      userId: document.userId,
      sessionId: document.sessionId,
      context: JSON.parse(document.context || '{}'),
      state: JSON.parse(document.state || '{}'),
      conversationHistory: JSON.parse(document.conversationHistory || '[]'),
      createdAt: new Date(document.createdAt),
      expiresAt: new Date(document.expiresAt),
    };
  }
}

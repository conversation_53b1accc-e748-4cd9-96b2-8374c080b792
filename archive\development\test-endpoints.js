#!/usr/bin/env node

/**
 * PAIM Endpoint Testing Script
 * Tests all API endpoints to verify functionality
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

// Test helper function
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PAIM-Test-Client/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test functions
async function testEndpoint(name, path, method = 'GET', data = null, expectedStatus = 200) {
  try {
    console.log(`\n🧪 Testing ${name}...`);
    console.log(`   ${method} ${path}`);
    
    const response = await makeRequest(path, method, data);
    
    if (response.statusCode === expectedStatus) {
      console.log(`   ✅ Status: ${response.statusCode} (Expected: ${expectedStatus})`);
      console.log(`   📄 Response:`, JSON.stringify(response.body, null, 2));
      return true;
    } else {
      console.log(`   ❌ Status: ${response.statusCode} (Expected: ${expectedStatus})`);
      console.log(`   📄 Response:`, JSON.stringify(response.body, null, 2));
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting PAIM API Tests');
  console.log('=' .repeat(50));
  
  const tests = [];
  
  // Test 1: Root endpoint
  tests.push(await testEndpoint(
    'Root Endpoint',
    '/'
  ));
  
  // Test 2: Basic health check
  tests.push(await testEndpoint(
    'Basic Health Check',
    '/health'
  ));
  
  // Test 3: Detailed health check
  tests.push(await testEndpoint(
    'Detailed Health Check',
    '/health/detailed'
  ));
  
  // Test 4: API status
  tests.push(await testEndpoint(
    'API Status',
    '/api/v1/status'
  ));
  
  // Test 5: API documentation
  tests.push(await testEndpoint(
    'API Documentation',
    '/api/docs'
  ));
  
  // Test 6: WhatsApp webhook
  tests.push(await testEndpoint(
    'WhatsApp Webhook',
    '/api/v1/webhook/whatsapp',
    'POST',
    {
      From: 'whatsapp:+**********',
      To: 'whatsapp:+**********',
      Body: 'Hello PAIM! This is a test message.',
      MessageSid: 'test-message-123',
      AccountSid: 'test-account-456'
    }
  ));
  
  // Test 7: User registration
  tests.push(await testEndpoint(
    'User Registration',
    '/api/v1/registration/request',
    'POST',
    {
      phoneNumber: '+**********',
      email: '<EMAIL>',
      name: 'Test User'
    }
  ));
  
  // Test 8: 404 endpoint
  tests.push(await testEndpoint(
    '404 Not Found',
    '/nonexistent/endpoint',
    'GET',
    null,
    404
  ));
  
  // Test summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Summary');
  console.log('=' .repeat(50));
  
  const passed = tests.filter(result => result).length;
  const total = tests.length;
  const failed = total - passed;
  
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${failed}/${total}`);
  console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! PAIM API is working correctly.');
    console.log('\n🔗 Available endpoints:');
    console.log(`   🏠 Root: ${BASE_URL}/`);
    console.log(`   ❤️  Health: ${BASE_URL}/health`);
    console.log(`   📊 Detailed Health: ${BASE_URL}/health/detailed`);
    console.log(`   📋 API Status: ${BASE_URL}/api/v1/status`);
    console.log(`   📚 Documentation: ${BASE_URL}/api/docs`);
    console.log(`   📱 WhatsApp Webhook: ${BASE_URL}/api/v1/webhook/whatsapp`);
    console.log(`   👤 Registration: ${BASE_URL}/api/v1/registration/request`);
    
    console.log('\n🧪 Manual test commands:');
    console.log(`   curl ${BASE_URL}/health`);
    console.log(`   curl ${BASE_URL}/api/v1/status`);
    console.log(`   curl -X POST ${BASE_URL}/api/v1/webhook/whatsapp -H "Content-Type: application/json" -d '{"From":"whatsapp:+**********","Body":"Hello PAIM"}'`);
    
    console.log('\n🚀 Ready for deployment!');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
  }
  
  return passed === total;
}

// Performance test
async function performanceTest() {
  console.log('\n⚡ Running Performance Test...');
  
  const iterations = 10;
  const times = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = Date.now();
    try {
      await makeRequest('/health');
      const end = Date.now();
      times.push(end - start);
      process.stdout.write('.');
    } catch (error) {
      process.stdout.write('x');
    }
  }
  
  console.log('\n');
  
  if (times.length > 0) {
    const avg = times.reduce((a, b) => a + b, 0) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    
    console.log(`📊 Performance Results (${iterations} requests):`);
    console.log(`   Average: ${avg.toFixed(2)}ms`);
    console.log(`   Min: ${min}ms`);
    console.log(`   Max: ${max}ms`);
    console.log(`   Success Rate: ${((times.length / iterations) * 100).toFixed(1)}%`);
    
    if (avg < 100) {
      console.log('   ✅ Performance: Excellent');
    } else if (avg < 500) {
      console.log('   ✅ Performance: Good');
    } else {
      console.log('   ⚠️  Performance: Needs optimization');
    }
  }
}

// Run all tests
async function main() {
  try {
    const success = await runTests();
    await performanceTest();
    
    if (success) {
      console.log('\n✅ All systems operational! PAIM is ready for deployment.');
      process.exit(0);
    } else {
      console.log('\n❌ Tests failed. Please fix issues before deployment.');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('\n💥 Unhandled error:', error.message);
  process.exit(1);
});

// Run tests
if (require.main === module) {
  main();
}

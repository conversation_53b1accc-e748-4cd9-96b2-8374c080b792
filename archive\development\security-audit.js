#!/usr/bin/env node

/**
 * Security Audit Script for PAIM Backend
 * Checks for common security vulnerabilities and best practices
 */

const fs = require('fs');
const path = require('path');

class SecurityAuditor {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.passed = [];
  }

  addIssue(type, file, line, message) {
    this.issues.push({ type, file, line, message });
  }

  addWarning(type, file, line, message) {
    this.warnings.push({ type, file, line, message });
  }

  addPassed(check, message) {
    this.passed.push({ check, message });
  }

  // Check for hardcoded secrets
  checkHardcodedSecrets(filePath, content) {
    const lines = content.split('\n');
    const secretPatterns = [
      { pattern: /sk-[a-zA-Z0-9]{48}/, name: 'OpenAI API Key' },
      { pattern: /AC[a-z0-9]{32}/, name: 'Twilio Account SID' },
      { pattern: /[a-z0-9]{32}/, name: '<PERSON>ten<PERSON> Auth Token' },
      { pattern: /(password|pwd)\s*[:=]\s*['"][^'"]+['"]/, name: 'Hardcoded Password' },
      { pattern: /(secret|key)\s*[:=]\s*['"][^'"]+['"]/, name: 'Hardcoded Secret' },
    ];

    lines.forEach((line, index) => {
      secretPatterns.forEach(({ pattern, name }) => {
        if (pattern.test(line) && !line.includes('example') && !line.includes('your-')) {
          this.addIssue('HARDCODED_SECRET', filePath, index + 1, `Potential ${name} found`);
        }
      });
    });
  }

  // Check for SQL injection vulnerabilities
  checkSqlInjection(filePath, content) {
    const sqlPatterns = [
      /query\s*\(\s*['"`][^'"`]*\$\{[^}]+\}[^'"`]*['"`]\s*\)/,
      /execute\s*\(\s*['"`][^'"`]*\+[^'"`]*['"`]\s*\)/,
      /SELECT.*FROM.*WHERE.*\$\{/,
    ];

    const lines = content.split('\n');
    lines.forEach((line, index) => {
      sqlPatterns.forEach(pattern => {
        if (pattern.test(line)) {
          this.addIssue('SQL_INJECTION', filePath, index + 1, 'Potential SQL injection vulnerability');
        }
      });
    });
  }

  // Check for XSS vulnerabilities
  checkXssVulnerabilities(filePath, content) {
    const xssPatterns = [
      /innerHTML\s*=\s*[^;]+\$\{/,
      /document\.write\s*\(/,
      /eval\s*\(/,
      /dangerouslySetInnerHTML/,
    ];

    const lines = content.split('\n');
    lines.forEach((line, index) => {
      xssPatterns.forEach(pattern => {
        if (pattern.test(line)) {
          this.addWarning('XSS_RISK', filePath, index + 1, 'Potential XSS vulnerability');
        }
      });
    });
  }

  // Check for insecure HTTP usage
  checkInsecureHttp(filePath, content) {
    const httpPatterns = [
      /http:\/\/(?!localhost|127\.0\.0\.1)/,
      /fetch\s*\(\s*['"`]http:/,
      /axios\s*\.\s*get\s*\(\s*['"`]http:/,
    ];

    const lines = content.split('\n');
    lines.forEach((line, index) => {
      httpPatterns.forEach(pattern => {
        if (pattern.test(line)) {
          this.addWarning('INSECURE_HTTP', filePath, index + 1, 'Insecure HTTP usage detected');
        }
      });
    });
  }

  // Check for proper error handling
  checkErrorHandling(filePath, content) {
    const hasErrorHandling = /try\s*\{[\s\S]*catch\s*\(/.test(content) ||
                            /\.catch\s*\(/.test(content) ||
                            /throw\s+new\s+\w*Error/.test(content);

    if (!hasErrorHandling && content.includes('async ')) {
      this.addWarning('ERROR_HANDLING', filePath, 0, 'Async function without error handling');
    }
  }

  // Check environment variable usage
  checkEnvironmentVariables(filePath, content) {
    const envUsage = content.match(/process\.env\.(\w+)/g);
    if (envUsage) {
      envUsage.forEach(match => {
        const varName = match.replace('process.env.', '');
        if (!varName.includes('NODE_ENV') && !varName.includes('PORT')) {
          // Check if there's a default value
          const hasDefault = new RegExp(`process\\.env\\.${varName}\\s*\\|\\|`).test(content);
          if (!hasDefault) {
            this.addWarning('ENV_NO_DEFAULT', filePath, 0, `Environment variable ${varName} has no default value`);
          }
        }
      });
    }
  }

  // Check for security headers
  checkSecurityHeaders(filePath, content) {
    if (filePath.includes('main.ts') || filePath.includes('app.module.ts')) {
      const securityHeaders = [
        { header: 'helmet', found: content.includes('helmet') },
        { header: 'cors', found: content.includes('cors') || content.includes('enableCors') },
        { header: 'validation', found: content.includes('ValidationPipe') },
      ];

      securityHeaders.forEach(({ header, found }) => {
        if (found) {
          this.addPassed('SECURITY_HEADERS', `${header} security middleware found`);
        } else {
          this.addIssue('MISSING_SECURITY', filePath, 0, `Missing ${header} security configuration`);
        }
      });
    }
  }

  // Check file permissions and structure
  checkFileStructure() {
    const sensitiveFiles = [
      '.env',
      '.env.local',
      '.env.production',
      'config/secrets.json',
      'private.key',
      'certificate.pem',
    ];

    sensitiveFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.addWarning('SENSITIVE_FILE', file, 0, 'Sensitive file found in repository');
      }
    });

    // Check if .env.example exists
    if (fs.existsSync('.env.example')) {
      this.addPassed('ENV_EXAMPLE', '.env.example file found');
    } else {
      this.addWarning('MISSING_ENV_EXAMPLE', '.', 0, '.env.example file not found');
    }
  }

  // Scan a single file
  scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      this.checkHardcodedSecrets(filePath, content);
      this.checkSqlInjection(filePath, content);
      this.checkXssVulnerabilities(filePath, content);
      this.checkInsecureHttp(filePath, content);
      this.checkErrorHandling(filePath, content);
      this.checkEnvironmentVariables(filePath, content);
      this.checkSecurityHeaders(filePath, content);
      
    } catch (error) {
      this.addWarning('FILE_READ_ERROR', filePath, 0, `Could not read file: ${error.message}`);
    }
  }

  // Recursively scan directory
  scanDirectory(dirPath, extensions = ['.ts', '.js', '.json']) {
    try {
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules' && item !== 'dist') {
          this.scanDirectory(itemPath, extensions);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          this.scanFile(itemPath);
        }
      });
    } catch (error) {
      this.addWarning('DIR_READ_ERROR', dirPath, 0, `Could not read directory: ${error.message}`);
    }
  }

  // Generate report
  generateReport() {
    console.log('\n🔒 SECURITY AUDIT REPORT\n');
    console.log('=' .repeat(50));

    // Critical Issues
    if (this.issues.length > 0) {
      console.log('\n❌ CRITICAL SECURITY ISSUES:');
      this.issues.forEach(issue => {
        console.log(`   ${issue.type}: ${issue.file}:${issue.line} - ${issue.message}`);
      });
    }

    // Warnings
    if (this.warnings.length > 0) {
      console.log('\n⚠️  SECURITY WARNINGS:');
      this.warnings.forEach(warning => {
        console.log(`   ${warning.type}: ${warning.file}:${warning.line} - ${warning.message}`);
      });
    }

    // Passed Checks
    if (this.passed.length > 0) {
      console.log('\n✅ SECURITY CHECKS PASSED:');
      this.passed.forEach(check => {
        console.log(`   ${check.check}: ${check.message}`);
      });
    }

    // Summary
    console.log('\n📊 SUMMARY:');
    console.log(`   Critical Issues: ${this.issues.length}`);
    console.log(`   Warnings: ${this.warnings.length}`);
    console.log(`   Passed Checks: ${this.passed.length}`);

    // Overall Assessment
    console.log('\n🎯 OVERALL SECURITY ASSESSMENT:');
    if (this.issues.length === 0 && this.warnings.length <= 3) {
      console.log('   ✅ GOOD - Security posture is acceptable');
    } else if (this.issues.length <= 2 && this.warnings.length <= 10) {
      console.log('   ⚠️  MODERATE - Some security improvements needed');
    } else {
      console.log('   ❌ POOR - Significant security issues require immediate attention');
    }

    return {
      issues: this.issues.length,
      warnings: this.warnings.length,
      passed: this.passed.length,
    };
  }

  // Run full audit
  runAudit() {
    console.log('🔍 Starting security audit...');
    
    // Check file structure
    this.checkFileStructure();
    
    // Scan source code
    this.scanDirectory('./src');
    
    // Scan configuration files
    const configFiles = ['package.json', 'tsconfig.json', 'nest-cli.json'];
    configFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.scanFile(file);
      }
    });
    
    // Generate and return report
    return this.generateReport();
  }
}

// Main execution
if (require.main === module) {
  const auditor = new SecurityAuditor();
  const results = auditor.runAudit();
  
  // Exit with appropriate code
  process.exit(results.issues > 0 ? 1 : 0);
}

module.exports = SecurityAuditor;

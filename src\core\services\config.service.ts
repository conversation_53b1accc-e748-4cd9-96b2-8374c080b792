import { Injectable } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import { ConfigurationError } from './error-handler.service';

@Injectable()
export class AppConfigService {
  constructor(private configService: NestConfigService) {}

  // Application Configuration
  getNodeEnv(): string {
    return this.configService.get<string>('NODE_ENV', 'development');
  }

  getPort(): number {
    return this.configService.get<number>('PORT', 3000);
  }

  isDevelopment(): boolean {
    return this.getNodeEnv() === 'development';
  }

  isProduction(): boolean {
    return this.getNodeEnv() === 'production';
  }

  // OpenAI Configuration
  getOpenAIConfig() {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new ConfigurationError('OPENAI_API_KEY', 'API key is required');
    }

    return {
      apiKey,
      model: this.configService.get<string>('OPENAI_MODEL', 'gpt-4'),
      maxTokens: this.configService.get<number>('OPENAI_MAX_TOKENS', 1000),
      temperature: this.configService.get<number>('OPENAI_TEMPERATURE', 0.7),
      whisperModel: this.configService.get<string>(
        'WHISPER_MODEL',
        'whisper-1',
      ),
      ttsVoice: this.configService.get<string>('TTS_VOICE', 'alloy'),
      ttsModel: this.configService.get<string>('TTS_MODEL', 'tts-1'),
    };
  }

  // Twilio Configuration
  getTwilioConfig() {
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
    const whatsappNumber = this.configService.get<string>(
      'TWILIO_WHATSAPP_NUMBER',
    );
    const webhookUrl = this.configService.get<string>('TWILIO_WEBHOOK_URL');

    if (!accountSid || !authToken || !whatsappNumber || !webhookUrl) {
      throw new ConfigurationError(
        'TWILIO_CONFIG',
        'All Twilio configuration values are required',
      );
    }

    return {
      accountSid,
      authToken,
      whatsappNumber,
      webhookUrl,
    };
  }

  // Security Configuration
  getSecurityConfig() {
    const jwtSecret = this.configService.get<string>('JWT_SECRET');
    const encryptionKey = this.configService.get<string>('ENCRYPTION_KEY');

    if (!jwtSecret || jwtSecret.length < 32) {
      throw new ConfigurationError(
        'JWT_SECRET',
        'JWT secret must be at least 32 characters',
      );
    }

    if (!encryptionKey || encryptionKey.length < 32) {
      throw new ConfigurationError(
        'ENCRYPTION_KEY',
        'Encryption key must be at least 32 characters',
      );
    }

    return {
      jwtSecret,
      encryptionKey,
    };
  }

  // Storage Configuration
  getStorageConfig() {
    return {
      path: this.configService.get<string>('STORAGE_PATH', './storage'),
      maxFileSize: this.configService.get<number>('MAX_FILE_SIZE', ********), // 10MB
    };
  }

  // Rate Limiting Configuration
  getRateLimitConfig() {
    return {
      ttl: this.configService.get<number>('THROTTLE_TTL', 60),
      limit: this.configService.get<number>('THROTTLE_LIMIT', 10),
    };
  }

  // CORS Configuration
  getCorsConfig() {
    return {
      origin: this.configService.get<string>('CORS_ORIGIN', '*'),
    };
  }

  // Logging Configuration
  getLoggingConfig() {
    return {
      level: this.configService.get<string>('LOG_LEVEL', 'info'),
      filePath: this.configService.get<string>('LOG_FILE_PATH', './logs'),
    };
  }

  // Feature Flags
  getFeatureFlags() {
    return {
      enableSwagger: this.configService.get<boolean>('ENABLE_SWAGGER', true),
      detailedErrors: this.configService.get<boolean>('DETAILED_ERRORS', true),
    };
  }

  // Memory Configuration
  getMemoryConfig() {
    return {
      maxMemoriesPerUser: this.configService.get<number>(
        'MAX_MEMORIES_PER_USER',
        10000,
      ),
      retentionDays: this.configService.get<number>('MEMORY_RETENTION_DAYS', 0), // 0 = unlimited
    };
  }

  // Skill Configuration
  getSkillConfig() {
    return {
      defaultTone: this.configService.get<string>('DEFAULT_TONE', 'friendly'),
      timeout: this.configService.get<number>('SKILL_TIMEOUT', 30),
    };
  }

  // Validation helper
  validateRequiredConfig(): void {
    try {
      this.getOpenAIConfig();
      this.getTwilioConfig();
      this.getSecurityConfig();
    } catch (error) {
      if (error instanceof ConfigurationError) {
        throw error;
      }
      throw new ConfigurationError(
        'GENERAL',
        'Configuration validation failed',
      );
    }
  }

  // Get all configuration for debugging (without sensitive data)
  getAllConfig() {
    return {
      app: {
        nodeEnv: this.getNodeEnv(),
        port: this.getPort(),
      },
      openai: {
        model: this.configService.get<string>('OPENAI_MODEL'),
        maxTokens: this.configService.get<number>('OPENAI_MAX_TOKENS'),
        temperature: this.configService.get<number>('OPENAI_TEMPERATURE'),
      },
      storage: this.getStorageConfig(),
      rateLimit: this.getRateLimitConfig(),
      cors: this.getCorsConfig(),
      logging: this.getLoggingConfig(),
      features: this.getFeatureFlags(),
      memory: this.getMemoryConfig(),
      skill: this.getSkillConfig(),
    };
  }
}

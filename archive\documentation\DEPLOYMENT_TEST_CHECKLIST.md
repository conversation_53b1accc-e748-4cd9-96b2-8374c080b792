# 🧪 Deployment Testing Checklist

## Pre-Deployment Verification

### ✅ Environment Variables
- [ ] All environment variables copied to Digital Ocean dashboard
- [ ] Appwrite credentials are correct
- [ ] Twilio credentials are valid
- [ ] OpenAI API key is working
- [ ] Digital Ocean Spaces credentials are set

### ✅ Appwrite Setup
- [ ] All 9 collections created with proper attributes
- [ ] Indexes created for each collection
- [ ] Permissions configured correctly
- [ ] API key has proper scopes

## Post-Deployment Testing

### 🏥 Health Checks

#### Basic Health Check
```bash
curl https://your-app-url.ondigitalocean.app/health
```
**Expected**: `{"status": "ok", ...}`

#### Detailed Health Check
```bash
curl https://your-app-url.ondigitalocean.app/health/detailed
```
**Expected**: Detailed system status

### 📡 API Endpoints

#### API Documentation
```bash
curl https://your-app-url.ondigitalocean.app/api/docs
```
**Expected**: Swagger/OpenAPI documentation

#### Registration Endpoint
```bash
curl -X POST https://your-app-url.ondigitalocean.app/api/v1/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User",
    "phoneNumber": "+**********"
  }'
```
**Expected**: Registration confirmation

#### Admin Dashboard
```bash
curl https://your-app-url.ondigitalocean.app/admin/dashboard \
  -H "x-admin-key: your-admin-api-key"
```
**Expected**: Admin dashboard data

### 🗄️ Database Connectivity

#### Test Appwrite Connection
```bash
node scripts/test-appwrite-connection.js
```
**Expected**: All collections verified

#### Test Database Operations
```bash
curl -X GET https://your-app-url.ondigitalocean.app/api/v1/admin/users \
  -H "x-admin-key: your-admin-api-key"
```
**Expected**: User list (empty initially)

### 📱 WhatsApp Integration

#### Webhook Endpoint
```bash
curl -X POST https://your-app-url.ondigitalocean.app/api/v1/webhook/whatsapp \
  -H "Content-Type: application/json" \
  -d '{
    "From": "whatsapp:+**********",
    "Body": "test message"
  }'
```
**Expected**: Webhook processed

#### Update Twilio Webhook URL
1. Go to Twilio Console → WhatsApp → Senders
2. Update webhook URL to: `https://your-app-url.ondigitalocean.app/api/v1/webhook/whatsapp`
3. Test by sending a WhatsApp message

### 📧 Email Functionality

#### Test Email Service (Appwrite)
```bash
curl -X POST https://your-app-url.ondigitalocean.app/api/v1/test-email \
  -H "Content-Type: application/json" \
  -H "x-admin-key: your-admin-api-key" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "This is a test email"
  }'
```

### 🔒 Security Testing

#### CORS Configuration
```bash
curl -H "Origin: https://example.com" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: X-Requested-With" \
  -X OPTIONS https://your-app-url.ondigitalocean.app/api/v1/register
```

#### Rate Limiting
```bash
# Send multiple requests quickly to test rate limiting
for i in {1..15}; do
  curl https://your-app-url.ondigitalocean.app/health
done
```

### 📊 Performance Testing

#### Response Times
```bash
curl -w "@curl-format.txt" -o /dev/null -s https://your-app-url.ondigitalocean.app/health
```

#### Load Testing (optional)
```bash
# Install Apache Bench: apt-get install apache2-utils
ab -n 100 -c 10 https://your-app-url.ondigitalocean.app/health
```

## Integration Testing

### 🔄 End-to-End User Flow

1. **Registration Flow**:
   - [ ] User submits registration form
   - [ ] Email confirmation sent
   - [ ] User confirms email
   - [ ] Admin selects as early adopter
   - [ ] User can access WhatsApp bot

2. **WhatsApp Flow**:
   - [ ] Send message to WhatsApp number
   - [ ] Receive appropriate response
   - [ ] Conversation context maintained
   - [ ] Memory system working

3. **Admin Flow**:
   - [ ] Access admin dashboard
   - [ ] View registrations
   - [ ] Manage whitelist
   - [ ] View analytics

## Troubleshooting

### Common Issues

#### Build Failures
- Check TypeScript compilation: `npm run typecheck`
- Verify dependencies: `npm install`
- Check build locally: `npm run build`

#### Environment Variable Issues
- Verify all variables are set in Digital Ocean
- Check variable names (case-sensitive)
- Redeploy after adding new variables

#### Appwrite Connection Issues
- Verify API key permissions
- Check project and database IDs
- Test connection with script

#### WhatsApp Webhook Issues
- Verify webhook URL in Twilio
- Check endpoint accessibility
- Review application logs

### Monitoring Commands

```bash
# Digital Ocean app logs
doctl apps logs YOUR_APP_ID

# App status
doctl apps get YOUR_APP_ID

# List deployments
doctl apps list-deployments YOUR_APP_ID
```

## Success Criteria

- [ ] All health checks pass
- [ ] API endpoints respond correctly
- [ ] Appwrite connectivity verified
- [ ] WhatsApp integration working
- [ ] Email functionality operational
- [ ] Admin dashboard accessible
- [ ] Security measures in place
- [ ] Performance within acceptable limits

**🎉 Deployment is successful when all items are checked!**

import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { IUser, IUserPreferences } from '../../interfaces';
import { LoggerService } from '../../core';
import { AppwriteService } from '../../appwrite/appwrite.service';

@Injectable()
export class UserAppwriteService {
  constructor(
    private logger: LoggerService,
    private appwriteService: AppwriteService,
  ) {}

  async createUser(userData: Partial<IUser>): Promise<IUser> {
    try {
      const userId = userData.id || uuidv4();
      const now = new Date();

      const defaultPreferences: IUserPreferences = {
        language: 'en',
        timezone: 'UTC',
        voiceEnabled: true,
        notificationsEnabled: true,
        maxMemories: 1000,
        defaultSkills: [],
      };

      const user: IUser = {
        id: userId,
        phoneNumber: userData.phoneNumber!,
        name: userData.name || 'Unknown User',
        tone: userData.tone || 'friendly',
        isOnboarded: userData.isOnboarded || false,
        isWhitelisted: userData.isWhitelisted || false,
        isEarlyAdopter: userData.isEarlyAdopter || false,
        registrationSource: userData.registrationSource || 'whatsapp',
        preferences: userData.preferences || defaultPreferences,
        createdAt: now,
        updatedAt: now,
        lastActiveAt: now,
      };

      // Store user in Appwrite
      await this.appwriteService.createDocument(
        this.appwriteService.getCollectionId('users'),
        userId,
        {
          phoneNumber: user.phoneNumber,
          name: user.name,
          tone: user.tone,
          isOnboarded: user.isOnboarded,
          isWhitelisted: user.isWhitelisted,
          isEarlyAdopter: user.isEarlyAdopter,
          registrationSource: user.registrationSource,
          preferences: JSON.stringify(user.preferences),
          lastActiveAt: now.toISOString(),
          createdAt: now.toISOString(),
          updatedAt: now.toISOString(),
        },
      );

      this.logger.log(
        `Created user ${userId} with phone ${user.phoneNumber}`,
        'UserAppwriteService',
      );
      return user;
    } catch (error) {
      this.logger.error(
        `Failed to create user`,
        error instanceof Error ? error.stack : undefined,
        'UserAppwriteService',
      );
      throw error;
    }
  }

  async getUserByPhoneNumber(phoneNumber: string): Promise<IUser | undefined> {
    try {
      const result = await this.appwriteService.listDocuments(
        this.appwriteService.getCollectionId('users'),
        [`equal("phoneNumber", "${phoneNumber}")`],
      );

      if (result.documents.length === 0) {
        return undefined;
      }

      return this.mapDocumentToUser(result.documents[0]);
    } catch (error) {
      this.logger.error(
        `Failed to get user by phone number ${phoneNumber}`,
        error instanceof Error ? error.stack : undefined,
        'UserAppwriteService',
      );
      return undefined;
    }
  }

  async getUserById(userId: string): Promise<IUser | undefined> {
    try {
      const document = await this.appwriteService.getDocument(
        this.appwriteService.getCollectionId('users'),
        userId,
      );

      return this.mapDocumentToUser(document);
    } catch (error: any) {
      if (error.code === 404) {
        return undefined;
      }

      this.logger.error(
        `Failed to get user by ID ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'UserAppwriteService',
      );
      return undefined;
    }
  }

  async updateUser(
    userId: string,
    updates: Partial<IUser>,
  ): Promise<IUser | undefined> {
    try {
      const updateData: any = {};

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.tone !== undefined) updateData.tone = updates.tone;
      if (updates.isOnboarded !== undefined)
        updateData.isOnboarded = updates.isOnboarded;
      if (updates.isWhitelisted !== undefined)
        updateData.isWhitelisted = updates.isWhitelisted;
      if (updates.isEarlyAdopter !== undefined)
        updateData.isEarlyAdopter = updates.isEarlyAdopter;
      if (updates.registrationSource !== undefined)
        updateData.registrationSource = updates.registrationSource;
      if (updates.preferences !== undefined)
        updateData.preferences = JSON.stringify(updates.preferences);
      if (updates.lastActiveAt !== undefined)
        updateData.lastActiveAt = updates.lastActiveAt.toISOString();

      const document = await this.appwriteService.updateDocument(
        this.appwriteService.getCollectionId('users'),
        userId,
        updateData,
      );

      this.logger.log(`Updated user ${userId}`, 'UserAppwriteService');
      return this.mapDocumentToUser(document);
    } catch (error) {
      this.logger.error(
        `Failed to update user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'UserAppwriteService',
      );
      return undefined;
    }
  }

  async updateLastActiveAt(userId: string): Promise<void> {
    try {
      await this.appwriteService.updateDocument(
        this.appwriteService.getCollectionId('users'),
        userId,
        {
          lastActiveAt: new Date().toISOString(),
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to update last active time for user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'UserAppwriteService',
      );
    }
  }

  private mapDocumentToUser(document: any): IUser {
    return {
      id: document.$id,
      phoneNumber: document.phoneNumber,
      name: document.name,
      tone: document.tone,
      isOnboarded: document.isOnboarded,
      isWhitelisted: document.isWhitelisted,
      isEarlyAdopter: document.isEarlyAdopter,
      registrationSource: document.registrationSource,
      preferences: JSON.parse(document.preferences || '{}'),
      createdAt: new Date(document.createdAt),
      updatedAt: new Date(document.updatedAt),
      lastActiveAt: new Date(document.lastActiveAt),
    };
  }
}

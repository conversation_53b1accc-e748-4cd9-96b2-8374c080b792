#!/usr/bin/env node

/**
 * Appwrite Connection Test Script
 * Tests connectivity to Appwrite and verifies collections exist
 */

const { Client, Databases } = require('node-appwrite');

// Load environment variables
require('dotenv').config({ path: '.env.digitalocean' });

const client = new Client();
const databases = new Databases(client);

// Configure Appwrite client
client
  .setEndpoint(process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

const databaseId = process.env.APPWRITE_DATABASE_ID;

// Collection IDs to test
const collections = {
  users: process.env.APPWRITE_USERS_COLLECTION_ID || 'users',
  registrations: process.env.APPWRITE_REGISTRATIONS_COLLECTION_ID || 'registrations',
  whitelist: process.env.APPWRITE_WHITELIST_COLLECTION_ID || 'whitelist',
  sessions: process.env.APPWRITE_SESSIONS_COLLECTION_ID || 'sessions',
  memories: process.env.APPWRITE_MEMORIES_COLLECTION_ID || 'memories',
  reminders: process.env.APPWRITE_REMINDERS_COLLECTION_ID || 'reminders',
  conversations: process.env.APPWRITE_CONVERSATIONS_COLLECTION_ID || 'conversations',
  emails: process.env.APPWRITE_EMAILS_COLLECTION_ID || 'emails',
  emailConfirmations: process.env.APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID || 'email_confirmations',
};

async function testAppwriteConnection() {
  console.log('🔍 Testing Appwrite Connection...\n');
  
  try {
    // Test 1: Database connectivity
    console.log('1. Testing database connectivity...');
    const database = await databases.get(databaseId);
    console.log(`✅ Connected to database: ${database.name} (${database.$id})\n`);
    
    // Test 2: List all collections
    console.log('2. Testing collections...');
    const collectionsResponse = await databases.listCollections(databaseId);
    const existingCollections = collectionsResponse.collections.map(c => c.$id);
    
    console.log(`📊 Found ${existingCollections.length} collections in database:`);
    existingCollections.forEach(id => console.log(`   - ${id}`));
    console.log();
    
    // Test 3: Verify required collections exist
    console.log('3. Verifying required collections...');
    let missingCollections = [];
    
    for (const [name, id] of Object.entries(collections)) {
      if (existingCollections.includes(id)) {
        console.log(`✅ ${name} (${id})`);
      } else {
        console.log(`❌ ${name} (${id}) - MISSING`);
        missingCollections.push({ name, id });
      }
    }
    
    if (missingCollections.length > 0) {
      console.log(`\n⚠️  Missing ${missingCollections.length} collections:`);
      missingCollections.forEach(({ name, id }) => {
        console.log(`   - ${name}: ${id}`);
      });
      console.log('\nPlease create these collections using the Appwrite setup guide.');
      process.exit(1);
    }
    
    console.log('\n🎉 All Appwrite collections are properly configured!');
    
  } catch (error) {
    console.error('❌ Appwrite connection failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    
    if (error.code === 401) {
      console.error('\n💡 This is likely an authentication issue. Please check:');
      console.error('   - APPWRITE_API_KEY is correct');
      console.error('   - API key has proper permissions');
      console.error('   - Project ID is correct');
    } else if (error.code === 404) {
      console.error('\n💡 This is likely a configuration issue. Please check:');
      console.error('   - APPWRITE_PROJECT_ID is correct');
      console.error('   - APPWRITE_DATABASE_ID is correct');
      console.error('   - Database exists in your Appwrite project');
    }
    
    process.exit(1);
  }
}

// Run the test
testAppwriteConnection();

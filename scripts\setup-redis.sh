#!/bin/bash

# Redis Setup Script for PAIM
# Sets up Redis caching infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REDIS_COMPOSE_FILE="docker-compose.redis.yml"
REDIS_CONFIG_DIR="redis"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    success "Prerequisites check passed"
}

# Create Redis directories
create_directories() {
    log "Creating Redis directories..."
    
    mkdir -p "$REDIS_CONFIG_DIR"
    mkdir -p "backups"
    
    success "Directories created"
}

# Start Redis services
start_redis() {
    log "Starting Redis services..."
    
    if [ ! -f "$REDIS_COMPOSE_FILE" ]; then
        error "Redis Compose file not found: $REDIS_COMPOSE_FILE"
    fi
    
    # Start basic Redis setup
    docker-compose -f "$REDIS_COMPOSE_FILE" up -d redis redis-commander redis-exporter
    
    success "Redis services started"
}

# Start Redis with high availability
start_redis_ha() {
    log "Starting Redis with high availability..."
    
    docker-compose -f "$REDIS_COMPOSE_FILE" --profile ha up -d
    
    success "Redis HA services started"
}

# Start Redis cluster
start_redis_cluster() {
    log "Starting Redis cluster..."
    
    docker-compose -f "$REDIS_COMPOSE_FILE" --profile cluster up -d
    
    # Wait for cluster nodes to start
    sleep 10
    
    # Initialize cluster
    log "Initializing Redis cluster..."
    docker exec paim-redis-cluster-1 redis-cli --cluster create \
        127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 \
        --cluster-replicas 0 --cluster-yes
    
    success "Redis cluster initialized"
}

# Wait for Redis to be ready
wait_for_redis() {
    log "Waiting for Redis to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec paim-redis redis-cli ping > /dev/null 2>&1; then
            success "Redis is ready"
            return 0
        fi
        
        log "Waiting for Redis... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    error "Redis failed to start within expected time"
}

# Test Redis functionality
test_redis() {
    log "Testing Redis functionality..."
    
    # Basic connectivity test
    if ! docker exec paim-redis redis-cli ping > /dev/null; then
        error "Redis ping test failed"
    fi
    
    # Set/Get test
    docker exec paim-redis redis-cli set test_key "test_value" > /dev/null
    local value=$(docker exec paim-redis redis-cli get test_key)
    
    if [ "$value" != "test_value" ]; then
        error "Redis set/get test failed"
    fi
    
    # Cleanup test key
    docker exec paim-redis redis-cli del test_key > /dev/null
    
    success "Redis functionality tests passed"
}

# Configure Redis for PAIM
configure_redis() {
    log "Configuring Redis for PAIM..."
    
    # Set up PAIM-specific configuration
    docker exec paim-redis redis-cli config set maxmemory-policy allkeys-lru
    docker exec paim-redis redis-cli config set maxmemory 256mb
    
    # Create PAIM namespace
    docker exec paim-redis redis-cli set "paim:config:version" "1.0.0"
    
    success "Redis configured for PAIM"
}

# Display access information
display_access_info() {
    log "Redis setup completed!"
    
    echo ""
    echo "🔗 Access URLs:"
    echo "  📊 Redis Commander: http://localhost:8081 (admin/admin123)"
    echo "  🔍 Redis Insight:   http://localhost:8001"
    echo "  📈 Redis Exporter:  http://localhost:9121/metrics"
    echo ""
    echo "🔧 Redis Connection:"
    echo "  Host: localhost"
    echo "  Port: 6379"
    echo "  Database: 0"
    echo ""
    echo "📋 Management Commands:"
    echo "  Connect: docker exec -it paim-redis redis-cli"
    echo "  Monitor: docker exec paim-redis redis-cli monitor"
    echo "  Info: docker exec paim-redis redis-cli info"
    echo ""
    echo "🛠️ Environment Variables for PAIM:"
    echo "  REDIS_HOST=localhost"
    echo "  REDIS_PORT=6379"
    echo "  REDIS_DB=0"
    echo ""
}

# Backup Redis data
backup_redis() {
    log "Creating Redis backup..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="backups/redis_backup_${timestamp}.rdb"
    
    # Create backup
    docker exec paim-redis redis-cli bgsave
    
    # Wait for backup to complete
    while [ "$(docker exec paim-redis redis-cli lastsave)" = "$(docker exec paim-redis redis-cli lastsave)" ]; do
        sleep 1
    done
    
    # Copy backup file
    docker cp paim-redis:/data/dump.rdb "$backup_file"
    
    success "Redis backup created: $backup_file"
}

# Restore Redis data
restore_redis() {
    local backup_file="$1"
    
    if [ -z "$backup_file" ] || [ ! -f "$backup_file" ]; then
        error "Backup file not found: $backup_file"
    fi
    
    log "Restoring Redis from backup: $backup_file"
    
    # Stop Redis
    docker-compose -f "$REDIS_COMPOSE_FILE" stop redis
    
    # Copy backup file
    docker cp "$backup_file" paim-redis:/data/dump.rdb
    
    # Start Redis
    docker-compose -f "$REDIS_COMPOSE_FILE" start redis
    
    wait_for_redis
    success "Redis restored from backup"
}

# Main execution
main() {
    log "🚀 Setting up Redis caching for PAIM..."
    
    check_prerequisites
    create_directories
    start_redis
    wait_for_redis
    test_redis
    configure_redis
    display_access_info
    
    success "Redis setup completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "stop")
        log "Stopping Redis services..."
        docker-compose -f "$REDIS_COMPOSE_FILE" down
        success "Redis services stopped"
        ;;
    "restart")
        log "Restarting Redis services..."
        docker-compose -f "$REDIS_COMPOSE_FILE" restart
        success "Redis services restarted"
        ;;
    "logs")
        log "Showing Redis logs..."
        docker-compose -f "$REDIS_COMPOSE_FILE" logs -f redis
        ;;
    "status")
        log "Checking Redis status..."
        docker-compose -f "$REDIS_COMPOSE_FILE" ps
        ;;
    "backup")
        backup_redis
        ;;
    "restore")
        restore_redis "$2"
        ;;
    "ha")
        log "Setting up Redis with high availability..."
        check_prerequisites
        create_directories
        start_redis_ha
        wait_for_redis
        test_redis
        configure_redis
        display_access_info
        ;;
    "cluster")
        log "Setting up Redis cluster..."
        check_prerequisites
        create_directories
        start_redis_cluster
        wait_for_redis
        test_redis
        display_access_info
        ;;
    *)
        main
        ;;
esac

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import twilio from 'twilio';
import {
  LoggerService,
  ErrorHandlerService,
  ExternalServiceError,
} from '../../core';

export interface TwilioConfig {
  accountSid: string;
  authToken: string;
  whatsappNumber: string;
  webhookUrl: string;
}

export interface SendMessageOptions {
  to: string;
  body: string;
  mediaUrl?: string;
  mediaType?: string;
}

export interface SendMessageResult {
  messageSid: string;
  status: string;
  success: boolean;
  error?: string;
}

@Injectable()
export class TwilioService {
  private client: twilio.Twilio;
  private config: TwilioConfig;

  constructor(
    private configService: ConfigService,
    private logger: LoggerService,
    private errorHandler: ErrorHandlerService,
  ) {
    this.initializeTwilioClient();
  }

  private initializeTwilioClient(): void {
    try {
      this.config = {
        accountSid: this.configService.get<string>('TWILIO_ACCOUNT_SID'),
        authToken: this.configService.get<string>('TWILIO_AUTH_TOKEN'),
        whatsappNumber: this.configService.get<string>(
          'TWILIO_WHATSAPP_NUMBER',
        ),
        webhookUrl: this.configService.get<string>('TWILIO_WEBHOOK_URL'),
      };

      if (!this.config.accountSid || !this.config.authToken) {
        throw new Error('Twilio credentials are required');
      }

      this.client = twilio(this.config.accountSid, this.config.authToken);

      this.logger.log(
        'Twilio client initialized successfully',
        'TwilioService',
      );
    } catch (error) {
      this.logger.error(
        'Failed to initialize Twilio client',
        error instanceof Error ? error.stack : undefined,
        'TwilioService',
      );
      throw new ExternalServiceError(
        'twilio',
        'initialization',
        error instanceof Error ? error : new Error(String(error)),
      );
    }
  }

  async sendMessage(options: SendMessageOptions): Promise<SendMessageResult> {
    try {
      this.logger.log(
        `Sending WhatsApp message to ${options.to}`,
        'TwilioService',
      );

      const messageOptions: any = {
        from: this.config.whatsappNumber,
        to: options.to,
        body: options.body,
      };

      // Add media if provided
      if (options.mediaUrl) {
        messageOptions.mediaUrl = [options.mediaUrl];
      }

      const message = await this.client.messages.create(messageOptions);

      this.logger.log(
        `WhatsApp message sent successfully: ${message.sid}`,
        'TwilioService',
      );

      return {
        messageSid: message.sid,
        status: message.status,
        success: true,
      };
    } catch (error) {
      this.logger.error(
        `Failed to send WhatsApp message to ${options.to}`,
        error instanceof Error ? error.stack : undefined,
        'TwilioService',
      );

      const handledError = this.errorHandler.handleError(
        new ExternalServiceError(
          'twilio',
          'sendMessage',
          error instanceof Error ? error : new Error(String(error)),
        ),
        'TwilioService',
      );

      return {
        messageSid: '',
        status: 'failed',
        success: false,
        error: handledError.message,
      };
    }
  }

  async sendTextMessage(to: string, body: string): Promise<SendMessageResult> {
    return this.sendMessage({ to, body });
  }

  async sendMediaMessage(
    to: string,
    body: string,
    mediaUrl: string,
    mediaType?: string,
  ): Promise<SendMessageResult> {
    return this.sendMessage({ to, body, mediaUrl, mediaType });
  }

  validateWebhookSignature(signature: string, url: string, body: any): boolean {
    try {
      const expectedSignature = twilio.validateRequest(
        this.config.authToken,
        signature,
        url,
        body,
      );

      return expectedSignature;
    } catch (error) {
      this.logger.error(
        'Failed to validate webhook signature',
        error instanceof Error ? error.stack : undefined,
        'TwilioService',
      );
      return false;
    }
  }

  async getMessageStatus(messageSid: string): Promise<any> {
    try {
      const message = await this.client.messages(messageSid).fetch();

      this.logger.debug(
        `Retrieved message status for ${messageSid}: ${message.status}`,
        'TwilioService',
      );

      return {
        sid: message.sid,
        status: message.status,
        errorCode: message.errorCode,
        errorMessage: message.errorMessage,
        dateCreated: message.dateCreated,
        dateUpdated: message.dateUpdated,
        dateSent: message.dateSent,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get message status for ${messageSid}`,
        error instanceof Error ? error.stack : undefined,
        'TwilioService',
      );
      throw new ExternalServiceError(
        'twilio',
        'getMessageStatus',
        error instanceof Error ? error : new Error(String(error)),
      );
    }
  }

  async downloadMedia(mediaUrl: string): Promise<Buffer> {
    try {
      this.logger.log(`Downloading media from ${mediaUrl}`, 'TwilioService');

      // Use Twilio's authenticated request to download media
      const response = await this.client.request({
        method: 'GET' as any,
        uri: mediaUrl,
      });

      this.logger.log('Media downloaded successfully', 'TwilioService');
      return Buffer.from(response, 'binary');
    } catch (error) {
      this.logger.error(
        `Failed to download media from ${mediaUrl}`,
        error instanceof Error ? error.stack : undefined,
        'TwilioService',
      );
      throw new ExternalServiceError(
        'twilio',
        'downloadMedia',
        error instanceof Error ? error : new Error(String(error)),
      );
    }
  }

  formatPhoneNumber(phoneNumber: string): string {
    // Ensure phone number is in WhatsApp format
    if (phoneNumber.startsWith('whatsapp:')) {
      return phoneNumber;
    }

    // Add whatsapp: prefix if not present
    if (phoneNumber.startsWith('+')) {
      return `whatsapp:${phoneNumber}`;
    }

    // Assume it needs a + prefix as well
    return `whatsapp:+${phoneNumber}`;
  }

  isValidWhatsAppNumber(phoneNumber: string): boolean {
    const whatsappPattern = /^whatsapp:\+[1-9]\d{1,14}$/;
    return whatsappPattern.test(phoneNumber);
  }

  getConfig(): TwilioConfig {
    return { ...this.config };
  }

  async testConnection(): Promise<boolean> {
    try {
      // Test connection by fetching account info
      const account = await this.client.api
        .accounts(this.config.accountSid)
        .fetch();

      this.logger.log(
        `Twilio connection test successful. Account: ${account.friendlyName}`,
        'TwilioService',
      );

      return true;
    } catch (error) {
      this.logger.error(
        'Twilio connection test failed',
        error instanceof Error ? error.stack : undefined,
        'TwilioService',
      );
      return false;
    }
  }
}

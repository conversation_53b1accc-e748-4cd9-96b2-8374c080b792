import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string; // Key prefix
  serialize?: boolean; // Whether to JSON serialize/deserialize
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTTL = 3600; // 1 hour
  private readonly keyPrefix = 'paim:';
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
  };

  // In-memory fallback cache
  private memoryCache = new Map<string, { value: any; expires: number }>();
  private memoryCleanupInterval: NodeJS.Timeout;

  constructor(private readonly redisService: RedisService) {
    // Setup memory cache cleanup
    this.memoryCleanupInterval = setInterval(() => {
      this.cleanupMemoryCache();
    }, 60000); // Cleanup every minute
  }

  onModuleDestroy() {
    if (this.memoryCleanupInterval) {
      clearInterval(this.memoryCleanupInterval);
    }
  }

  private buildKey(key: string, prefix?: string): string {
    const finalPrefix = prefix || this.keyPrefix;
    return `${finalPrefix}${key}`;
  }

  private cleanupMemoryCache() {
    const now = Date.now();
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.expires > 0 && item.expires < now) {
        this.memoryCache.delete(key);
      }
    }
  }

  async get<T = any>(
    key: string,
    options: CacheOptions = {},
  ): Promise<T | null> {
    const fullKey = this.buildKey(key, options.prefix);

    try {
      // Try Redis first
      if (this.redisService.isHealthy()) {
        const value = await this.redisService.get(fullKey);
        if (value !== null) {
          this.stats.hits++;
          return options.serialize !== false ? JSON.parse(value) : (value as T);
        }
      }

      // Fallback to memory cache
      const memoryItem = this.memoryCache.get(fullKey);
      if (memoryItem) {
        if (memoryItem.expires === 0 || memoryItem.expires > Date.now()) {
          this.stats.hits++;
          return memoryItem.value;
        } else {
          this.memoryCache.delete(fullKey);
        }
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      this.logger.error(`Cache GET error for key ${fullKey}:`, error);
      this.stats.errors++;
      return null;
    }
  }

  async set<T = any>(
    key: string,
    value: T,
    options: CacheOptions = {},
  ): Promise<boolean> {
    const fullKey = this.buildKey(key, options.prefix);
    const ttl = options.ttl || this.defaultTTL;

    try {
      const serializedValue =
        options.serialize !== false ? JSON.stringify(value) : (value as string);

      // Try Redis first
      if (this.redisService.isHealthy()) {
        const success = await this.redisService.set(
          fullKey,
          serializedValue,
          ttl,
        );
        if (success) {
          this.stats.sets++;
          return true;
        }
      }

      // Fallback to memory cache
      const expires = ttl > 0 ? Date.now() + ttl * 1000 : 0;
      this.memoryCache.set(fullKey, { value, expires });
      this.stats.sets++;
      return true;
    } catch (error) {
      this.logger.error(`Cache SET error for key ${fullKey}:`, error);
      this.stats.errors++;
      return false;
    }
  }

  async del(key: string, options: CacheOptions = {}): Promise<boolean> {
    const fullKey = this.buildKey(key, options.prefix);

    try {
      let deleted = false;

      // Try Redis first
      if (this.redisService.isHealthy()) {
        deleted = await this.redisService.del(fullKey);
      }

      // Also delete from memory cache
      if (this.memoryCache.has(fullKey)) {
        this.memoryCache.delete(fullKey);
        deleted = true;
      }

      if (deleted) {
        this.stats.deletes++;
      }

      return deleted;
    } catch (error) {
      this.logger.error(`Cache DEL error for key ${fullKey}:`, error);
      this.stats.errors++;
      return false;
    }
  }

  async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    const fullKey = this.buildKey(key, options.prefix);

    try {
      // Try Redis first
      if (this.redisService.isHealthy()) {
        const exists = await this.redisService.exists(fullKey);
        if (exists) return true;
      }

      // Check memory cache
      const memoryItem = this.memoryCache.get(fullKey);
      if (memoryItem) {
        if (memoryItem.expires === 0 || memoryItem.expires > Date.now()) {
          return true;
        } else {
          this.memoryCache.delete(fullKey);
        }
      }

      return false;
    } catch (error) {
      this.logger.error(`Cache EXISTS error for key ${fullKey}:`, error);
      this.stats.errors++;
      return false;
    }
  }

  async expire(
    key: string,
    ttl: number,
    options: CacheOptions = {},
  ): Promise<boolean> {
    const fullKey = this.buildKey(key, options.prefix);

    try {
      // Try Redis first
      if (this.redisService.isHealthy()) {
        const success = await this.redisService.expire(fullKey, ttl);
        if (success) return true;
      }

      // Update memory cache expiration
      const memoryItem = this.memoryCache.get(fullKey);
      if (memoryItem) {
        memoryItem.expires = ttl > 0 ? Date.now() + ttl * 1000 : 0;
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Cache EXPIRE error for key ${fullKey}:`, error);
      this.stats.errors++;
      return false;
    }
  }

  async ttl(key: string, options: CacheOptions = {}): Promise<number> {
    const fullKey = this.buildKey(key, options.prefix);

    try {
      // Try Redis first
      if (this.redisService.isHealthy()) {
        return await this.redisService.ttl(fullKey);
      }

      // Check memory cache
      const memoryItem = this.memoryCache.get(fullKey);
      if (memoryItem) {
        if (memoryItem.expires === 0) return -1; // No expiration
        const remaining = Math.floor((memoryItem.expires - Date.now()) / 1000);
        return remaining > 0 ? remaining : -2; // Expired
      }

      return -2; // Key doesn't exist
    } catch (error) {
      this.logger.error(`Cache TTL error for key ${fullKey}:`, error);
      this.stats.errors++;
      return -1;
    }
  }

  // Utility methods for common caching patterns
  async getOrSet<T = any>(
    key: string,
    factory: () => Promise<T>,
    options: CacheOptions = {},
  ): Promise<T> {
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, options);
    return value;
  }

  async remember<T = any>(
    key: string,
    factory: () => Promise<T>,
    ttl: number = this.defaultTTL,
  ): Promise<T> {
    return this.getOrSet(key, factory, { ttl });
  }

  // Cache invalidation patterns
  async invalidatePattern(pattern: string): Promise<number> {
    let count = 0;

    try {
      // Redis pattern invalidation
      if (this.redisService.isHealthy()) {
        const keys = await this.redisService.keys(pattern);
        for (const key of keys) {
          await this.redisService.del(key);
          count++;
        }
      }

      // Memory cache pattern invalidation
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      for (const key of this.memoryCache.keys()) {
        if (regex.test(key)) {
          this.memoryCache.delete(key);
          count++;
        }
      }

      this.stats.deletes += count;
      return count;
    } catch (error) {
      this.logger.error(
        `Cache pattern invalidation error for pattern ${pattern}:`,
        error,
      );
      this.stats.errors++;
      return 0;
    }
  }

  async flush(): Promise<boolean> {
    try {
      let success = true;

      // Flush Redis
      if (this.redisService.isHealthy()) {
        success = await this.redisService.flushdb();
      }

      // Clear memory cache
      this.memoryCache.clear();

      if (success) {
        this.logger.log('Cache flushed successfully');
      }

      return success;
    } catch (error) {
      this.logger.error('Cache flush error:', error);
      this.stats.errors++;
      return false;
    }
  }

  getStats(): CacheStats {
    return { ...this.stats };
  }

  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
    };
  }

  getHitRate(): number {
    const total = this.stats.hits + this.stats.misses;
    return total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  async getInfo(): Promise<any> {
    const info = {
      stats: this.getStats(),
      hitRate: this.getHitRate(),
      memoryCache: {
        size: this.memoryCache.size,
        keys: Array.from(this.memoryCache.keys()),
      },
      redis: {
        healthy: this.redisService.isHealthy(),
        info: this.redisService.isHealthy()
          ? await this.redisService.info()
          : null,
      },
    };

    return info;
  }
}

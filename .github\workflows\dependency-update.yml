name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Update dependencies
      run: |
        npm update
        npm audit fix --force
        
    - name: Run tests after update
      run: |
        npm run test
        npm run build
        
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: 'chore: automated dependency updates'
        body: |
          ## Automated Dependency Updates
          
          This PR contains automated dependency updates:
          
          - Updated npm packages to latest compatible versions
          - Applied security fixes via `npm audit fix`
          - All tests passing after updates
          
          Please review the changes and merge if everything looks good.
          
          **Note:** This PR was created automatically by GitHub Actions.
        branch: chore/dependency-updates
        delete-branch: true
        
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: |
        echo "## Security Audit Report" > audit-report.md
        echo "Generated on: $(date)" >> audit-report.md
        echo "" >> audit-report.md
        
        if npm audit --audit-level=moderate; then
          echo "✅ No security vulnerabilities found" >> audit-report.md
        else
          echo "⚠️ Security vulnerabilities detected:" >> audit-report.md
          npm audit --audit-level=moderate >> audit-report.md
        fi
        
    - name: Upload audit report
      uses: actions/upload-artifact@v3
      with:
        name: security-audit-report
        path: audit-report.md
        retention-days: 30

#!/bin/bash

# PAIM Production Deployment Script
# This script handles the deployment of PAIM to production environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_IMAGE_NAME="paim-core"
DOCKER_TAG="latest"
BACKUP_DIR="/opt/paim/backups"
LOG_FILE="/var/log/paim/deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
    fi
    
    # Check if .env.production exists
    if [[ ! -f ".env.production" ]]; then
        error ".env.production file not found"
    fi
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Backup timestamp
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/paim_backup_$BACKUP_TIMESTAMP.tar.gz"
    
    # Stop services for consistent backup
    docker-compose -f docker-compose.prod.yml stop paim-app || true
    
    # Create backup of data volumes
    docker run --rm \
        -v paim_postgres_data:/data/postgres \
        -v paim_redis_data:/data/redis \
        -v "$(pwd)/storage":/data/storage \
        -v "$BACKUP_DIR":/backup \
        alpine:latest \
        tar czf "/backup/paim_backup_$BACKUP_TIMESTAMP.tar.gz" /data
    
    success "Backup created: $BACKUP_FILE"
}

# Build Docker image
build_image() {
    log "Building Docker image..."
    
    # Build the production image
    docker build -t "$DOCKER_IMAGE_NAME:$DOCKER_TAG" .
    
    # Tag with timestamp for rollback capability
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    docker tag "$DOCKER_IMAGE_NAME:$DOCKER_TAG" "$DOCKER_IMAGE_NAME:$TIMESTAMP"
    
    success "Docker image built successfully"
}

# Deploy application
deploy() {
    log "Deploying application..."
    
    # Pull latest images for dependencies
    docker-compose -f docker-compose.prod.yml pull postgres redis nginx prometheus grafana
    
    # Start the application
    docker-compose -f docker-compose.prod.yml up -d
    
    success "Application deployed"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Wait for application to start
    sleep 30
    
    # Check if containers are running
    if ! docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
        error "Some containers are not running"
    fi
    
    # Check application health endpoint
    for i in {1..10}; do
        if curl -f http://localhost:3000/health > /dev/null 2>&1; then
            success "Health check passed"
            return 0
        fi
        log "Health check attempt $i/10 failed, retrying in 10 seconds..."
        sleep 10
    done
    
    error "Health check failed after 10 attempts"
}

# Cleanup old images
cleanup() {
    log "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Keep only last 5 tagged images
    docker images "$DOCKER_IMAGE_NAME" --format "table {{.Tag}}" | \
        grep -E '^[0-9]{8}_[0-9]{6}$' | \
        sort -r | \
        tail -n +6 | \
        xargs -r -I {} docker rmi "$DOCKER_IMAGE_NAME:{}"
    
    success "Cleanup completed"
}

# Rollback function
rollback() {
    log "Rolling back to previous version..."
    
    # Get the previous image tag
    PREVIOUS_TAG=$(docker images "$DOCKER_IMAGE_NAME" --format "table {{.Tag}}" | \
        grep -E '^[0-9]{8}_[0-9]{6}$' | \
        sort -r | \
        head -n 2 | \
        tail -n 1)
    
    if [[ -z "$PREVIOUS_TAG" ]]; then
        error "No previous version found for rollback"
    fi
    
    # Tag previous version as latest
    docker tag "$DOCKER_IMAGE_NAME:$PREVIOUS_TAG" "$DOCKER_IMAGE_NAME:latest"
    
    # Restart services
    docker-compose -f docker-compose.prod.yml up -d paim-app
    
    success "Rollback completed to version: $PREVIOUS_TAG"
}

# Main deployment process
main() {
    log "Starting PAIM production deployment..."
    
    check_root
    check_prerequisites
    
    # Handle command line arguments
    case "${1:-deploy}" in
        "deploy")
            create_backup
            build_image
            deploy
            health_check
            cleanup
            success "Deployment completed successfully!"
            ;;
        "rollback")
            rollback
            health_check
            success "Rollback completed successfully!"
            ;;
        "backup")
            create_backup
            ;;
        "health")
            health_check
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|backup|health}"
            echo "  deploy  - Full deployment process (default)"
            echo "  rollback - Rollback to previous version"
            echo "  backup  - Create backup only"
            echo "  health  - Health check only"
            exit 1
            ;;
    esac
}

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

# Run main function
main "$@"

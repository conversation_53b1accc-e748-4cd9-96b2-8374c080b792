import { Module } from '@nestjs/common';
import { CoreModule } from '../core/core.module';
import { SkillsModule } from '../skills/skills.module';
import { RegistrationModule } from '../registration/registration.module';
import { AppwriteModule } from '../appwrite/appwrite.module';

// Controllers
import { WhatsAppController } from './controllers/whatsapp.controller';

// Services
import { TwilioService } from './services/twilio.service';
import { MessageParsingService } from './services/message-parsing.service';
import { MessageSendingService } from './services/message-sending.service';
import { SessionManagementService } from './services/session-management.service';
import { MessageProcessingService } from './services/message-processing.service';
import { SessionAppwriteService } from './services/session-appwrite.service';
import { UserAppwriteService } from './services/user-appwrite.service';
import { MemoryAppwriteService } from '../skills/services/memory-appwrite.service';

// Guards
import { WebhookValidationGuard } from './guards/webhook-validation.guard';

@Module({
  imports: [CoreModule, SkillsModule, RegistrationModule, AppwriteModule],
  controllers: [WhatsAppController],
  providers: [
    // Core services
    TwilioService,
    MessageParsingService,
    MessageSendingService,
    SessionManagementService,
    MessageProcessingService,

    // Appwrite services
    SessionAppwriteService,
    UserAppwriteService,
    MemoryAppwriteService,

    // Guards
    WebhookValidationGuard,
  ],
  exports: [
    TwilioService,
    MessageParsingService,
    MessageSendingService,
    SessionManagementService,
    MessageProcessingService,
  ],
})
export class WebhookModule {}

#!/usr/bin/env node

/**
 * Ultra-simple HTTP server for PAIM testing
 * Uses only Node.js built-in modules
 */

const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = process.env.PORT || 3000;

// Simple request body parser
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        if (req.headers['content-type']?.includes('application/json')) {
          resolve(JSON.parse(body));
        } else if (req.headers['content-type']?.includes('application/x-www-form-urlencoded')) {
          resolve(querystring.parse(body));
        } else {
          resolve(body);
        }
      } catch (error) {
        resolve(body);
      }
    });
    req.on('error', reject);
  });
}

// Response helper
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data, null, 2));
}

// Create server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;
  
  console.log(`${new Date().toISOString()} - ${method} ${path}`);
  
  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }
  
  try {
    // Route handling
    if (path === '/' && method === 'GET') {
      sendJSON(res, {
        message: 'Welcome to PAIM - Personal AI Manager',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/health',
          api_status: '/api/v1/status',
          documentation: '/api/docs'
        }
      });
      
    } else if (path === '/health' && method === 'GET') {
      sendJSON(res, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        pid: process.pid
      });
      
    } else if (path === '/health/detailed' && method === 'GET') {
      sendJSON(res, {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        pid: process.pid,
        services: {
          database: 'connected',
          cache: 'connected',
          external_apis: {
            openai: 'available',
            twilio: 'available',
            appwrite: 'available'
          }
        },
        system: {
          platform: process.platform,
          arch: process.arch,
          node_version: process.version,
          cpu_usage: process.cpuUsage(),
          load_average: require('os').loadavg()
        }
      });
      
    } else if (path === '/api/v1/status' && method === 'GET') {
      sendJSON(res, {
        message: 'PAIM API is running',
        version: '1.0.0',
        timestamp: new Date().toISOString()
      });
      
    } else if (path === '/api/v1/webhook/whatsapp' && method === 'POST') {
      const body = await parseBody(req);
      console.log('WhatsApp webhook received:', body);
      
      sendJSON(res, {
        status: 'received',
        timestamp: new Date().toISOString(),
        message: 'Webhook processed successfully',
        data: {
          from: body.From || 'unknown',
          body: body.Body || 'empty',
          messageId: body.MessageSid || 'test-' + Date.now()
        }
      });
      
    } else if (path === '/api/v1/registration/request' && method === 'POST') {
      const body = await parseBody(req);
      console.log('Registration request:', body);
      
      sendJSON(res, {
        status: 'success',
        message: 'Registration request received',
        timestamp: new Date().toISOString(),
        data: {
          phoneNumber: body.phoneNumber,
          email: body.email,
          name: body.name,
          registrationId: 'reg-' + Date.now()
        }
      });
      
    } else if (path === '/api/docs' && method === 'GET') {
      sendJSON(res, {
        title: 'PAIM API Documentation',
        version: '1.0.0',
        description: 'Personal AI Manager API endpoints',
        endpoints: [
          {
            method: 'GET',
            path: '/health',
            description: 'Basic health check'
          },
          {
            method: 'GET',
            path: '/health/detailed',
            description: 'Detailed health check with system information'
          },
          {
            method: 'GET',
            path: '/api/v1/status',
            description: 'API status information'
          },
          {
            method: 'POST',
            path: '/api/v1/webhook/whatsapp',
            description: 'WhatsApp webhook endpoint'
          },
          {
            method: 'POST',
            path: '/api/v1/registration/request',
            description: 'User registration request'
          },
          {
            method: 'GET',
            path: '/api/docs',
            description: 'API documentation'
          }
        ]
      });
      
    } else {
      // 404 handler
      sendJSON(res, {
        error: 'Not Found',
        message: `Endpoint ${method} ${path} not found`,
        timestamp: new Date().toISOString()
      }, 404);
    }
    
  } catch (error) {
    console.error('Error:', error);
    sendJSON(res, {
      error: 'Internal Server Error',
      message: error.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 PAIM Simple Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API docs: http://localhost:${PORT}/api/docs`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  console.log('');
  console.log('🧪 Test endpoints:');
  console.log(`   curl http://localhost:${PORT}/health`);
  console.log(`   curl http://localhost:${PORT}/api/v1/status`);
  console.log(`   curl -X POST http://localhost:${PORT}/api/v1/webhook/whatsapp -H "Content-Type: application/json" -d '{"From":"whatsapp:+**********","Body":"Hello PAIM"}'`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    process.exit(0);
  });
});

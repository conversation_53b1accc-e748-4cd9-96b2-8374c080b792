#!/usr/bin/env node

/**
 * Comprehensive Code Quality Testing Script
 * Runs all quality checks and generates reports
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  coverageThreshold: 70,
  maxComplexity: 10,
  maxFileSize: 500, // lines
  outputDir: 'quality-reports',
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function createOutputDir() {
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
  }
}

function runCommand(command, description) {
  log(`\n🔍 ${description}...`, 'blue');
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} completed successfully`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return { success: false, error: error.message, output: error.stdout };
  }
}

function generateReport(results) {
  const timestamp = new Date().toISOString();
  const report = {
    timestamp,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
    },
    results,
  };

  const reportPath = path.join(CONFIG.outputDir, 'quality-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  // Generate HTML report
  generateHtmlReport(report);
  
  return report;
}

function generateHtmlReport(report) {
  const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Code Quality Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        .success { color: #28a745; }
        .failure { color: #dc3545; }
        .warning { color: #ffc107; }
        .result { margin: 10px 0; padding: 15px; border-left: 4px solid #ddd; }
        .result.success { border-color: #28a745; background: #f8fff9; }
        .result.failure { border-color: #dc3545; background: #fff8f8; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Code Quality Report</h1>
        <p>Generated: ${report.timestamp}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div>${report.summary.total}</div>
        </div>
        <div class="metric">
            <h3 class="success">Passed</h3>
            <div>${report.summary.passed}</div>
        </div>
        <div class="metric">
            <h3 class="failure">Failed</h3>
            <div>${report.summary.failed}</div>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <div>${Math.round((report.summary.passed / report.summary.total) * 100)}%</div>
        </div>
    </div>
    
    <h2>Detailed Results</h2>
    ${report.results.map(result => `
        <div class="result ${result.success ? 'success' : 'failure'}">
            <h3>${result.success ? '✅' : '❌'} ${result.name}</h3>
            <p>${result.description}</p>
            ${result.output ? `<pre>${result.output}</pre>` : ''}
            ${result.error ? `<pre class="failure">${result.error}</pre>` : ''}
        </div>
    `).join('')}
</body>
</html>`;

  const htmlPath = path.join(CONFIG.outputDir, 'quality-report.html');
  fs.writeFileSync(htmlPath, html);
  log(`📊 HTML report generated: ${htmlPath}`, 'cyan');
}

async function main() {
  log('🚀 Starting comprehensive code quality checks...', 'magenta');
  
  createOutputDir();
  
  const checks = [
    {
      name: 'TypeScript Compilation',
      description: 'Checking TypeScript compilation',
      command: 'npm run build',
    },
    {
      name: 'ESLint',
      description: 'Running ESLint code analysis',
      command: 'npm run lint',
    },
    {
      name: 'Unit Tests',
      description: 'Running unit tests',
      command: 'npm run test',
    },
    {
      name: 'Test Coverage',
      description: 'Generating test coverage report',
      command: 'npm run test:cov',
    },
    {
      name: 'E2E Tests',
      description: 'Running end-to-end tests',
      command: 'npm run test:e2e',
    },
    {
      name: 'Security Audit',
      description: 'Running npm security audit',
      command: 'npm audit --audit-level=moderate',
    },
    {
      name: 'Dependency Check',
      description: 'Checking for outdated dependencies',
      command: 'npm outdated',
    },
  ];

  const results = [];
  
  for (const check of checks) {
    const result = runCommand(check.command, check.description);
    results.push({
      ...check,
      ...result,
      timestamp: new Date().toISOString(),
    });
  }

  // Generate comprehensive report
  const report = generateReport(results);
  
  // Summary
  log('\n📋 Quality Check Summary:', 'magenta');
  log(`Total checks: ${report.summary.total}`, 'blue');
  log(`Passed: ${report.summary.passed}`, 'green');
  log(`Failed: ${report.summary.failed}`, 'red');
  
  const successRate = Math.round((report.summary.passed / report.summary.total) * 100);
  log(`Success rate: ${successRate}%`, successRate >= 80 ? 'green' : 'yellow');
  
  // Check coverage threshold
  if (fs.existsSync('coverage/coverage-summary.json')) {
    const coverage = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
    const totalCoverage = coverage.total.lines.pct;
    
    log(`\n📊 Test Coverage: ${totalCoverage}%`, totalCoverage >= CONFIG.coverageThreshold ? 'green' : 'red');
    
    if (totalCoverage < CONFIG.coverageThreshold) {
      log(`⚠️  Coverage below threshold (${CONFIG.coverageThreshold}%)`, 'yellow');
    }
  }
  
  log(`\n📁 Reports saved to: ${CONFIG.outputDir}/`, 'cyan');
  
  // Exit with appropriate code
  const hasFailures = report.summary.failed > 0;
  if (hasFailures) {
    log('\n❌ Some quality checks failed. Please review the report.', 'red');
    process.exit(1);
  } else {
    log('\n✅ All quality checks passed!', 'green');
    process.exit(0);
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`\n💥 Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    log(`\n💥 Script failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

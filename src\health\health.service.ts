import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TwilioService } from '../webhook/services/twilio.service';
import { LoggerService } from '../core/services/logger.service';

export interface HealthCheckResult {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  dependencies?: {
    [key: string]: {
      status: 'ok' | 'error';
      responseTime?: number;
      error?: string;
    };
  };
}

@Injectable()
export class HealthService {
  constructor(
    private configService: ConfigService,
    private twilioService: TwilioService,
    private logger: LoggerService,
  ) {}

  async check(): Promise<HealthCheckResult> {
    const result: HealthCheckResult = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '0.1.0',
      environment: this.configService.get<string>('NODE_ENV', 'development'),
    };

    return result;
  }

  async detailedCheck(): Promise<HealthCheckResult> {
    const result = await this.check();

    result.dependencies = {};

    // Check Twilio connection
    try {
      const startTime = Date.now();
      const twilioHealthy = await this.twilioService.testConnection();
      const responseTime = Date.now() - startTime;

      result.dependencies.twilio = {
        status: twilioHealthy ? 'ok' : 'error',
        responseTime,
      };
    } catch (error) {
      result.dependencies.twilio = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      result.status = 'error';
    }

    // Check OpenAI (basic configuration check)
    try {
      const openaiKey = this.configService.get<string>('OPENAI_API_KEY');
      result.dependencies.openai = {
        status: openaiKey && openaiKey.startsWith('sk-') ? 'ok' : 'error',
        error: !openaiKey ? 'API key not configured' : undefined,
      };
    } catch (error) {
      result.dependencies.openai = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      result.status = 'error';
    }

    // Check file system access
    try {
      const fs = await import('fs/promises');
      const storagePath = this.configService.get<string>(
        'STORAGE_PATH',
        './storage',
      );
      await fs.access(storagePath);
      result.dependencies.filesystem = {
        status: 'ok',
      };
    } catch (error) {
      result.dependencies.filesystem = {
        status: 'error',
        error:
          error instanceof Error
            ? error.message
            : 'Storage path not accessible',
      };
      result.status = 'error';
    }

    // Check memory usage
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    };

    result.dependencies.memory = {
      status: memoryUsageMB.heapUsed < 512 ? 'ok' : 'error', // Alert if using more than 512MB
      responseTime: memoryUsageMB.heapUsed,
      error:
        memoryUsageMB.heapUsed >= 512
          ? 'High memory usage detected'
          : undefined,
    };

    this.logger.log(
      `Health check completed: ${result.status}`,
      'HealthService',
    );

    return result;
  }
}

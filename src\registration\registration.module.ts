import { Module } from '@nestjs/common';
import { RegistrationController } from './controllers/registration.controller';
import { AdminController } from './controllers/admin.controller';
import { RegistrationService } from './services/registration.service';
import { EmailService } from './services/email.service';
import { WhitelistService } from './services/whitelist.service';
import { CoreModule } from '../core/core.module';
import { AppwriteModule } from '../appwrite/appwrite.module';

@Module({
  imports: [CoreModule, AppwriteModule],
  controllers: [RegistrationController, AdminController],
  providers: [RegistrationService, EmailService, WhitelistService],
  exports: [RegistrationService, WhitelistService],
})
export class RegistrationModule {}

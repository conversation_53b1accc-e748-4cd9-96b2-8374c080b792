# 🌊 Digital Ocean App Platform Deployment Guide

Complete guide for deploying Sanad to Digital Ocean App Platform with optimal configuration.

## 🎯 Why Digital Ocean App Platform?

### ✅ **Perfect for Sanad's Architecture**
- **No Cold Starts**: Persistent connections for WhatsApp webhooks
- **Long-Running Processes**: Ideal for AI processing and real-time conversations
- **Predictable Pricing**: Fixed costs vs. variable serverless pricing
- **Native DO Spaces Integration**: Seamless file storage with minimal latency
- **Built-in Monitoring**: Comprehensive logging and metrics
- **Auto-Scaling**: Handles traffic spikes automatically

### 📊 **Cost Comparison**
- **DO App Platform**: $5-12/month for basic tier (predictable)
- **Vercel**: $20+/month with usage spikes (variable)
- **Heroku**: $7-25/month (limited features)

## 🚀 Quick Start Deployment

### Prerequisites

1. **Digital Ocean Account**
   - Sign up at [digitalocean.com](https://digitalocean.com)
   - Create a personal access token

2. **doctl CLI Installation**
   ```bash
   # Windows (Chocolatey)
   choco install doctl
   
   # macOS (Homebrew)
   brew install doctl
   
   # Linux
   curl -sL https://github.com/digitalocean/doctl/releases/download/v1.94.0/doctl-1.94.0-linux-amd64.tar.gz | tar -xzv
   ```

3. **GitHub Repository Access**
   - Ensure your repository is accessible to Digital Ocean
   - Repository: `HDickenson/sanad`

### Step-by-Step Deployment

#### 1. Authenticate with Digital Ocean
```bash
doctl auth init
# Enter your personal access token when prompted
```

#### 2. Verify Authentication
```bash
doctl account get
```

#### 3. Deploy the Application
```bash
# Option A: Use our deployment script
npm run deploy:do

# Option B: Manual deployment
npm run build
doctl apps create --spec .do/app.yaml
```

#### 4. Monitor Deployment
```bash
# List your apps
doctl apps list

# Get specific app details
doctl apps get <app-id>

# View deployment logs
doctl apps logs <app-id>
```

## ⚙️ Configuration Details

### App Platform Configuration (`.do/app.yaml`)

```yaml
name: sanad-paim
region: blr1

services:
  - name: api
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
      deploy_on_push: true
    
    build_command: npm run build
    run_command: npm run start:prod
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    
    health_check:
      http_path: /health
      initial_delay_seconds: 30
      period_seconds: 10
      timeout_seconds: 5
      success_threshold: 1
      failure_threshold: 3
    
    http_port: 3000
```

### Environment Variables Setup

#### Required Variables
Copy these from `.env.digitalocean` to your DO App Platform dashboard:

**Core Application:**
- `NODE_ENV=production`
- `PORT=3000`

**OpenAI Integration:**
- `OPENAI_API_KEY=your-openai-key`
- `OPENAI_MODEL=gpt-4`

**Twilio WhatsApp:**
- `TWILIO_ACCOUNT_SID=your-sid`
- `TWILIO_AUTH_TOKEN=your-token`
- `TWILIO_WHATSAPP_NUMBER=whatsapp:+your-number`
- `TWILIO_WEBHOOK_URL=https://your-app.ondigitalocean.app/api/v1/webhook/whatsapp`

**Security:**
- `JWT_SECRET=your-64-char-secret`
- `ENCRYPTION_KEY=your-64-char-key`

**Digital Ocean Spaces:**
- `STORAGE_PROVIDER=digitalocean`
- `DO_SPACES_ACCESS_KEY_ID=your-key`
- `DO_SPACES_SECRET_ACCESS_KEY=your-secret`
- `DO_SPACES_ENDPOINT=https://nyc3.digitaloceanspaces.com`
- `DO_SPACES_REGION=nyc3`
- `DO_SPACES_BUCKET=your-bucket-name`

## 🔧 Post-Deployment Configuration

### 1. Update Twilio Webhook
```bash
# Your new webhook URL will be:
https://sanad-paim-xxxxx.ondigitalocean.app/api/v1/webhook/whatsapp
```

### 2. Configure Digital Ocean Spaces
1. Create a Spaces bucket in the same region as your app
2. Generate Spaces access keys
3. Update environment variables in DO dashboard

### 3. Set Up Custom Domain (Optional)
```bash
# Add custom domain
doctl apps update <app-id> --spec .do/app.yaml
```

### 4. Enable HTTPS
- Digital Ocean automatically provides SSL certificates
- No additional configuration needed

## 📊 Monitoring & Maintenance

### Application Monitoring
```bash
# View real-time logs
doctl apps logs <app-id> --follow

# Check app health
curl https://your-app.ondigitalocean.app/health

# View app metrics in DO dashboard
```

### Scaling Configuration
```yaml
# In .do/app.yaml
instance_count: 2  # Scale to 2 instances
instance_size_slug: basic-xs  # Upgrade instance size
```

### Backup Strategy
- **Code**: Automatically backed up via GitHub
- **Data**: Stored in Digital Ocean Spaces (built-in redundancy)
- **Environment**: Configuration stored in DO dashboard

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check build logs
   doctl apps logs <app-id> --type build
   
   # Verify package.json scripts
   npm run build  # Test locally
   ```

2. **Environment Variable Issues**
   ```bash
   # List current environment variables
   doctl apps get <app-id> --format yaml
   ```

3. **Health Check Failures**
   ```bash
   # Test health endpoint
   curl https://your-app.ondigitalocean.app/health
   
   # Check health check configuration in .do/app.yaml
   ```

### Performance Optimization

1. **Instance Sizing**
   - Start with `basic-xxs` ($5/month)
   - Monitor CPU/memory usage
   - Scale up if needed

2. **Regional Optimization**
   - Deploy in region closest to users
   - Use same region for Spaces storage

3. **Caching Strategy**
   - Enable Redis for session caching
   - Use DO Spaces CDN for static assets

## 💰 Cost Optimization

### Pricing Tiers
- **Basic XXS**: $5/month (512MB RAM, 1 vCPU)
- **Basic XS**: $12/month (1GB RAM, 1 vCPU)
- **Basic S**: $25/month (2GB RAM, 1 vCPU)

### Cost-Saving Tips
1. Start with smallest instance size
2. Use DO Spaces for file storage (cheaper than alternatives)
3. Monitor usage and scale appropriately
4. Use built-in monitoring (no additional cost)

## 🔄 CI/CD Integration

### Automatic Deployments
- Enabled via `deploy_on_push: true` in app.yaml
- Deploys automatically on push to master branch
- No additional CI/CD setup required

### Manual Deployments
```bash
# Force redeploy
doctl apps create-deployment <app-id>

# Update app configuration
doctl apps update <app-id> --spec .do/app.yaml
```

## 📞 Support & Resources

- **Digital Ocean Documentation**: [docs.digitalocean.com/products/app-platform](https://docs.digitalocean.com/products/app-platform/)
- **doctl CLI Reference**: [docs.digitalocean.com/reference/doctl](https://docs.digitalocean.com/reference/doctl/)
- **Community Support**: [digitalocean.com/community](https://digitalocean.com/community)

---

**Next Steps**: After successful deployment, configure your environment variables and test the WhatsApp integration!

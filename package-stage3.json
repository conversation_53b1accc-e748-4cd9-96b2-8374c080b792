{"name": "sanad-paim-stage3", "version": "0.1.0", "description": "Sanad PAIM - Stage 3 Business Logic Dependencies", "main": "dist/main.js", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "scripts": {"build": "nest build", "start": "node dist/main", "start:dev": "nest start --watch", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cors": "^2.8.5", "express": "^4.18.2", "openai": "^4.20.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "twilio": "^4.19.0", "typescript": "^5.1.3"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.3.1"}, "keywords": ["sanad", "paim", "<PERSON><PERSON><PERSON>", "openai", "twi<PERSON>", "stage3"], "author": "Sanad Team", "license": "MIT"}
# 📡 PAIM API Documentation

This document describes the PAIM API endpoints and webhook interfaces.

## 🔗 Base URL

- **Development**: `http://localhost:3000/api/v1`
- **Production**: `https://your-domain.com/api/v1`

## 🔐 Authentication

PAIM uses webhook signature validation for security. All webhook requests must include a valid Twilio signature in the `X-Twilio-Signature` header.

## 📨 Webhook Endpoints

### WhatsApp Message Webhook

Receives incoming WhatsApp messages from <PERSON><PERSON>lio.

**Endpoint**: `POST /webhook/whatsapp`

**Headers**:
- `Content-Type: application/x-www-form-urlencoded`
- `X-Twilio-Signature: <signature>`

**Request Body** (form-encoded):
```
From=whatsapp%3A%2B**********
To=whatsapp%3A%2B***********
Body=Hello%20PAIM
MessageSid=SMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AccountSid=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
NumMedia=0
```

**Response**:
```json
{
  "status": "received",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Status Codes**:
- `200`: Message received and queued for processing
- `400`: Invalid message format
- `401`: Invalid webhook signature
- `500`: Internal server error

### WhatsApp Status Webhook

Receives message delivery status updates from Twilio.

**Endpoint**: `POST /webhook/whatsapp/status`

**Headers**:
- `Content-Type: application/x-www-form-urlencoded`
- `X-Twilio-Signature: <signature>`

**Request Body** (form-encoded):
```
MessageSid=SMxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
MessageStatus=delivered
AccountSid=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

**Response**:
```json
{
  "status": "processed",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🔄 Message Processing Flow

1. **Webhook Receives Message**
   - Validates Twilio signature
   - Parses message content
   - Adds to processing queue

2. **Message Processing**
   - Extracts user information
   - Creates or retrieves session
   - Routes to appropriate skill
   - Generates response

3. **Response Delivery**
   - Sends response via Twilio
   - Updates conversation history
   - Logs performance metrics

## 📋 Message Types

### Text Messages

Standard text messages from users.

**Example**:
```
From: whatsapp:+**********
Body: remember that I have a meeting tomorrow
```

### Voice Messages

Audio messages that will be processed with speech-to-text.

**Example**:
```
From: whatsapp:+**********
NumMedia: 1
MediaUrl0: https://api.twilio.com/2010-04-01/Accounts/.../Messages/.../Media/...
MediaContentType0: audio/ogg
```

### Image Messages

Images with optional captions.

**Example**:
```
From: whatsapp:+**********
Body: This is my new car
NumMedia: 1
MediaUrl0: https://api.twilio.com/2010-04-01/Accounts/.../Messages/.../Media/...
MediaContentType0: image/jpeg
```

## 🎯 Skill Routing

Messages are routed to skills based on trigger words and content analysis:

### Onboarding Skill
**Triggers**: `hello`, `start`, `setup`, `onboard`, `hi`, `hey`
**Purpose**: User setup and configuration

### Memory Capture Skill
**Triggers**: `remember`, `note`, `idea`, `save`, `capture`, `memory`
**Purpose**: Save user thoughts and information

### Reminder Skill
**Triggers**: `remind`, `remind me`, `reminder`, `alert`, `notify`
**Purpose**: Set time-based reminders

### Roadmap Skill
**Triggers**: `roadmap`, `features`, `what's next`, `upcoming`, `plans`, `future`
**Purpose**: Show development progress

## 🔧 Error Handling

### Error Response Format

```json
{
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/webhook/whatsapp",
  "method": "POST",
  "message": "Invalid message format",
  "code": "VALIDATION_ERROR"
}
```

### Error Codes

- `VALIDATION_ERROR`: Invalid input data
- `USER_NOT_FOUND`: User profile not found
- `SKILL_EXECUTION_ERROR`: Skill processing failed
- `EXTERNAL_SERVICE_ERROR`: Third-party service error
- `CONFIGURATION_ERROR`: System configuration issue
- `RATE_LIMIT_ERROR`: Too many requests

## 📊 Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/endpoint",
  "method": "POST"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error context
    }
  },
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/endpoint",
  "method": "POST"
}
```

## 🔒 Security Features

### Webhook Validation

All webhook requests are validated using Twilio's signature verification:

1. **Signature Header**: `X-Twilio-Signature`
2. **URL Validation**: Full request URL including query parameters
3. **Body Validation**: Complete request body
4. **Token Validation**: Twilio auth token

### Rate Limiting

- **Default Limit**: 10 requests per minute per client
- **Identification**: IP address + User-Agent
- **Response**: HTTP 429 with retry-after header

### Input Sanitization

All user inputs are sanitized to prevent:
- Script injection
- SQL injection
- Cross-site scripting (XSS)
- Command injection

## 📈 Monitoring & Logging

### Request Logging

All requests are logged with:
- Request method and URL
- Response status code
- Processing time
- User identification
- Error details (if any)

### Performance Metrics

- **Skill Execution Time**: Time taken to process each skill
- **Message Processing Time**: End-to-end message processing
- **Queue Length**: Number of messages waiting for processing
- **Success Rate**: Percentage of successful message processing

### Health Checks

Monitor system health through:
- **Twilio Connection**: Test API connectivity
- **Queue Status**: Monitor message queue length
- **Memory Usage**: Track application memory consumption
- **Error Rates**: Monitor error frequency and types

## 🧪 Testing Webhooks

### Using ngrok for Local Development

1. **Install ngrok**: `npm install -g ngrok`
2. **Start local server**: `npm run start:dev`
3. **Expose webhook**: `ngrok http 3000`
4. **Configure Twilio**: Use ngrok URL + `/api/v1/webhook/whatsapp`

### Testing with curl

```bash
# Test webhook endpoint
curl -X POST http://localhost:3000/api/v1/webhook/whatsapp \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "X-Twilio-Signature: <signature>" \
  -d "From=whatsapp%3A%2B**********&To=whatsapp%3A%2B***********&Body=test&MessageSid=test&AccountSid=test&NumMedia=0"
```

## 📚 Integration Examples

### Node.js Client

```javascript
const axios = require('axios');

async function sendTestMessage() {
  const response = await axios.post('http://localhost:3000/api/v1/webhook/whatsapp', {
    From: 'whatsapp:+**********',
    To: 'whatsapp:+***********',
    Body: 'remember that I love coffee',
    MessageSid: 'test-message-id',
    AccountSid: 'test-account-id',
    NumMedia: '0'
  }, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'X-Twilio-Signature': 'valid-signature'
    }
  });
  
  console.log(response.data);
}
```

### Python Client

```python
import requests

def send_test_message():
    response = requests.post('http://localhost:3000/api/v1/webhook/whatsapp', 
        data={
            'From': 'whatsapp:+**********',
            'To': 'whatsapp:+***********',
            'Body': 'remember that I love coffee',
            'MessageSid': 'test-message-id',
            'AccountSid': 'test-account-id',
            'NumMedia': '0'
        },
        headers={
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Twilio-Signature': 'valid-signature'
        }
    )
    
    print(response.json())
```

## 🔄 Future API Endpoints

The following endpoints are planned for future releases:

- `GET /api/v1/users/{userId}/memories` - Retrieve user memories
- `POST /api/v1/users/{userId}/memories` - Create new memory
- `GET /api/v1/users/{userId}/reminders` - Get user reminders
- `POST /api/v1/users/{userId}/reminders` - Create new reminder
- `GET /api/v1/skills` - List available skills
- `GET /api/v1/health` - System health check
- `GET /api/v1/metrics` - Performance metrics

For the latest API documentation, visit the Swagger UI at `/api/docs` when running in development mode.

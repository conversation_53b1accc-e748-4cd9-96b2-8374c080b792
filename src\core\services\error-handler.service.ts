import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger.service';

export class PaimError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public context?: Record<string, any>,
  ) {
    super(message);
    this.name = 'PaimError';
  }
}

export class SkillExecutionError extends PaimError {
  constructor(
    skillId: string,
    originalError: Error,
    context?: Record<string, any>,
  ) {
    super(
      `Skill execution failed: ${originalError.message}`,
      'SKILL_EXECUTION_ERROR',
      500,
      { skillId, originalError: originalError.message, ...context },
    );
  }
}

export class UserNotFoundError extends PaimError {
  constructor(userId: string) {
    super(`User not found: ${userId}`, 'USER_NOT_FOUND', 404, { userId });
  }
}

export class ValidationError extends PaimError {
  constructor(field: string, value: any, reason: string) {
    super(
      `Validation failed for ${field}: ${reason}`,
      'VALIDATION_ERROR',
      400,
      {
        field,
        value,
        reason,
      },
    );
  }
}

export class ConfigurationError extends PaimError {
  constructor(configKey: string, reason: string) {
    super(
      `Configuration error for ${configKey}: ${reason}`,
      'CONFIGURATION_ERROR',
      500,
      {
        configKey,
        reason,
      },
    );
  }
}

export class ExternalServiceError extends PaimError {
  constructor(service: string, operation: string, originalError: Error) {
    super(
      `External service error (${service}): ${originalError.message}`,
      'EXTERNAL_SERVICE_ERROR',
      502,
      { service, operation, originalError: originalError.message },
    );
  }
}

@Injectable()
export class ErrorHandlerService {
  constructor(
    private logger: LoggerService,
    private configService: ConfigService,
  ) {}

  handleError(error: Error, context?: string, userId?: string): PaimError {
    // If it's already a PaimError, just log and return
    if (error instanceof PaimError) {
      this.logError(error, context, userId);
      return error;
    }

    // Convert generic errors to PaimError
    const paimError = new PaimError(
      error.message || 'An unexpected error occurred',
      'INTERNAL_ERROR',
      500,
      { originalError: error.name, context, userId },
    );

    this.logError(paimError, context, userId);
    return paimError;
  }

  private logError(error: PaimError, context?: string, userId?: string) {
    this.logger.error(error.message, error.stack, context || 'ErrorHandler');

    // Log additional context
    if (error.context || userId) {
      this.logger.error('Error context', undefined, 'ErrorHandler');
      this.logger.error(
        JSON.stringify({ ...error.context, userId }),
        undefined,
        'ErrorHandler',
      );
    }
  }

  createUserFriendlyMessage(error: PaimError): string {
    const detailedErrors = this.configService.get<boolean>(
      'DETAILED_ERRORS',
      false,
    );

    if (detailedErrors) {
      return `${error.message} (Code: ${error.code})`;
    }

    // Return generic messages for production
    switch (error.code) {
      case 'USER_NOT_FOUND':
        return "I couldn't find your user profile. Please try the onboarding process again.";
      case 'SKILL_EXECUTION_ERROR':
        return 'I encountered an issue while processing your request. Please try again.';
      case 'VALIDATION_ERROR':
        return 'There was an issue with the information provided. Please check and try again.';
      case 'EXTERNAL_SERVICE_ERROR':
        return "I'm having trouble connecting to external services. Please try again later.";
      case 'CONFIGURATION_ERROR':
        return "There's a configuration issue. Please contact support.";
      default:
        return 'I encountered an unexpected issue. Please try again or contact support.';
    }
  }

  isRetryableError(error: PaimError): boolean {
    const retryableCodes = [
      'EXTERNAL_SERVICE_ERROR',
      'TIMEOUT_ERROR',
      'RATE_LIMIT_ERROR',
      'NETWORK_ERROR',
    ];
    return retryableCodes.includes(error.code);
  }

  shouldNotifyUser(error: PaimError): boolean {
    const silentCodes = ['VALIDATION_ERROR', 'USER_NOT_FOUND'];
    return !silentCodes.includes(error.code);
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import {
  ISkill,
  ISkill<PERSON>ontext,
  IUser,
  IUserSession,
  IMessage,
  IUserConfig,
  IMemory,
  IReminder,
  MessageType,
  MessageDirection,
} from '../../interfaces';
import { LoggerService } from '../../core';

export class MockSkillContext implements ISkillContext {
  public memories: Partial<IMemory>[] = [];
  public reminders: Partial<IReminder>[] = [];
  public state: Record<string, any> = {};
  public userConfig: IUserConfig;

  constructor(
    public user: IUser,
    public session: IUserSession,
    public message: IMessage,
    userConfig?: Partial<IUserConfig>,
  ) {
    this.userConfig = {
      name: user.name || 'Test User',
      tone: user.tone,
      language: user.preferences?.language || 'en',
      timezone: user.preferences?.timezone || 'UTC',
      preferences: user.preferences,
      ...userConfig,
    };
  }

  async getUserConfig(): Promise<IUserConfig> {
    return this.userConfig;
  }

  async setState(key: string, value: any): Promise<void> {
    this.state[key] = value;
  }

  async getState(key: string): Promise<any> {
    return this.state[key];
  }

  async saveMemory(memory: Partial<IMemory>): Promise<void> {
    this.memories.push({
      id: `memory-${this.memories.length + 1}`,
      userId: this.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      accessCount: 0,
      lastAccessedAt: new Date(),
      ...memory,
    });
  }

  async saveReminder(reminder: Partial<IReminder>): Promise<void> {
    this.reminders.push({
      id: `reminder-${this.reminders.length + 1}`,
      userId: this.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...reminder,
    });
  }

  reply(message: string, tone?: string): string {
    const actualTone = tone || this.user.tone;

    // Simple tone formatting for testing
    switch (actualTone) {
      case 'friendly':
        return `😊 ${message}`;
      case 'pro':
        return message;
      case 'witty':
        return `😄 ${message}`;
      default:
        return message;
    }
  }

  log(level: string, message: string, meta?: any): void {
    // Mock logging - could be enhanced to capture logs for testing
    console.log(`[${level.toUpperCase()}] ${message}`, meta);
  }

  // Helper methods for testing
  async hasState(key: string): Promise<boolean> {
    return this.state[key] !== undefined;
  }

  async removeState(key: string): Promise<void> {
    delete this.state[key];
  }

  async getStateWithDefault<T>(key: string, defaultValue: T): Promise<T> {
    return this.state[key] !== undefined ? this.state[key] : defaultValue;
  }

  async incrementState(key: string, increment: number = 1): Promise<number> {
    const currentValue = await this.getStateWithDefault(key, 0);
    const newValue =
      (typeof currentValue === 'number' ? currentValue : 0) + increment;
    await this.setState(key, newValue);
    return newValue;
  }

  async appendToStateArray<T>(key: string, item: T): Promise<T[]> {
    const currentArray = await this.getStateWithDefault<T[]>(key, []);
    const newArray = Array.isArray(currentArray)
      ? [...currentArray, item]
      : [item];
    await this.setState(key, newArray);
    return newArray;
  }

  getConversationHistory(): any[] {
    return this.session.conversationHistory || [];
  }

  getLastMessage(): any | undefined {
    const history = this.getConversationHistory();
    return history.length > 0 ? history[history.length - 1] : undefined;
  }

  getMessagesSince(timestamp: Date): any[] {
    return this.getConversationHistory().filter(
      (msg) => msg.timestamp && new Date(msg.timestamp) > timestamp,
    );
  }

  isFirstInteraction(): boolean {
    return this.getConversationHistory().length <= 1;
  }

  getUserPreference(key: string): any {
    return this.user.preferences?.[key];
  }

  getUserLanguage(): string {
    return this.user.preferences?.language || 'en';
  }

  getUserTimezone(): string {
    return this.user.preferences?.timezone || 'UTC';
  }
}

export class SkillTestHelper {
  static createMockUser(overrides?: Partial<IUser>): IUser {
    return {
      id: 'test-user-123',
      phoneNumber: 'whatsapp:+1234567890',
      name: 'Test User',
      tone: 'friendly',
      isOnboarded: true,
      isWhitelisted: true,
      isEarlyAdopter: false,
      registrationSource: 'whatsapp',
      preferences: {
        language: 'en',
        timezone: 'UTC',
        voiceEnabled: true,
        notificationsEnabled: true,
        maxMemories: 1000,
        defaultSkills: [],
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActiveAt: new Date(),
      ...overrides,
    };
  }

  static createMockSession(
    userId: string,
    overrides?: Partial<IUserSession>,
  ): IUserSession {
    return {
      userId,
      sessionId: 'test-session-123',
      context: {},
      state: {},
      conversationHistory: [],
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      ...overrides,
    };
  }

  static createMockMessage(
    userId: string,
    content: string,
    overrides?: Partial<IMessage>,
  ): IMessage {
    return {
      id: 'test-message-123',
      userId,
      sessionId: 'test-session-123',
      content,
      type: MessageType.TEXT,
      direction: MessageDirection.INBOUND,
      metadata: {},
      timestamp: new Date(),
      processed: false,
      ...overrides,
    };
  }

  static createMockContext(
    input: string,
    userOverrides?: Partial<IUser>,
    sessionOverrides?: Partial<IUserSession>,
    messageOverrides?: Partial<IMessage>,
    configOverrides?: Partial<IUserConfig>,
  ): MockSkillContext {
    const user = this.createMockUser(userOverrides);
    const session = this.createMockSession(user.id, sessionOverrides);
    const message = this.createMockMessage(user.id, input, messageOverrides);

    return new MockSkillContext(user, session, message, configOverrides);
  }

  static async createTestingModule(skillClass: any): Promise<TestingModule> {
    const mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
      logSkillExecution: jest.fn(),
      logUserAction: jest.fn(),
      logApiCall: jest.fn(),
      logMemoryOperation: jest.fn(),
    };

    const mockValidation = {
      validateDto: jest.fn(),
      validatePhoneNumber: jest.fn(),
      validateUserId: jest.fn(),
      validateTone: jest.fn(),
      validateSkillId: jest.fn(),
      validateMemoryContent: jest.fn().mockReturnValue(true),
      validateTags: jest.fn(),
      sanitizeInput: jest.fn((input) => input),
      isValidUrl: jest.fn(),
      isValidEmail: jest.fn(),
      isValidLanguageCode: jest.fn(),
      isValidTimezone: jest.fn(),
    };

    return Test.createTestingModule({
      providers: [
        skillClass,
        {
          provide: LoggerService,
          useValue: mockLogger,
        },
        {
          provide: 'ValidationService',
          useValue: mockValidation,
        },
      ],
    }).compile();
  }

  static async testSkillExecution(
    skill: ISkill,
    input: string,
    contextOverrides?: {
      user?: Partial<IUser>;
      session?: Partial<IUserSession>;
      message?: Partial<IMessage>;
      config?: Partial<IUserConfig>;
    },
  ) {
    const context = this.createMockContext(
      input,
      contextOverrides?.user,
      contextOverrides?.session,
      contextOverrides?.message,
      contextOverrides?.config,
    );

    const startTime = Date.now();
    const response = await skill.execute(input, context);
    const executionTime = Date.now() - startTime;

    return {
      response,
      context,
      executionTime,
      memories: context.memories,
      reminders: context.reminders,
      state: context.state,
    };
  }

  static expectValidResponse(response: any) {
    expect(response).toBeDefined();
    expect(response.content).toBeDefined();
    expect(typeof response.content).toBe('string');
    expect(response.content.length).toBeGreaterThan(0);
    expect(response.type).toBeDefined();
    expect(Object.values(MessageType)).toContain(response.type);
  }

  static expectMemorySaved(
    context: MockSkillContext,
    expectedContent?: string,
  ) {
    expect(context.memories.length).toBeGreaterThan(0);
    if (expectedContent) {
      expect(
        context.memories.some((m) => m.content?.includes(expectedContent)),
      ).toBe(true);
    }
  }

  static expectReminderSaved(
    context: MockSkillContext,
    expectedTitle?: string,
  ) {
    expect(context.reminders.length).toBeGreaterThan(0);
    if (expectedTitle) {
      expect(
        context.reminders.some((r) => r.title?.includes(expectedTitle)),
      ).toBe(true);
    }
  }

  static expectStateSet(
    context: MockSkillContext,
    key: string,
    expectedValue?: any,
  ) {
    expect(context.state[key]).toBeDefined();
    if (expectedValue !== undefined) {
      expect(context.state[key]).toEqual(expectedValue);
    }
  }
}

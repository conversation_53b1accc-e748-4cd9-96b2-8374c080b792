#!/bin/bash

# PAIM Monitoring Stack Setup Script
# Sets up Prometheus, Grafana, and supporting monitoring services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MONITORING_DIR="monitoring"
GRAFANA_DIR="$MONITORING_DIR/grafana"
COMPOSE_FILE="docker-compose.monitoring.yml"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi

    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi

    success "Prerequisites check passed"
}

# Create monitoring directories
create_directories() {
    log "Creating monitoring directories..."

    mkdir -p "$GRAFANA_DIR/provisioning/datasources"
    mkdir -p "$GRAFANA_DIR/provisioning/dashboards"
    mkdir -p "$GRAFANA_DIR/dashboards/paim"
    mkdir -p "$GRAFANA_DIR/dashboards/system"
    mkdir -p "$GRAFANA_DIR/dashboards/infrastructure"
    mkdir -p "logs"

    success "Directories created"
}

# Set permissions
set_permissions() {
    log "Setting permissions..."

    # Grafana needs specific permissions
    sudo chown -R 472:472 "$GRAFANA_DIR" 2>/dev/null || true
    chmod -R 755 "$MONITORING_DIR"

    success "Permissions set"
}

# Start monitoring stack
start_monitoring() {
    log "Starting monitoring stack..."

    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Docker Compose file not found: $COMPOSE_FILE"
    fi

    docker-compose -f "$COMPOSE_FILE" up -d

    success "Monitoring stack started"
}

# Wait for services
wait_for_services() {
    log "Waiting for services to be ready..."

    local services=("prometheus:9090" "grafana:3001" "alertmanager:9093")

    for service in "${services[@]}"; do
        local name="${service%:*}"
        local port="${service#*:}"

        log "Waiting for $name on port $port..."

        local attempts=0
        local max_attempts=30

        while [ $attempts -lt $max_attempts ]; do
            if curl -s "http://localhost:$port" > /dev/null 2>&1; then
                success "$name is ready"
                break
            fi

            attempts=$((attempts + 1))
            sleep 2
        done

        if [ $attempts -eq $max_attempts ]; then
            warning "$name may not be ready yet"
        fi
    done
}

# Display access information
display_access_info() {
    log "Monitoring stack is ready!"

    echo ""
    echo "🔗 Access URLs:"
    echo "  📊 Grafana:      http://localhost:3001 (admin/admin123)"
    echo "  📈 Prometheus:   http://localhost:9090"
    echo "  🚨 AlertManager: http://localhost:9093"
    echo "  📋 cAdvisor:     http://localhost:8080"
    echo "  🖥️  Node Exporter: http://localhost:9100"
    echo "  📝 Loki:         http://localhost:3100"
    echo "  🔍 Jaeger:       http://localhost:16686"
    echo ""
    echo "📚 Default Credentials:"
    echo "  Grafana: admin / admin123"
    echo ""
    echo "📁 Configuration files:"
    echo "  Prometheus: $MONITORING_DIR/prometheus.yml"
    echo "  AlertManager: $MONITORING_DIR/alertmanager.yml"
    echo "  Grafana: $GRAFANA_DIR/provisioning/"
    echo ""
}

# Main execution
main() {
    log "🚀 Setting up PAIM monitoring stack..."

    check_prerequisites
    create_directories
    set_permissions
    start_monitoring
    wait_for_services
    display_access_info

    success "Monitoring setup completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "stop")
        log "Stopping monitoring stack..."
        docker-compose -f "$COMPOSE_FILE" down
        success "Monitoring stack stopped"
        ;;
    "restart")
        log "Restarting monitoring stack..."
        docker-compose -f "$COMPOSE_FILE" restart
        success "Monitoring stack restarted"
        ;;
    "logs")
        log "Showing monitoring stack logs..."
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
    "status")
        log "Checking monitoring stack status..."
        docker-compose -f "$COMPOSE_FILE" ps
        ;;
    *)
        main
        ;;
esac
// Import statements for referenced interfaces
import { IUser, IUserSession, IUserConfig } from './user.interface';
import { IMessage, MessageType } from './message.interface';
import { IMemory } from './memory.interface';
import { IReminder } from './reminder.interface';

export interface ISkill {
  id: string;
  name: string;
  description: string;
  triggers: string[];
  category: SkillCategory;
  priority: number;
  enabled: boolean;
  execute(input: string, context: ISkillContext): Promise<ISkillResponse>;
}

export enum SkillCategory {
  CORE = 'core',
  MEMORY = 'memory',
  PRODUCTIVITY = 'productivity',
  COMMUNICATION = 'communication',
  ENTERTAINMENT = 'entertainment',
  UTILITY = 'utility',
  CUSTOM = 'custom',
}

export interface ISkillContext {
  user: IUser;
  session: IUserSession;
  message: IMessage;
  getUserConfig(): Promise<IUserConfig>;
  setState(key: string, value: any): Promise<void>;
  getState(key: string): Promise<any>;
  saveMemory(memory: Partial<IMemory>): Promise<void>;
  saveReminder(reminder: Partial<IReminder>): Promise<void>;
  reply(message: string, tone?: string): string;
  log(level: string, message: string, meta?: any): void;

  // Helper methods
  hasState(key: string): Promise<boolean>;
  removeState(key: string): Promise<void>;
  getStateWithDefault<T>(key: string, defaultValue: T): Promise<T>;
  incrementState(key: string, increment?: number): Promise<number>;
  appendToStateArray<T>(key: string, item: T): Promise<T[]>;

  // Context information getters
  getConversationHistory(): any[];
  getLastMessage(): any | undefined;
  getMessagesSince(timestamp: Date): any[];
  isFirstInteraction(): boolean;
  getUserPreference(key: string): any;
  getUserLanguage(): string;
  getUserTimezone(): string;
}

export interface ISkillResponse {
  content: string;
  type: MessageType;
  tone?: string;
  metadata?: Record<string, any>;
  nextAction?: string;
  shouldEndConversation?: boolean;
}

export interface ISkillRegistry {
  register(skill: ISkill): void;
  unregister(skillId: string): void;
  getSkill(skillId: string): ISkill | undefined;
  getAllSkills(): ISkill[];
  getEnabledSkills(): ISkill[];
  findSkillByTrigger(input: string): ISkill | undefined;
}

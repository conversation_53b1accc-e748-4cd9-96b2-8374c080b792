{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/skills/*": ["src/skills/*"], "@/services/*": ["src/services/*"], "@/interfaces/*": ["src/interfaces/*"], "@/utils/*": ["src/utils/*"], "@/config/*": ["src/config/*"], "@/ai/*": ["src/ai/*"], "@/webhook/*": ["src/webhook/*"], "@/storage/*": ["src/storage/*"]}, "lib": ["ES2021"], "esModuleInterop": true, "resolveJsonModule": true, "strict": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.spec.ts", "**/*.test.ts"]}
import { Injectable } from '@nestjs/common';
import {
  ISkillContext,
  ISkillResponse,
  SkillCategory,
  MessageType,
  MemoryCategory,
  MemoryImportance,
} from '../../interfaces';
import { BaseSkill } from '../base';
import { LoggerService, ValidationService } from '../../core';

@Injectable()
export class MemoryCaptureSkill extends BaseSkill {
  readonly id = 'memory.capture';
  readonly name = 'Memory Capture';
  readonly description =
    'Captures and stores user memories, thoughts, and notes';
  readonly triggers = ['remember', 'note', 'idea', 'save', 'capture', 'memory'];
  readonly category = SkillCategory.MEMORY;
  readonly priority = 8;

  constructor(
    logger: LoggerService,
    private validation: ValidationService,
  ) {
    super(logger);
  }

  async execute(
    input: string,
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    const startTime = Date.now();

    try {
      if (!this.validateInput(input)) {
        return this.createErrorResponse('Please provide something to remember');
      }

      // Extract the actual content to remember
      const content = this.extractMemoryContent(input);

      if (!content || !this.validation.validateMemoryContent(content)) {
        return this.createResponse(
          context.reply(
            'Please provide something meaningful to remember (at least a few characters).',
          ),
          MessageType.TEXT,
        );
      }

      // Analyze the content to determine category and importance
      const analysis = this.analyzeMemoryContent(content);

      // Extract entities and tags
      const entities = this.extractEntities(content);
      const tags = this.generateTags(content, entities);

      // Create memory object
      const memory = {
        content,
        tags,
        category: analysis.category,
        importance: analysis.importance,
        metadata: {
          source: 'whatsapp',
          relatedMemories: [],
          entities: entities,
          sentiment: analysis.sentiment,
          language: context.getUserLanguage(),
        },
        accessCount: 0,
        lastAccessedAt: new Date(),
      };

      // Save the memory
      await context.saveMemory(memory);

      // Generate response based on content type
      const response = this.generateCaptureResponse(analysis, context);

      const duration = Date.now() - startTime;
      this.logExecution(input, response, context.user.id, duration, true);

      return this.createResponse(
        response,
        MessageType.TEXT,
        context.user.tone,
        {
          memoryCategory: analysis.category,
          memoryImportance: analysis.importance,
          tagsGenerated: tags.length,
        },
      );
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logExecution(input, errorMessage, context.user.id, duration, false);
      return this.createErrorResponse(
        'Failed to save your memory. Please try again.',
      );
    }
  }

  private extractMemoryContent(input: string): string {
    // Remove trigger words and clean up the content
    const triggerPattern = /^(remember|note|idea|save|capture|memory)\s+/i;
    let content = input.replace(triggerPattern, '').trim();

    // Handle "remember that..." pattern
    content = content.replace(/^that\s+/i, '');

    // Handle "remember to..." pattern (this might be a reminder, but we'll save as memory)
    if (content.toLowerCase().startsWith('to ')) {
      content = content.substring(3);
    }

    return content.trim();
  }

  private analyzeMemoryContent(content: string): {
    category: MemoryCategory;
    importance: MemoryImportance;
    sentiment: number;
  } {
    const lowerContent = content.toLowerCase();

    // Determine category based on keywords
    let category = MemoryCategory.OTHER;

    if (this.containsWorkKeywords(lowerContent)) {
      category = MemoryCategory.WORK;
    } else if (this.containsPersonalKeywords(lowerContent)) {
      category = MemoryCategory.PERSONAL;
    } else if (this.containsIdeaKeywords(lowerContent)) {
      category = MemoryCategory.IDEA;
    } else if (this.containsTaskKeywords(lowerContent)) {
      category = MemoryCategory.TASK;
    } else if (this.containsContactKeywords(lowerContent)) {
      category = MemoryCategory.CONTACT;
    } else if (this.containsEventKeywords(lowerContent)) {
      category = MemoryCategory.EVENT;
    } else {
      category = MemoryCategory.NOTE;
    }

    // Determine importance based on content analysis
    let importance = MemoryImportance.MEDIUM;

    if (this.containsUrgentKeywords(lowerContent)) {
      importance = MemoryImportance.HIGH;
    } else if (this.containsImportantKeywords(lowerContent)) {
      importance = MemoryImportance.HIGH;
    } else if (this.containsCriticalKeywords(lowerContent)) {
      importance = MemoryImportance.CRITICAL;
    } else if (content.length < 20) {
      importance = MemoryImportance.LOW;
    }

    // Simple sentiment analysis (positive = 1, neutral = 0, negative = -1)
    const sentiment = this.analyzeSentiment(lowerContent);

    return { category, importance, sentiment };
  }

  private containsWorkKeywords(content: string): boolean {
    const workKeywords = [
      'work',
      'office',
      'meeting',
      'project',
      'deadline',
      'client',
      'boss',
      'colleague',
      'presentation',
      'report',
    ];
    return workKeywords.some((keyword) => content.includes(keyword));
  }

  private containsPersonalKeywords(content: string): boolean {
    const personalKeywords = [
      'family',
      'friend',
      'home',
      'personal',
      'birthday',
      'anniversary',
      'vacation',
      'hobby',
    ];
    return personalKeywords.some((keyword) => content.includes(keyword));
  }

  private containsIdeaKeywords(content: string): boolean {
    const ideaKeywords = [
      'idea',
      'concept',
      'thought',
      'brainstorm',
      'innovation',
      'solution',
      'inspiration',
    ];
    return ideaKeywords.some((keyword) => content.includes(keyword));
  }

  private containsTaskKeywords(content: string): boolean {
    const taskKeywords = [
      'todo',
      'task',
      'do',
      'complete',
      'finish',
      'action',
      'need to',
    ];
    return taskKeywords.some((keyword) => content.includes(keyword));
  }

  private containsContactKeywords(content: string): boolean {
    const contactKeywords = [
      'phone',
      'email',
      'address',
      'contact',
      'number',
      '@',
    ];
    return contactKeywords.some((keyword) => content.includes(keyword));
  }

  private containsEventKeywords(content: string): boolean {
    const eventKeywords = [
      'event',
      'appointment',
      'schedule',
      'calendar',
      'date',
      'time',
      'when',
    ];
    return eventKeywords.some((keyword) => content.includes(keyword));
  }

  private containsUrgentKeywords(content: string): boolean {
    const urgentKeywords = [
      'urgent',
      'asap',
      'immediately',
      'emergency',
      'critical',
      'important',
    ];
    return urgentKeywords.some((keyword) => content.includes(keyword));
  }

  private containsImportantKeywords(content: string): boolean {
    const importantKeywords = [
      'important',
      'crucial',
      'vital',
      'key',
      'essential',
      'significant',
    ];
    return importantKeywords.some((keyword) => content.includes(keyword));
  }

  private containsCriticalKeywords(content: string): boolean {
    const criticalKeywords = [
      'critical',
      'emergency',
      'crisis',
      'urgent',
      'life',
      'death',
    ];
    return criticalKeywords.some((keyword) => content.includes(keyword));
  }

  private analyzeSentiment(content: string): number {
    const positiveWords = [
      'good',
      'great',
      'excellent',
      'amazing',
      'wonderful',
      'happy',
      'love',
      'like',
      'awesome',
    ];
    const negativeWords = [
      'bad',
      'terrible',
      'awful',
      'hate',
      'dislike',
      'sad',
      'angry',
      'frustrated',
      'disappointed',
    ];

    const positiveCount = positiveWords.filter((word) =>
      content.includes(word),
    ).length;
    const negativeCount = negativeWords.filter((word) =>
      content.includes(word),
    ).length;

    if (positiveCount > negativeCount) return 1;
    if (negativeCount > positiveCount) return -1;
    return 0;
  }

  private generateTags(
    content: string,
    entities: Record<string, string[]>,
  ): string[] {
    const tags = new Set<string>();

    // Add entity-based tags
    entities.hashtags?.forEach((tag) => tags.add(tag));

    // Add category-based tags
    const lowerContent = content.toLowerCase();

    if (this.containsWorkKeywords(lowerContent)) tags.add('work');
    if (this.containsPersonalKeywords(lowerContent)) tags.add('personal');
    if (this.containsIdeaKeywords(lowerContent)) tags.add('idea');
    if (this.containsTaskKeywords(lowerContent)) tags.add('task');

    // Add date-based tags if content mentions time
    if (lowerContent.includes('today')) tags.add('today');
    if (lowerContent.includes('tomorrow')) tags.add('tomorrow');
    if (lowerContent.includes('week')) tags.add('weekly');
    if (lowerContent.includes('month')) tags.add('monthly');

    return Array.from(tags).slice(0, 10); // Limit to 10 tags
  }

  private generateCaptureResponse(
    analysis: { category: MemoryCategory; importance: MemoryImportance },
    context: ISkillContext,
  ): string {
    const responses = {
      [MemoryCategory.WORK]: [
        'Got it! Saved that work-related memory.',
        'Noted and filed under work memories.',
        'Work memory captured successfully.',
      ],
      [MemoryCategory.PERSONAL]: [
        'Saved that personal memory for you.',
        'Personal note captured and stored.',
        'Got it! That personal memory is now saved.',
      ],
      [MemoryCategory.IDEA]: [
        "Great idea! I've captured it for you.",
        'Brilliant! That idea is now safely stored.',
        "Idea saved! I'll help you remember this inspiration.",
      ],
      [MemoryCategory.TASK]: [
        "Task noted! I'll help you remember to do this.",
        'Got it! That task is now in your memory bank.',
        'Task captured successfully.',
      ],
      default: [
        'Noted and saved to your memory.',
        'Memory captured successfully!',
        "Got it! I'll remember that for you.",
        "Saved! I'll keep that in mind.",
      ],
    };

    const categoryResponses = responses[analysis.category] || responses.default;
    const baseResponse =
      categoryResponses[Math.floor(Math.random() * categoryResponses.length)];

    // Add importance indicator for high/critical memories
    if (analysis.importance >= MemoryImportance.HIGH) {
      return context.reply(`${baseResponse} I've marked this as important.`);
    }

    return context.reply(baseResponse);
  }
}

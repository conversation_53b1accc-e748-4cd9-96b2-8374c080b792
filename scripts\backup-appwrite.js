#!/usr/bin/env node

/**
 * Appwrite Backup Script for PAIM
 * Automated backup of Appwrite databases, collections, and documents
 */

const fs = require('fs');
const path = require('path');
const { Client, Databases, Users, Storage } = require('node-appwrite');

// Configuration
const CONFIG = {
  endpoint: process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
  projectId: process.env.APPWRITE_PROJECT_ID,
  apiKey: process.env.APPWRITE_API_KEY,
  databaseId: process.env.APPWRITE_DATABASE_ID,
  backupDir: process.env.BACKUP_DIR || './backups',
  retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS) || 30,
  compression: process.env.BACKUP_COMPRESSION === 'true',
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Initialize Appwrite client
function initializeClient() {
  if (!CONFIG.projectId || !CONFIG.apiKey) {
    throw new Error('Missing required Appwrite configuration. Please set APPWRITE_PROJECT_ID and APPWRITE_API_KEY environment variables.');
  }

  const client = new Client()
    .setEndpoint(CONFIG.endpoint)
    .setProject(CONFIG.projectId)
    .setKey(CONFIG.apiKey);

  return {
    databases: new Databases(client),
    users: new Users(client),
    storage: new Storage(client),
  };
}

// Create backup directory structure
function createBackupDirectory() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(CONFIG.backupDir, `appwrite-backup-${timestamp}`);
  
  fs.mkdirSync(backupPath, { recursive: true });
  fs.mkdirSync(path.join(backupPath, 'databases'), { recursive: true });
  fs.mkdirSync(path.join(backupPath, 'users'), { recursive: true });
  fs.mkdirSync(path.join(backupPath, 'storage'), { recursive: true });
  
  return backupPath;
}

// Backup database collections and documents
async function backupDatabases(databases, backupPath) {
  log('📊 Backing up databases...', 'blue');
  
  try {
    // Get all databases
    const databasesList = await databases.list();
    const backupData = {
      timestamp: new Date().toISOString(),
      databases: [],
    };

    for (const database of databasesList.databases) {
      log(`  📁 Backing up database: ${database.name} (${database.$id})`, 'cyan');
      
      const databaseBackup = {
        id: database.$id,
        name: database.name,
        collections: [],
      };

      // Get all collections in the database
      const collections = await databases.listCollections(database.$id);
      
      for (const collection of collections.collections) {
        log(`    📋 Backing up collection: ${collection.name} (${collection.$id})`, 'cyan');
        
        const collectionBackup = {
          id: collection.$id,
          name: collection.name,
          attributes: collection.attributes,
          indexes: collection.indexes,
          documents: [],
        };

        // Get all documents in the collection
        let offset = 0;
        const limit = 100;
        let hasMore = true;

        while (hasMore) {
          try {
            const documents = await databases.listDocuments(
              database.$id,
              collection.$id,
              [],
              limit,
              offset
            );

            collectionBackup.documents.push(...documents.documents);
            
            if (documents.documents.length < limit) {
              hasMore = false;
            } else {
              offset += limit;
            }
          } catch (error) {
            log(`    ⚠️ Error backing up documents: ${error.message}`, 'yellow');
            hasMore = false;
          }
        }

        log(`    ✅ Backed up ${collectionBackup.documents.length} documents`, 'green');
        databaseBackup.collections.push(collectionBackup);
      }

      backupData.databases.push(databaseBackup);
    }

    // Save database backup
    const databaseBackupFile = path.join(backupPath, 'databases', 'databases.json');
    fs.writeFileSync(databaseBackupFile, JSON.stringify(backupData, null, 2));
    
    log(`✅ Database backup completed: ${backupData.databases.length} databases`, 'green');
    return backupData;
  } catch (error) {
    log(`❌ Database backup failed: ${error.message}`, 'red');
    throw error;
  }
}

// Backup users
async function backupUsers(users, backupPath) {
  log('👥 Backing up users...', 'blue');
  
  try {
    const usersList = await users.list();
    const usersBackup = {
      timestamp: new Date().toISOString(),
      users: usersList.users,
      total: usersList.total,
    };

    const usersBackupFile = path.join(backupPath, 'users', 'users.json');
    fs.writeFileSync(usersBackupFile, JSON.stringify(usersBackup, null, 2));
    
    log(`✅ Users backup completed: ${usersBackup.total} users`, 'green');
    return usersBackup;
  } catch (error) {
    log(`❌ Users backup failed: ${error.message}`, 'red');
    throw error;
  }
}

// Backup storage buckets and files metadata
async function backupStorage(storage, backupPath) {
  log('📁 Backing up storage metadata...', 'blue');
  
  try {
    const buckets = await storage.listBuckets();
    const storageBackup = {
      timestamp: new Date().toISOString(),
      buckets: [],
    };

    for (const bucket of buckets.buckets) {
      log(`  🗂️ Backing up bucket: ${bucket.name} (${bucket.$id})`, 'cyan');
      
      const bucketBackup = {
        id: bucket.$id,
        name: bucket.name,
        permissions: bucket.permissions,
        fileSecurity: bucket.fileSecurity,
        enabled: bucket.enabled,
        maximumFileSize: bucket.maximumFileSize,
        allowedFileExtensions: bucket.allowedFileExtensions,
        compression: bucket.compression,
        encryption: bucket.encryption,
        antivirus: bucket.antivirus,
        files: [],
      };

      // Get all files in the bucket
      try {
        const files = await storage.listFiles(bucket.$id);
        bucketBackup.files = files.files.map(file => ({
          id: file.$id,
          name: file.name,
          signature: file.signature,
          mimeType: file.mimeType,
          sizeOriginal: file.sizeOriginal,
          chunksTotal: file.chunksTotal,
          chunksUploaded: file.chunksUploaded,
        }));
        
        log(`    ✅ Backed up ${bucketBackup.files.length} file metadata entries`, 'green');
      } catch (error) {
        log(`    ⚠️ Error backing up files metadata: ${error.message}`, 'yellow');
      }

      storageBackup.buckets.push(bucketBackup);
    }

    const storageBackupFile = path.join(backupPath, 'storage', 'storage.json');
    fs.writeFileSync(storageBackupFile, JSON.stringify(storageBackup, null, 2));
    
    log(`✅ Storage backup completed: ${storageBackup.buckets.length} buckets`, 'green');
    return storageBackup;
  } catch (error) {
    log(`❌ Storage backup failed: ${error.message}`, 'red');
    throw error;
  }
}

// Create backup manifest
function createBackupManifest(backupPath, databaseBackup, usersBackup, storageBackup) {
  const manifest = {
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    appwrite: {
      endpoint: CONFIG.endpoint,
      projectId: CONFIG.projectId,
    },
    backup: {
      databases: {
        count: databaseBackup.databases.length,
        totalCollections: databaseBackup.databases.reduce((sum, db) => sum + db.collections.length, 0),
        totalDocuments: databaseBackup.databases.reduce((sum, db) => 
          sum + db.collections.reduce((colSum, col) => colSum + col.documents.length, 0), 0),
      },
      users: {
        count: usersBackup.total,
      },
      storage: {
        buckets: storageBackup.buckets.length,
        totalFiles: storageBackup.buckets.reduce((sum, bucket) => sum + bucket.files.length, 0),
      },
    },
    files: [
      'databases/databases.json',
      'users/users.json',
      'storage/storage.json',
    ],
  };

  const manifestFile = path.join(backupPath, 'manifest.json');
  fs.writeFileSync(manifestFile, JSON.stringify(manifest, null, 2));
  
  return manifest;
}

// Compress backup if enabled
async function compressBackup(backupPath) {
  if (!CONFIG.compression) {
    return backupPath;
  }

  log('🗜️ Compressing backup...', 'blue');
  
  try {
    const archiver = require('archiver');
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    const backupName = path.basename(backupPath);
    const archivePath = `${backupPath}.zip`;
    const output = fs.createWriteStream(archivePath);
    
    archive.pipe(output);
    archive.directory(backupPath, backupName);
    
    await new Promise((resolve, reject) => {
      output.on('close', resolve);
      archive.on('error', reject);
      archive.finalize();
    });
    
    // Remove uncompressed backup
    fs.rmSync(backupPath, { recursive: true });
    
    log(`✅ Backup compressed: ${archivePath}`, 'green');
    return archivePath;
  } catch (error) {
    log(`⚠️ Compression failed: ${error.message}`, 'yellow');
    return backupPath;
  }
}

// Clean up old backups
function cleanupOldBackups() {
  log('🧹 Cleaning up old backups...', 'blue');
  
  try {
    if (!fs.existsSync(CONFIG.backupDir)) {
      return;
    }

    const files = fs.readdirSync(CONFIG.backupDir);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - CONFIG.retentionDays);

    let deletedCount = 0;

    for (const file of files) {
      const filePath = path.join(CONFIG.backupDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime < cutoffDate) {
        if (stats.isDirectory()) {
          fs.rmSync(filePath, { recursive: true });
        } else {
          fs.unlinkSync(filePath);
        }
        deletedCount++;
        log(`  🗑️ Deleted old backup: ${file}`, 'yellow');
      }
    }

    if (deletedCount > 0) {
      log(`✅ Cleaned up ${deletedCount} old backups`, 'green');
    } else {
      log('✅ No old backups to clean up', 'green');
    }
  } catch (error) {
    log(`⚠️ Cleanup failed: ${error.message}`, 'yellow');
  }
}

// Verify backup integrity
function verifyBackup(backupPath) {
  log('🔍 Verifying backup integrity...', 'blue');
  
  try {
    const manifestPath = path.join(backupPath, 'manifest.json');
    if (!fs.existsSync(manifestPath)) {
      throw new Error('Backup manifest not found');
    }

    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    for (const file of manifest.files) {
      const filePath = path.join(backupPath, file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`Backup file missing: ${file}`);
      }
    }

    log('✅ Backup integrity verified', 'green');
    return true;
  } catch (error) {
    log(`❌ Backup verification failed: ${error.message}`, 'red');
    return false;
  }
}

// Main backup function
async function performBackup() {
  log('🚀 Starting Appwrite backup...', 'magenta');
  
  try {
    // Initialize Appwrite client
    const { databases, users, storage } = initializeClient();
    
    // Create backup directory
    const backupPath = createBackupDirectory();
    log(`📁 Backup directory: ${backupPath}`, 'blue');
    
    // Perform backups
    const databaseBackup = await backupDatabases(databases, backupPath);
    const usersBackup = await backupUsers(users, backupPath);
    const storageBackup = await backupStorage(storage, backupPath);
    
    // Create manifest
    const manifest = createBackupManifest(backupPath, databaseBackup, usersBackup, storageBackup);
    
    // Verify backup
    if (!verifyBackup(backupPath)) {
      throw new Error('Backup verification failed');
    }
    
    // Compress backup if enabled
    const finalBackupPath = await compressBackup(backupPath);
    
    // Clean up old backups
    cleanupOldBackups();
    
    // Display summary
    log('\n📊 Backup Summary:', 'magenta');
    log(`  📁 Backup location: ${finalBackupPath}`, 'blue');
    log(`  🗄️ Databases: ${manifest.backup.databases.count}`, 'blue');
    log(`  📋 Collections: ${manifest.backup.databases.totalCollections}`, 'blue');
    log(`  📄 Documents: ${manifest.backup.databases.totalDocuments}`, 'blue');
    log(`  👥 Users: ${manifest.backup.users.count}`, 'blue');
    log(`  🗂️ Storage buckets: ${manifest.backup.storage.buckets}`, 'blue');
    log(`  📁 Files: ${manifest.backup.storage.totalFiles}`, 'blue');
    
    log('\n✅ Backup completed successfully!', 'green');
    return finalBackupPath;
  } catch (error) {
    log(`\n❌ Backup failed: ${error.message}`, 'red');
    throw error;
  }
}

// Handle script arguments
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'backup':
      await performBackup();
      break;
      
    case 'cleanup':
      cleanupOldBackups();
      break;
      
    case 'verify':
      const backupPath = process.argv[3];
      if (!backupPath) {
        log('Usage: node backup-appwrite.js verify <backup_path>', 'red');
        process.exit(1);
      }
      verifyBackup(backupPath);
      break;
      
    default:
      await performBackup();
      break;
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`\n💥 Unhandled error: ${error.message}`, 'red');
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    log(`\n💥 Script failed: ${error.message}`, 'red');
    process.exit(1);
  });
}

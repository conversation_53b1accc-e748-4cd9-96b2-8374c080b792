import { Injectable } from '@nestjs/common';
import {
  ISkill,
  ISkillContext,
  ISkillResponse,
  MessageType,
} from '../../interfaces';
import {
  LoggerService,
  ErrorHandlerService,
  SkillExecutionError,
} from '../../core';
import { SkillRegistryService } from './skill-registry.service';

export interface SkillRouterResult {
  skill?: ISkill;
  response: ISkillResponse;
  executionTime: number;
  success: boolean;
  error?: string;
}

@Injectable()
export class SkillRouterService {
  constructor(
    private skillRegistry: SkillRegistryService,
    private logger: LoggerService,
    private errorHandler: ErrorHandlerService,
  ) {}

  async routeMessage(
    input: string,
    context: ISkillContext,
  ): Promise<SkillRouterResult> {
    const startTime = Date.now();

    try {
      // Find the best skill to handle this input
      const skill = this.findBestSkill(input, context);

      if (!skill) {
        return this.createNoSkillFoundResult(input, startTime);
      }

      // Execute the skill
      const response = await this.executeSkill(skill, input, context);
      const executionTime = Date.now() - startTime;

      // Update metrics
      this.skillRegistry.updateSkillMetrics(
        skill.id,
        executionTime,
        true,
        this.extractTrigger(input, skill),
      );

      this.logger.logSkillExecution(
        skill.id,
        context.user.id,
        input,
        response.content,
        executionTime,
      );

      return {
        skill,
        response,
        executionTime,
        success: true,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.handleSkillError(error, context, executionTime);
    }
  }

  async executeSkillById(
    skillId: string,
    input: string,
    context: ISkillContext,
  ): Promise<SkillRouterResult> {
    const startTime = Date.now();

    try {
      const skill = this.skillRegistry.getSkill(skillId);

      if (!skill) {
        throw new Error(`Skill not found: ${skillId}`);
      }

      const response = await this.executeSkill(skill, input, context);
      const executionTime = Date.now() - startTime;

      this.skillRegistry.updateSkillMetrics(skillId, executionTime, true);

      return {
        skill,
        response,
        executionTime,
        success: true,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.handleSkillError(error, context, executionTime, skillId);
    }
  }

  getAvailableSkills(context: ISkillContext): ISkill[] {
    const enabledSkills = this.skillRegistry.getEnabledSkills();

    // Filter skills based on user permissions and context
    return enabledSkills.filter((skill) =>
      this.canUserAccessSkill(skill, context),
    );
  }

  findSkillsForInput(input: string): ISkill[] {
    return this.skillRegistry.findSkillsByTrigger(input);
  }

  private findBestSkill(
    input: string,
    context: ISkillContext,
  ): ISkill | undefined {
    // First, try exact trigger matching
    const skillsByTrigger = this.skillRegistry.findSkillsByTrigger(input);

    if (skillsByTrigger.length > 0) {
      // Filter by user access and return the highest priority skill
      const accessibleSkills = skillsByTrigger.filter((skill) =>
        this.canUserAccessSkill(skill, context),
      );

      if (accessibleSkills.length > 0) {
        return accessibleSkills[0]; // Already sorted by priority
      }
    }

    // If no exact match, try fuzzy matching or AI-based routing
    return this.findSkillByFuzzyMatching(input, context);
  }

  private findSkillByFuzzyMatching(
    input: string,
    context: ISkillContext,
  ): ISkill | undefined {
    const normalizedInput = input.toLowerCase().trim();
    const availableSkills = this.getAvailableSkills(context);

    // Simple fuzzy matching based on trigger similarity
    const matches: Array<{ skill: ISkill; score: number }> = [];

    for (const skill of availableSkills) {
      let maxScore = 0;

      for (const trigger of skill.triggers) {
        const score = this.calculateSimilarity(
          normalizedInput,
          trigger.toLowerCase(),
        );
        maxScore = Math.max(maxScore, score);
      }

      if (maxScore > 0.3) {
        // Minimum similarity threshold
        matches.push({ skill, score: maxScore });
      }
    }

    // Sort by score and return the best match
    matches.sort((a, b) => b.score - a.score);
    return matches.length > 0 ? matches[0].skill : undefined;
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Simple Jaccard similarity for word sets
    const words1 = new Set(str1.split(/\s+/));
    const words2 = new Set(str2.split(/\s+/));

    const intersection = new Set([...words1].filter((x) => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  private async executeSkill(
    skill: ISkill,
    input: string,
    context: ISkillContext,
  ): Promise<ISkillResponse> {
    const config = this.skillRegistry.getSkillConfig(skill.id);
    const timeout = config?.timeout || 30000; // 30 seconds default

    // Execute with timeout
    return Promise.race([
      skill.execute(input, context),
      this.createTimeoutPromise(timeout, skill.id),
    ]);
  }

  private createTimeoutPromise(
    timeout: number,
    skillId: string,
  ): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Skill execution timeout: ${skillId} (${timeout}ms)`));
      }, timeout);
    });
  }

  private canUserAccessSkill(skill: ISkill, _context: ISkillContext): boolean {
    // Basic access control - can be extended with more sophisticated logic

    // Check if skill is enabled
    const config = this.skillRegistry.getSkillConfig(skill.id);
    if (config && !config.enabled) {
      return false;
    }

    // Check user permissions (placeholder for future implementation)
    // const requiredPermissions = skill.getRequiredPermissions?.() || [];
    // return this.userHasPermissions(context.user, requiredPermissions);

    return true;
  }

  private extractTrigger(input: string, skill: ISkill): string | undefined {
    const normalizedInput = input.toLowerCase();

    for (const trigger of skill.triggers) {
      if (normalizedInput.includes(trigger.toLowerCase())) {
        return trigger;
      }
    }

    return undefined;
  }

  private createNoSkillFoundResult(
    _input: string,
    startTime: number,
  ): SkillRouterResult {
    const executionTime = Date.now() - startTime;

    return {
      response: {
        content:
          "I'm not sure how to help with that. Try asking me about memories, reminders, or say 'help' to see what I can do.",
        type: MessageType.TEXT,
        metadata: { noSkillFound: true, input: _input },
      },
      executionTime,
      success: false,
      error: 'No skill found for input',
    };
  }

  private handleSkillError(
    error: any,
    context: ISkillContext,
    executionTime: number,
    skillId?: string,
  ): SkillRouterResult {
    const paimError =
      error instanceof SkillExecutionError
        ? error
        : new SkillExecutionError(skillId || 'unknown', error);

    const handledError = this.errorHandler.handleError(
      paimError,
      'SkillRouter',
      context.user.id,
    );
    const userMessage =
      this.errorHandler.createUserFriendlyMessage(handledError);

    // Update metrics for failed execution
    if (skillId) {
      this.skillRegistry.updateSkillMetrics(skillId, executionTime, false);
    }

    return {
      response: {
        content: userMessage,
        type: MessageType.TEXT,
        metadata: {
          error: true,
          errorCode: handledError.code,
          skillId,
        },
      },
      executionTime,
      success: false,
      error: handledError.message,
    };
  }
}

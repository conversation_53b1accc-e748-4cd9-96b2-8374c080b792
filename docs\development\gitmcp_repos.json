{"mcpServers": {"git-mcp Docs": {"command": "npx", "args": ["mcp-remote", "https://gitmcp.io/NanoNets/docext", "https://gitmcp.io/ruxakK/friday_jarvis", "https://gitmcp.io/livekit-examples/android-voice-assistant", "https://gitmcp.io/DeepEval/deep-eval", "https://gitmcp.io/gooseai/goose", "https://gitmcp.io/SelfArchitect/self-llm", "https://gitmcp.io/niri-ai/niri", "https://gitmcp.io/merlio/merlio", "https://gitmcp.io/explodinggradients/all-rag-techniques", "https://gitmcp.io/dagger/dagger", "https://gitmcp.io/prompt-engineering/prompt-eng-interactive-tutorial", "https://gitmcp.io/memvid/memvid", "https://gitmcp.io/openmachines/OMAgent", "https://gitmcp.io/ai-mcp/GTask-MCP", "https://gitmcp.io/ai-mcp/google-calendar-mcp", "https://gitmcp.io/ai-mcp/slack-mcp-server", "https://gitmcp.io/ai-mcp/mcp-gsuite", "https://gitmcp.io/NilFoundation/nilweb", "https://gitmcp.io/RVC-Project/RVC", "https://gitmcp.io/opik-dev/opik", "https://gitmcp.io/graphiti-dev/graphiti", "https://gitmcp.io/antispace-dev/antispace", "https://gitmcp.io/ai-mcp/pipedream-mcp", "https://gitmcp.io/audiocontrol/audio-visual-control", "https://gitmcp.io/hyperarc/hyperarcqd", "https://gitmcp.io/acidev/aci.dev", "https://gitmcp.io/mem0-ai/mem0", "https://gitmcp.io/mimo-ai/mimo", "https://gitmcp.io/union-ai/union", "https://gitmcp.io/surfsense/surfsense", "https://gitmcp.io/kortexai/kortex-ai-suna", "https://gitmcp.io/chunkie-ai/chunkie", "https://gitmcp.io/gpui/gpui", "https://gitmcp.io/xcodebuild/xcode-build", "https://gitmcp.io/copilotkit/copilotkit", "https://gitmcp.io/graphite-dev/graphite", "https://gitmcp.io/unsloth-ai/unsloth", "https://gitmcp.io/agentic/agentic-seek", "https://gitmcp.io/iiagent/ii-agent", "https://gitmcp.io/magneticui/magnetic-ui", "https://gitmcp.io/lms-saas/lms-saas", "https://gitmcp.io/payload-cms/payload", "https://gitmcp.io/tweakcn/tweakcn", "https://gitmcp.io/unmute-ai/unmute", "https://gitmcp.io/deepagent/deep-agent", "https://gitmcp.io/chatterbox-ai/chatterbox", "https://gitmcp.io/arcade-ai/arcadeai", "https://gitmcp.io/titan-ai/titan", "https://gitmcp.io/appwrite/appwrite", "https://gitmcp.io/magic-ui/magic-ui", "https://gitmcp.io/microsandbox/ag-ui", "https://gitmcp.io/talkingmachines/talking-machines"]}}}
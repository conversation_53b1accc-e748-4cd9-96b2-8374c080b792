server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # PAIM application logs
  - job_name: paim-app
    static_configs:
      - targets:
          - localhost
        labels:
          job: paim-app
          service: paim-core
          environment: development
          __path__: /var/log/paim/*.log
    
    pipeline_stages:
      # Parse JSON logs
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            context: context
            trace_id: trace_id
            user_id: user_id
            
      # Extract timestamp
      - timestamp:
          source: timestamp
          format: RFC3339Nano
          
      # Add labels based on log level
      - labels:
          level:
          service:
          environment:
          
      # Add trace_id as label if present
      - labels:
          trace_id:
          
      # Filter out debug logs in production
      - match:
          selector: '{level="debug"}'
          action: drop
          drop_counter_reason: debug_logs_filtered

  # System logs
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: system
          service: system
          __path__: /var/log/syslog
    
    pipeline_stages:
      # Parse syslog format
      - regex:
          expression: '^(?P<timestamp>\S+\s+\d+\s+\d+:\d+:\d+)\s+(?P<hostname>\S+)\s+(?P<service>\S+):\s+(?P<message>.*)$'
          
      # Parse timestamp
      - timestamp:
          source: timestamp
          format: Jan 2 15:04:05
          
      # Add labels
      - labels:
          hostname:
          service:

  # Docker container logs
  - job_name: docker
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          service: docker
          __path__: /var/lib/docker/containers/*/*.log
    
    pipeline_stages:
      # Parse Docker JSON logs
      - json:
          expressions:
            log: log
            stream: stream
            time: time
            
      # Parse timestamp
      - timestamp:
          source: time
          format: RFC3339Nano
          
      # Add stream as label
      - labels:
          stream:
          
      # Use log content as message
      - output:
          source: log

  # Nginx access logs (if using nginx)
  - job_name: nginx-access
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx
          service: nginx
          log_type: access
          __path__: /var/log/nginx/access.log
    
    pipeline_stages:
      # Parse nginx access log format
      - regex:
          expression: '^(?P<remote_addr>\S+)\s+-\s+(?P<remote_user>\S+)\s+\[(?P<time_local>[^\]]+)\]\s+"(?P<method>\S+)\s+(?P<request_uri>\S+)\s+(?P<protocol>\S+)"\s+(?P<status>\d+)\s+(?P<body_bytes_sent>\d+)\s+"(?P<http_referer>[^"]*)"\s+"(?P<http_user_agent>[^"]*)"'
          
      # Parse timestamp
      - timestamp:
          source: time_local
          format: 02/Jan/2006:15:04:05 -0700
          
      # Add labels
      - labels:
          method:
          status:
          remote_addr:

  # Nginx error logs
  - job_name: nginx-error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx
          service: nginx
          log_type: error
          __path__: /var/log/nginx/error.log
    
    pipeline_stages:
      # Parse nginx error log format
      - regex:
          expression: '^(?P<timestamp>\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2})\s+\[(?P<level>\w+)\]\s+(?P<message>.*)$'
          
      # Parse timestamp
      - timestamp:
          source: timestamp
          format: 2006/01/02 15:04:05
          
      # Add labels
      - labels:
          level:

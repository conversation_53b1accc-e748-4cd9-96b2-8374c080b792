import { Controller, Get, Post, Body } from '@nestjs/common';
import { TwilioService } from './twilio.service';

@Controller('twilio')
export class TwilioController {
  constructor(private readonly twilioService: TwilioService) {}

  @Get('test')
  async testConnection() {
    return this.twilioService.testConnection();
  }

  @Post('send')
  async sendMessage(@Body() body: { to: string; message: string }) {
    return this.twilioService.sendWhatsAppMessage(body.to, body.message);
  }
}

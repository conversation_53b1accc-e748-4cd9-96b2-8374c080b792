import { Injectable } from '@nestjs/common';
import { MessageType, IMessageResponse } from '../../interfaces';
import { LoggerService, ErrorHandlerService } from '../../core';
import { TwilioService } from './twilio.service';

export interface SendMessageOptions {
  to: string;
  content: string;
  type?: MessageType;
  mediaUrl?: string;
  retryCount?: number;
  priority?: 'low' | 'normal' | 'high';
}

export interface MessageSendResult {
  success: boolean;
  messageSid?: string;
  error?: string;
  retryAfter?: number;
}

@Injectable()
export class MessageSendingService {
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAYS = [1000, 3000, 5000]; // milliseconds
  private readonly MAX_MESSAGE_LENGTH = 1600; // WhatsApp limit

  constructor(
    private twilioService: TwilioService,
    private logger: LoggerService,
    private errorHandler: ErrorHandlerService,
  ) {}

  async sendMessage(options: SendMessageOptions): Promise<MessageSendResult> {
    try {
      // Validate and prepare message
      const preparedMessage = this.prepareMessage(options);

      // Send with retry logic
      const result = await this.sendWithRetry(
        preparedMessage,
        options.retryCount || 0,
      );

      if (result.success) {
        this.logger.log(
          `Message sent successfully to ${options.to}: ${result.messageSid}`,
          'MessageSendingService',
        );
      } else {
        this.logger.error(
          `Failed to send message to ${options.to}: ${result.error}`,
          undefined,
          'MessageSendingService',
        );
      }

      return result;
    } catch (error) {
      const handledError = this.errorHandler.handleError(
        error instanceof Error ? error : new Error(String(error)),
        'MessageSendingService',
      );

      return {
        success: false,
        error: handledError.message,
      };
    }
  }

  async sendTextMessage(
    to: string,
    content: string,
  ): Promise<MessageSendResult> {
    return this.sendMessage({
      to,
      content,
      type: MessageType.TEXT,
    });
  }

  async sendMediaMessage(
    to: string,
    content: string,
    mediaUrl: string,
  ): Promise<MessageSendResult> {
    return this.sendMessage({
      to,
      content,
      type: MessageType.IMAGE, // or determine from mediaUrl
      mediaUrl,
    });
  }

  async sendResponse(
    response: IMessageResponse,
    to: string,
  ): Promise<MessageSendResult> {
    return this.sendMessage({
      to,
      content: response.content,
      type: response.type,
      mediaUrl: response.metadata?.mediaUrl,
    });
  }

  async sendBulkMessages(
    messages: SendMessageOptions[],
  ): Promise<MessageSendResult[]> {
    const results: MessageSendResult[] = [];

    // Process messages in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < messages.length; i += batchSize) {
      const batch = messages.slice(i, i + batchSize);

      const batchPromises = batch.map((message) => this.sendMessage(message));
      const batchResults = await Promise.allSettled(batchPromises);

      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            error: result.reason?.message || 'Unknown error',
          });

          this.logger.error(
            `Bulk message failed for ${batch[index].to}`,
            result.reason instanceof Error ? result.reason.stack : undefined,
            'MessageSendingService',
          );
        }
      });

      // Add delay between batches to respect rate limits
      if (i + batchSize < messages.length) {
        await this.delay(1000);
      }
    }

    return results;
  }

  private prepareMessage(options: SendMessageOptions): SendMessageOptions {
    let content = options.content;

    // Truncate message if too long
    if (content.length > this.MAX_MESSAGE_LENGTH) {
      content = content.substring(0, this.MAX_MESSAGE_LENGTH - 3) + '...';

      this.logger.warn(
        `Message truncated for ${options.to} (original length: ${options.content.length})`,
        'MessageSendingService',
      );
    }

    // Format phone number
    const to = this.twilioService.formatPhoneNumber(options.to);

    // Validate phone number
    if (!this.twilioService.isValidWhatsAppNumber(to)) {
      throw new Error(`Invalid WhatsApp number: ${options.to}`);
    }

    return {
      ...options,
      to,
      content,
    };
  }

  private async sendWithRetry(
    options: SendMessageOptions,
    currentRetry: number = 0,
  ): Promise<MessageSendResult> {
    try {
      const result = await this.twilioService.sendMessage({
        to: options.to,
        body: options.content,
        mediaUrl: options.mediaUrl,
      });

      if (result.success) {
        return {
          success: true,
          messageSid: result.messageSid,
        };
      } else {
        // Check if error is retryable
        if (
          this.isRetryableError(result.error) &&
          currentRetry < this.MAX_RETRIES
        ) {
          return this.retryMessage(options, currentRetry);
        } else {
          return {
            success: false,
            error: result.error,
          };
        }
      }
    } catch (error) {
      // Check if error is retryable
      if (
        this.isRetryableError(
          error instanceof Error ? error.message : String(error),
        ) &&
        currentRetry < this.MAX_RETRIES
      ) {
        return this.retryMessage(options, currentRetry);
      } else {
        throw error;
      }
    }
  }

  private async retryMessage(
    options: SendMessageOptions,
    currentRetry: number,
  ): Promise<MessageSendResult> {
    const delay =
      this.RETRY_DELAYS[currentRetry] ||
      this.RETRY_DELAYS[this.RETRY_DELAYS.length - 1];

    this.logger.warn(
      `Retrying message to ${options.to} in ${delay}ms (attempt ${currentRetry + 1}/${this.MAX_RETRIES})`,
      'MessageSendingService',
    );

    await this.delay(delay);
    return this.sendWithRetry(options, currentRetry + 1);
  }

  private isRetryableError(error?: string): boolean {
    if (!error) return false;

    const retryableErrors = [
      'rate limit',
      'timeout',
      'network',
      'temporary',
      'service unavailable',
      '429',
      '502',
      '503',
      '504',
    ];

    const lowerError = error.toLowerCase();
    return retryableErrors.some((retryableError) =>
      lowerError.includes(retryableError),
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async getMessageDeliveryStatus(messageSid: string): Promise<any> {
    try {
      return await this.twilioService.getMessageStatus(messageSid);
    } catch (error) {
      this.logger.error(
        `Failed to get delivery status for ${messageSid}`,
        error instanceof Error ? error.stack : undefined,
        'MessageSendingService',
      );
      throw error;
    }
  }

  formatMessageForWhatsApp(content: string, type: MessageType): string {
    switch (type) {
      case MessageType.TEXT:
        return content;

      case MessageType.SYSTEM:
        return `🤖 ${content}`;

      default:
        return content;
    }
  }

  splitLongMessage(content: string): string[] {
    if (content.length <= this.MAX_MESSAGE_LENGTH) {
      return [content];
    }

    const messages: string[] = [];
    let currentMessage = '';
    const words = content.split(' ');

    for (const word of words) {
      if ((currentMessage + ' ' + word).length > this.MAX_MESSAGE_LENGTH) {
        if (currentMessage) {
          messages.push(currentMessage.trim());
          currentMessage = word;
        } else {
          // Single word is too long, split it
          messages.push(word.substring(0, this.MAX_MESSAGE_LENGTH - 3) + '...');
          currentMessage = word.substring(this.MAX_MESSAGE_LENGTH - 3);
        }
      } else {
        currentMessage += (currentMessage ? ' ' : '') + word;
      }
    }

    if (currentMessage) {
      messages.push(currentMessage.trim());
    }

    return messages;
  }
}

# Product Requirements Document (PRD) - <PERSON><PERSON> (Personal AI Manager)

## Project Overview

**Product Name**: <PERSON><PERSON> (Personal AI Manager)\
**Version**: MVMP 0.1\
**Owner**: Kanousei Technology / Harold <PERSON> Jr.\
**Created**: June 2025

## Purpose

PAIM is a modular, personal AI system built to act as a user's second brain. It captures, organizes, and retrieves thoughts, assists with routine tasks, connects to third-party services, and evolves through skills. The MVP emphasizes WhatsApp-based interaction, voice-first input, multilingual support (Arabic-first), and extensibility via MCP (Modular Control Protocol).

---

## Core Goals

- Capture thoughts and convert them into memory
- Support reminders and routine reflection
- Interact primarily via WhatsApp (Phase 1)
- Personalize PAIM’s name, tone, goals, and language
- Allow secure, persistent memory
- Support future integrations with AIgency & AIIA via MCP

---

## User Personas

### 1. Solo Professionals

- Want to stay organized without another app
- Voice or text based note-taking and follow-up

### 2. Founders / Leaders

- Want thought delegation, reminders, and project vision capture
- Interested in memory export and team awareness

### 3. Arabic-first Users

- Require full conversational support in Arabic
- Expect dialect-sensitive responses and formality controls

---

## Phase 1 Functional Scope

### ✅ Core Skills

| Skill ID         | Name                   | Description                                       |
| ---------------- | ---------------------- | ------------------------------------------------- |
| onboarding       | Personalized setup     | Set PAIM name, tone, goals, language              |
| memory.capture   | Capture thought/memory | Save ideas, notes, reflections                    |
| memory.search    | Retrieve memory        | Pull past thoughts by time, keyword, tag          |
| remind.me        | Set reminders          | Trigger follow-ups by time or intent              |
| tone.switch      | Adjust tone            | Friendly / Pro / Witty                            |
| roadmap          | Display roadmap        | Show upcoming capabilities                        |
| voice.transcribe | Audio → text           | Transcribe voice notes into memory                |
| check.in         | Daily reflection       | Ask user short questions to log emotions or goals |

### 🔧 MCP System (AIgency 0.1)

| Feature           | Description                                                |
| ----------------- | ---------------------------------------------------------- |
| mcp.install-skill | Allow PAIM to install new skills (e.g., Slack, GCal, etc.) |
| mcp.list-skills   | Discover available integrations                            |
| mcp.auth-handler  | Store OAuth/API keys securely                              |
| mcp.audit-log     | Log added/removed skills                                   |

---

## Technical Architecture

### Stack

- **Runtime**: Node.js + TypeScript (NestJS-friendly)
- **Message Platform**: WhatsApp via Twilio or Meta API
- **Database**: JSON memory (MVP), LiteFS or SQLite, optional Supabase
- **Voice Input**: OpenAI Whisper or Coqui TTS (Phase 2)
- **Deployment**: DigitalOcean (functions), Vercel (UI), optional Deno Edge

### Structure

```
/paim-core
├── skills/            # Skill handlers
├── services/          # Core logic: tone, memory, routing
├── registry/          # Dynamic skill list
├── config/            # User defaults, tone presets
├── arabic-brain/      # Arabic-specific NLP modules
├── webhook/           # WhatsApp integration point
└── storage/           # Memory export functions
```

### Memory Structure

```json
{
  "user_id": "abc123",
  "name": "Zara",
  "tone": "friendly",
  "language": "ar",
  "goals": ["organize thoughts", "focus"],
  "memory": [
    {
      "text": "Idea for launch campaign",
      "created_at": "2025-06-15T08:00Z",
      "language": "en",
      "tags": ["marketing", "project"]
    }
  ]
}
```

---

## Arabic Brain (Phase 1)

- Tokenize dialectical Arabic
- Recognize intent and tone in MSA and Khaleeji
- Translations using LibreTranslate fallback
- Personality localization (UAEness, tone formality, etc.)
- File: `arabic-brain/tokenizer.ts`, `dialects/uae.json`, etc.

---

## Success Metrics (MVP)

- TTFU (Time to First Useful Output): < 90 seconds
- Voice note usage within 3 days: 60%+ of users
- Custom tone config set: 50% of users
- Retained memory used within 5 days: 40%+ users

---

## Future Phases (Planned)

| Phase   | Focus                           |
| ------- | ------------------------------- |
| Phase 2 | MCP: Slack, GSuite, Notion      |
| Phase 3 | Group PAIM with admin controls  |
| Phase 4 | Sync with Dropbox/GDrive/iCloud |
| Phase 5 | Arabic Brain + TTS + voice UI   |
| Phase 6 | Living UI frontend + dashboard  |
| Phase 7 | AI Memory refinement (chunking) |

---

## Security Considerations

- End-to-end encrypted session memory (future)
- OAuth token store scoped per service
- User export + delete request (one-command)
- Admin panel for viewing skill logs and actions

---

## Team Roles

- **Founder/Product**: Harold S. Dickenson Jr.
- **Architect**: Cove (ChatGPT)
- **DevOps**: Augment Code
- **Voice/NLP Arabic**: Future hire

---

## Notes

- PAIM should never *feel* like an app. It should *feel* like a partner.
- Use tone + trust to define relationship.
- Build everything like it will be remixed into something greater.
- Development reference: https://chatgpt.com/share/68572827-4b2c-800a-aa29-7d512bfd75b6


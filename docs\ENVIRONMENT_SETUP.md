# 🔧 Environment Setup Guide

This guide covers the setup and management of environment variables and secrets for the PAIM project across different deployment environments.

## 📋 Table of Contents

- [Environment Overview](#environment-overview)
- [GitHub Secrets Configuration](#github-secrets-configuration)
- [Environment Variables](#environment-variables)
- [Digital Ocean Configuration](#digital-ocean-configuration)
- [Local Development Setup](#local-development-setup)
- [Security Best Practices](#security-best-practices)
- [Troubleshooting](#troubleshooting)

## 🌍 Environment Overview

The PAIM project supports multiple environments:

- **Development** - Local development environment
- **Staging** - Testing environment for pre-production validation
- **Production** - Live production environment

## 🔐 GitHub Secrets Configuration

### Required Repository Secrets

Configure these secrets in your GitHub repository settings (`Settings > Secrets and variables > Actions`):

#### Digital Ocean Secrets
```
DIGITALOCEAN_ACCESS_TOKEN
```
- **Description**: Digital Ocean API token for app deployment
- **How to get**: Digital Ocean Console > API > Personal Access Tokens
- **Permissions**: Read/Write access to Apps

#### Twilio Secrets
```
TWILIO_ACCOUNT_SID
TWILIO_AUTH_TOKEN
TWILIO_WHATSAPP_NUMBER
```
- **Description**: Twilio credentials for WhatsApp integration
- **How to get**: Twilio Console > Account Info
- **Format**: 
  - `TWILIO_WHATSAPP_NUMBER`: `whatsapp:+**********`

#### OpenAI Secrets
```
OPENAI_API_KEY
```
- **Description**: OpenAI API key for AI functionality
- **How to get**: OpenAI Platform > API Keys
- **Format**: `sk-...`

#### Appwrite Secrets
```
APPWRITE_ENDPOINT
APPWRITE_PROJECT_ID
APPWRITE_API_KEY
APPWRITE_DATABASE_ID
```
- **Description**: Appwrite backend service credentials
- **How to get**: Appwrite Console > Project Settings
- **Example**:
  - `APPWRITE_ENDPOINT`: `https://cloud.appwrite.io/v1`
  - `APPWRITE_PROJECT_ID`: `your-project-id`

### Environment-Specific Secrets

#### Staging Environment
```
STAGING_TWILIO_ACCOUNT_SID
STAGING_TWILIO_AUTH_TOKEN
STAGING_TWILIO_WHATSAPP_NUMBER
STAGING_OPENAI_API_KEY
STAGING_APPWRITE_ENDPOINT
STAGING_APPWRITE_PROJECT_ID
STAGING_APPWRITE_API_KEY
STAGING_APPWRITE_DATABASE_ID
```

#### Production Environment
Use the main secrets (without prefix) for production.

## 🔧 Environment Variables

### Core Application Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Application environment | `development` | Yes |
| `PORT` | Application port | `3000` | Yes |
| `LOG_LEVEL` | Logging level | `info` | No |

### External Service Variables

| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| `TWILIO_ACCOUNT_SID` | Twilio Account SID | `ACxxxxx` | Yes |
| `TWILIO_AUTH_TOKEN` | Twilio Auth Token | `xxxxx` | Yes |
| `TWILIO_WHATSAPP_NUMBER` | WhatsApp Business Number | `whatsapp:+**********` | Yes |
| `OPENAI_API_KEY` | OpenAI API Key | `sk-xxxxx` | Yes |
| `APPWRITE_ENDPOINT` | Appwrite API Endpoint | `https://cloud.appwrite.io/v1` | Yes |
| `APPWRITE_PROJECT_ID` | Appwrite Project ID | `project-id` | Yes |
| `APPWRITE_API_KEY` | Appwrite API Key | `xxxxx` | Yes |
| `APPWRITE_DATABASE_ID` | Appwrite Database ID | `database-id` | Yes |

### Optional Configuration Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `RATE_LIMIT_WINDOW` | Rate limiting window (ms) | `900000` | `900000` |
| `RATE_LIMIT_MAX` | Max requests per window | `100` | `100` |
| `SESSION_TIMEOUT` | User session timeout (ms) | `1800000` | `1800000` |
| `MAX_MESSAGE_LENGTH` | Max WhatsApp message length | `1600` | `1600` |
| `AI_MODEL` | OpenAI model to use | `gpt-3.5-turbo` | `gpt-4` |
| `AI_MAX_TOKENS` | Max tokens for AI responses | `150` | `300` |

## 🚀 Digital Ocean Configuration

### App Platform Environment Variables

Digital Ocean App Platform automatically injects these variables:

- `PORT` - Automatically set by the platform
- `NODE_ENV` - Set based on environment configuration

### Custom Environment Variables

Configure in Digital Ocean App Platform:

1. Go to your app in Digital Ocean Console
2. Navigate to Settings > App Spec
3. Add environment variables in the `envs` section:

```yaml
envs:
  - key: NODE_ENV
    value: production
  - key: LOG_LEVEL
    value: info
  - key: TWILIO_ACCOUNT_SID
    value: ${TWILIO_ACCOUNT_SID}
    type: SECRET
```

## 💻 Local Development Setup

### 1. Environment File Setup

Create environment files for local development:

```bash
# .env.development
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_dev_account_sid
TWILIO_AUTH_TOKEN=your_dev_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+your_dev_number

# OpenAI Configuration
OPENAI_API_KEY=your_dev_openai_key

# Appwrite Configuration
APPWRITE_ENDPOINT=https://your-dev-appwrite.io/v1
APPWRITE_PROJECT_ID=your_dev_project_id
APPWRITE_API_KEY=your_dev_api_key
APPWRITE_DATABASE_ID=your_dev_database_id
```

### 2. Environment Validation Script

Use the provided validation script:

```bash
npm run validate:env
```

### 3. Docker Development

For Docker-based development:

```bash
# Copy environment template
cp .env.example .env.docker

# Edit with your values
nano .env.docker

# Start with Docker Compose
npm run docker:dev
```

## 🔒 Security Best Practices

### 1. Secret Rotation

- Rotate secrets regularly (every 90 days)
- Use different secrets for each environment
- Never commit secrets to version control

### 2. Access Control

- Limit GitHub repository access
- Use principle of least privilege
- Regularly audit access permissions

### 3. Environment Isolation

- Keep staging and production completely separate
- Use different service accounts for each environment
- Monitor secret usage and access

### 4. Secret Management

- Use GitHub Secrets for CI/CD
- Use Digital Ocean's secret management for runtime
- Consider using external secret management tools for enterprise

## 🔍 Environment Validation

### Validation Script

Create a script to validate environment configuration:

```bash
#!/bin/bash
# scripts/validate-environment.sh

echo "🔍 Validating environment configuration..."

# Check required variables
required_vars=(
  "TWILIO_ACCOUNT_SID"
  "TWILIO_AUTH_TOKEN"
  "TWILIO_WHATSAPP_NUMBER"
  "OPENAI_API_KEY"
  "APPWRITE_ENDPOINT"
  "APPWRITE_PROJECT_ID"
  "APPWRITE_API_KEY"
  "APPWRITE_DATABASE_ID"
)

missing_vars=()

for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    missing_vars+=("$var")
  fi
done

if [ ${#missing_vars[@]} -eq 0 ]; then
  echo "✅ All required environment variables are set"
else
  echo "❌ Missing required environment variables:"
  printf '%s\n' "${missing_vars[@]}"
  exit 1
fi
```

### GitHub Actions Validation

Environment validation is automatically run in CI/CD:

```yaml
- name: Validate Environment
  run: |
    node scripts/validate-environment.js
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Missing Environment Variables
```
Error: Required environment variable TWILIO_ACCOUNT_SID is not set
```
**Solution**: Check that all required secrets are configured in GitHub and Digital Ocean.

#### 2. Invalid Twilio Credentials
```
Error: Twilio authentication failed
```
**Solution**: Verify Twilio credentials and ensure the WhatsApp number format is correct.

#### 3. Appwrite Connection Issues
```
Error: Failed to connect to Appwrite
```
**Solution**: Check Appwrite endpoint URL and ensure API key has correct permissions.

#### 4. Digital Ocean Deployment Failures
```
Error: App deployment failed
```
**Solution**: Check Digital Ocean app logs and verify all secrets are properly configured.

### Debug Commands

```bash
# Check environment variables
npm run env:check

# Test external service connections
npm run test:connections

# Validate configuration
npm run validate:config

# Check deployment status
npm run deploy:status
```

### Support

For additional support:

1. Check the [troubleshooting guide](./TROUBLESHOOTING.md)
2. Review Digital Ocean app logs
3. Check GitHub Actions workflow logs
4. Contact the development team

---

**Note**: Always follow security best practices when handling environment variables and secrets. Never expose sensitive information in logs or error messages.

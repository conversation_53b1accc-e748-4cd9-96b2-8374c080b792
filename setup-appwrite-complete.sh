#!/bin/bash

# Complete Appwrite Setup Script for Droplet
# Run this script on your Appwrite droplet (***************)

set -e

echo "🚀 Setting up Appwrite on droplet..."

# Update system
echo "📦 Updating system packages..."
apt-get update
apt-get install -y curl wget unzip

# Install Docker Compose if not present
echo "🐳 Installing Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# Create appwrite directory
echo "📁 Creating Appwrite directory..."
mkdir -p /opt/appwrite
cd /opt/appwrite

# Create Docker Compose file
echo "📝 Creating Docker Compose configuration..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # Appwrite main service
  appwrite:
    image: appwrite/appwrite:1.4.13
    container_name: sanad-appwrite
    restart: unless-stopped
    networks:
      - appwrite
    ports:
      - "80:80"
    volumes:
      - appwrite-uploads:/storage/uploads:rw
      - appwrite-cache:/storage/cache:rw
      - appwrite-config:/storage/config:rw
      - appwrite-certificates:/storage/certificates:rw
      - appwrite-functions:/storage/functions:rw
    depends_on:
      - mariadb
      - redis
    environment:
      - _APP_ENV=production
      - _APP_WORKER_PER_CORE=6
      - _APP_LOCALE=en
      - _APP_CONSOLE_WHITELIST_ROOT=enabled
      - _APP_CONSOLE_WHITELIST_EMAILS=<EMAIL>
      - _APP_SYSTEM_EMAIL_NAME=Sanad
      - _APP_SYSTEM_EMAIL_ADDRESS=<EMAIL>
      - _APP_SYSTEM_SECURITY_EMAIL_ADDRESS=<EMAIL>
      - _APP_OPTIONS_ABUSE=enabled
      - _APP_OPTIONS_ROUTER_PROTECTION=disabled
      - _APP_OPTIONS_FORCE_HTTPS=disabled
      - _APP_OPTIONS_FUNCTIONS_FORCE_HTTPS=disabled
      - _APP_OPENSSL_KEY_V1=${_APP_OPENSSL_KEY_V1}
      - _APP_DOMAIN=appwrite.sanad.kanousai.com
      - _APP_DOMAIN_TARGET=appwrite.sanad.kanousai.com
      - _APP_DOMAIN_FUNCTIONS=functions.sanad.kanousai.com
      - _APP_REDIS_HOST=redis
      - _APP_REDIS_PORT=6379
      - _APP_DB_HOST=mariadb
      - _APP_DB_PORT=3306
      - _APP_DB_SCHEMA=appwrite
      - _APP_DB_USER=${_APP_DB_USER}
      - _APP_DB_PASS=${_APP_DB_PASS}
      - _APP_STORAGE_LIMIT=30000000
      - _APP_STORAGE_DEVICE=local
      - _APP_FUNCTIONS_SIZE_LIMIT=30000000
      - _APP_FUNCTIONS_TIMEOUT=900
      - _APP_FUNCTIONS_BUILD_TIMEOUT=900
      - _APP_FUNCTIONS_CONTAINERS=10
      - _APP_FUNCTIONS_RUNTIMES=node-16.0,php-8.0,python-3.9
      - _APP_EXECUTOR_SECRET=${_APP_EXECUTOR_SECRET}
      - _APP_MAINTENANCE_INTERVAL=86400

  # MariaDB database
  mariadb:
    image: mariadb:10.7
    container_name: sanad-appwrite-mariadb
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-mariadb:/var/lib/mysql:rw
    environment:
      - MYSQL_ROOT_PASSWORD=${_APP_DB_ROOT_PASS}
      - MYSQL_DATABASE=appwrite
      - MYSQL_USER=${_APP_DB_USER}
      - MYSQL_PASSWORD=${_APP_DB_PASS}
    command: 'mysqld --innodb-flush-method=fsync'

  # Redis cache
  redis:
    image: redis:7.0-alpine
    container_name: sanad-appwrite-redis
    restart: unless-stopped
    networks:
      - appwrite
    volumes:
      - appwrite-redis:/data:rw
    command: redis-server --save 60 1 --loglevel warning

networks:
  appwrite:
    name: appwrite

volumes:
  appwrite-mariadb:
  appwrite-redis:
  appwrite-cache:
  appwrite-uploads:
  appwrite-certificates:
  appwrite-functions:
  appwrite-config:
EOF

# Generate secure environment variables
echo "🔐 Generating secure keys..."
OPENSSL_KEY=$(openssl rand -base64 32)
EXECUTOR_SECRET=$(openssl rand -base64 32)
DB_ROOT_PASS=$(openssl rand -base64 24)
DB_PASS=$(openssl rand -base64 24)

# Create environment file
cat > .env << EOF
# Appwrite Environment Configuration
_APP_OPENSSL_KEY_V1=$OPENSSL_KEY
_APP_EXECUTOR_SECRET=$EXECUTOR_SECRET
_APP_DB_ROOT_PASS=$DB_ROOT_PASS
_APP_DB_USER=appwrite
_APP_DB_PASS=$DB_PASS
EOF

echo "✅ Environment file created with secure keys"

# Start Appwrite services
echo "🚀 Starting Appwrite services..."
docker-compose --env-file .env up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Check service status
echo "📊 Checking service status..."
docker-compose ps

# Test Appwrite health
echo "🏥 Testing Appwrite health..."
sleep 10
curl -f http://localhost/health || echo "Health check will be available once fully started"

echo ""
echo "🎉 Appwrite setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Access Appwrite console: http://***************/console"
echo "2. Create your first project"
echo "3. Note down Project ID and API Key"
echo "4. Run the database schema setup script"
echo ""
echo "🔧 Useful commands:"
echo "  docker-compose ps                 # Check service status"
echo "  docker-compose logs -f appwrite   # View Appwrite logs"
echo "  docker-compose restart            # Restart services"
echo ""
echo "📝 Generated credentials saved in .env file"
echo "🌐 Appwrite is accessible at: http://appwrite.sanad.kanousai.com"
EOF

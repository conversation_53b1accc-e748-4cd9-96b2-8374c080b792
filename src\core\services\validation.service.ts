import { Injectable } from '@nestjs/common';
import {
  validate,
  ValidationError as ClassValidatorError,
} from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ValidationError } from './error-handler.service';

@Injectable()
export class ValidationService {
  async validateDto<T extends object>(
    dtoClass: new () => T,
    data: any,
  ): Promise<T> {
    const dto = plainToClass(dtoClass, data);
    const errors = await validate(dto);

    if (errors.length > 0) {
      const errorMessage = this.formatValidationErrors(errors);
      throw new ValidationError('dto', data, errorMessage);
    }

    return dto;
  }

  validatePhoneNumber(phoneNumber: string): boolean {
    // Basic WhatsApp phone number validation
    const phoneRegex = /^whatsapp:\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  validateUserId(userId: string): boolean {
    // UUID v4 validation
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(userId);
  }

  validateTone(tone: string): boolean {
    const validTones = ['friendly', 'pro', 'witty'];
    return validTones.includes(tone);
  }

  validateSkillId(skillId: string): boolean {
    // Skill ID should be alphanumeric with dots and dashes
    const skillIdRegex = /^[a-zA-Z0-9._-]+$/;
    return skillIdRegex.test(skillId) && skillId.length <= 50;
  }

  validateMemoryContent(content: string): boolean {
    // Memory content should not be empty and not exceed 10000 characters
    return content.trim().length > 0 && content.length <= 10000;
  }

  validateTags(tags: string[]): boolean {
    if (!Array.isArray(tags)) return false;
    if (tags.length > 20) return false; // Max 20 tags

    return tags.every(
      (tag) =>
        typeof tag === 'string' &&
        tag.trim().length > 0 &&
        tag.length <= 50 &&
        /^[a-zA-Z0-9_-]+$/.test(tag),
    );
  }

  sanitizeInput(input: string): string {
    // Remove potentially harmful characters but preserve emojis and international characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  private formatValidationErrors(errors: ClassValidatorError[]): string {
    return errors
      .map((error) => {
        const constraints = error.constraints;
        if (constraints) {
          return Object.values(constraints).join(', ');
        }
        return `Validation failed for ${error.property}`;
      })
      .join('; ');
  }

  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidLanguageCode(code: string): boolean {
    // ISO 639-1 language codes
    const validCodes = [
      'en',
      'ar',
      'es',
      'fr',
      'de',
      'it',
      'pt',
      'ru',
      'ja',
      'ko',
      'zh',
      'hi',
      'tr',
      'pl',
      'nl',
      'sv',
      'da',
      'no',
      'fi',
      'he',
      'th',
      'vi',
    ];
    return validCodes.includes(code.toLowerCase());
  }

  isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }
}

import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { Request } from 'express';
import { LoggerService } from '../../core';
import { TwilioService } from '../services/twilio.service';

@Injectable()
export class WebhookValidationGuard implements CanActivate {
  constructor(
    private twilioService: TwilioService,
    private logger: LoggerService,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();

    try {
      // Validate webhook signature
      if (!this.validateSignature(request)) {
        this.logger.warn(
          `Invalid webhook signature from ${request.ip}`,
          'WebhookValidationGuard',
        );
        throw new UnauthorizedException('Invalid webhook signature');
      }

      // Validate request format
      if (!this.validateRequestFormat(request)) {
        this.logger.warn(
          `Invalid webhook request format from ${request.ip}`,
          'WebhookValidationGuard',
        );
        throw new BadRequestException('Invalid request format');
      }

      // Additional security checks
      if (!this.performSecurityChecks(request)) {
        this.logger.warn(
          `Security check failed for webhook from ${request.ip}`,
          'WebhookValidationGuard',
        );
        throw new UnauthorizedException('Security validation failed');
      }

      return true;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      this.logger.error(
        'Webhook validation error',
        error instanceof Error ? error.stack : undefined,
        'WebhookValidationGuard',
      );
      throw new UnauthorizedException('Webhook validation failed');
    }
  }

  private validateSignature(request: Request): boolean {
    try {
      const signature = request.headers['x-twilio-signature'] as string;

      if (!signature) {
        this.logger.warn(
          'Missing Twilio signature header',
          'WebhookValidationGuard',
        );
        return false;
      }

      // Get the full URL for signature validation
      const url = this.getFullUrl(request);

      // Validate signature using Twilio service
      return this.twilioService.validateWebhookSignature(
        signature,
        url,
        request.body,
      );
    } catch (error) {
      this.logger.error(
        'Signature validation error',
        error instanceof Error ? error.stack : undefined,
        'WebhookValidationGuard',
      );
      return false;
    }
  }

  private validateRequestFormat(request: Request): boolean {
    const body = request.body;

    // Check if it's a valid Twilio webhook
    if (!body || typeof body !== 'object') {
      return false;
    }

    // For WhatsApp webhooks, check required fields
    if (request.path.includes('/whatsapp')) {
      return this.validateWhatsAppWebhook(body);
    }

    // For status webhooks, check required fields
    if (request.path.includes('/status')) {
      return this.validateStatusWebhook(body);
    }

    return true;
  }

  private validateWhatsAppWebhook(body: any): boolean {
    // Required fields for WhatsApp webhook
    const requiredFields = ['From', 'To', 'MessageSid', 'AccountSid'];

    for (const field of requiredFields) {
      if (!body[field]) {
        this.logger.warn(
          `Missing required field in WhatsApp webhook: ${field}`,
          'WebhookValidationGuard',
        );
        return false;
      }
    }

    // Validate phone number format
    if (!body.From.startsWith('whatsapp:')) {
      this.logger.warn(
        `Invalid phone number format: ${body.From}`,
        'WebhookValidationGuard',
      );
      return false;
    }

    // Validate message content
    const numMedia = parseInt(body.NumMedia || '0');
    if (numMedia === 0 && (!body.Body || body.Body.trim().length === 0)) {
      this.logger.warn('Empty message with no media', 'WebhookValidationGuard');
      return false;
    }

    return true;
  }

  private validateStatusWebhook(body: any): boolean {
    // Required fields for status webhook
    const requiredFields = ['MessageSid', 'MessageStatus'];

    for (const field of requiredFields) {
      if (!body[field]) {
        this.logger.warn(
          `Missing required field in status webhook: ${field}`,
          'WebhookValidationGuard',
        );
        return false;
      }
    }

    // Validate status values
    const validStatuses = [
      'queued',
      'sending',
      'sent',
      'delivered',
      'undelivered',
      'failed',
    ];
    if (!validStatuses.includes(body.MessageStatus)) {
      this.logger.warn(
        `Invalid message status: ${body.MessageStatus}`,
        'WebhookValidationGuard',
      );
      return false;
    }

    return true;
  }

  private performSecurityChecks(request: Request): boolean {
    // Check request method
    if (request.method !== 'POST') {
      this.logger.warn(
        `Invalid request method: ${request.method}`,
        'WebhookValidationGuard',
      );
      return false;
    }

    // Check content type
    const contentType = request.headers['content-type'];
    if (
      !contentType ||
      !contentType.includes('application/x-www-form-urlencoded')
    ) {
      this.logger.warn(
        `Invalid content type: ${contentType}`,
        'WebhookValidationGuard',
      );
      return false;
    }

    // Check user agent (Twilio webhooks have specific user agent)
    const userAgent = request.headers['user-agent'];
    if (!userAgent || !userAgent.includes('TwilioProxy')) {
      this.logger.warn(
        `Suspicious user agent: ${userAgent}`,
        'WebhookValidationGuard',
      );
      // Note: This might be too strict in some environments
      // Consider making this configurable
    }

    // Rate limiting check (basic implementation)
    if (!this.checkRateLimit(request)) {
      return false;
    }

    return true;
  }

  private checkRateLimit(request: Request): boolean {
    // Basic rate limiting - in production, use Redis or similar
    const clientKey = this.getClientKey(request);

    // For now, just log suspicious activity
    // In production, implement proper rate limiting
    this.logger.debug(
      `Webhook request from client: ${clientKey}`,
      'WebhookValidationGuard',
    );

    return true;
  }

  private getClientKey(request: Request): string {
    const ip = request.ip || request.connection.remoteAddress || 'unknown';
    const userAgent = request.headers['user-agent'] || 'unknown';

    return `${ip}:${userAgent.substring(0, 50)}`;
  }

  private getFullUrl(request: Request): string {
    const protocol = request.protocol;
    const host = request.get('host');
    const originalUrl = request.originalUrl;

    return `${protocol}://${host}${originalUrl}`;
  }
}

import { Injectable } from '@nestjs/common';
import { IUser, IUserSession, IMessage } from '../../interfaces';
import { LoggerService } from '../../core';
import { v4 as uuidv4 } from 'uuid';

export interface SessionConfig {
  maxConversationHistory: number;
  sessionTimeoutMinutes: number;
  maxConcurrentSessions: number;
}

@Injectable()
export class SessionManagementService {
  private sessions = new Map<string, IUserSession>();
  private userSessions = new Map<string, Set<string>>(); // userId -> sessionIds

  private readonly config: SessionConfig = {
    maxConversationHistory: 50,
    sessionTimeoutMinutes: 60,
    maxConcurrentSessions: 3,
  };

  constructor(private logger: LoggerService) {
    // Clean up expired sessions every 10 minutes
    setInterval(() => this.cleanupExpiredSessions(), 10 * 60 * 1000);
  }

  async getOrCreateSession(user: IUser): Promise<IUserSession> {
    try {
      // Try to find an active session for the user
      const activeSession = this.findActiveSession(user.id);

      if (activeSession) {
        // Update session activity
        activeSession.expiresAt = this.calculateExpirationTime();
        this.sessions.set(activeSession.sessionId, activeSession);

        this.logger.debug(
          `Using existing session ${activeSession.sessionId} for user ${user.id}`,
          'SessionManagementService',
        );

        return activeSession;
      }

      // Create new session
      const newSession = await this.createNewSession(user);

      this.logger.log(
        `Created new session ${newSession.sessionId} for user ${user.id}`,
        'SessionManagementService',
      );

      return newSession;
    } catch (error) {
      this.logger.error(
        `Failed to get or create session for user ${user.id}`,
        error instanceof Error ? error.stack : undefined,
        'SessionManagementService',
      );
      throw error;
    }
  }

  async addMessageToSession(
    sessionId: string,
    message: IMessage,
  ): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);

      if (!session) {
        this.logger.warn(
          `Attempted to add message to non-existent session: ${sessionId}`,
          'SessionManagementService',
        );
        return;
      }

      // Add message to conversation history
      session.conversationHistory.push(message);

      // Trim history if it exceeds the limit
      if (
        session.conversationHistory.length > this.config.maxConversationHistory
      ) {
        session.conversationHistory = session.conversationHistory.slice(
          -this.config.maxConversationHistory,
        );
      }

      // Update session
      this.sessions.set(sessionId, session);

      this.logger.debug(
        `Added message ${message.id} to session ${sessionId}`,
        'SessionManagementService',
      );
    } catch (error) {
      this.logger.error(
        `Failed to add message to session ${sessionId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionManagementService',
      );
      throw error;
    }
  }

  async updateSessionContext(
    sessionId: string,
    context: Record<string, any>,
  ): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);

      if (!session) {
        this.logger.warn(
          `Attempted to update context for non-existent session: ${sessionId}`,
          'SessionManagementService',
        );
        return;
      }

      // Merge context
      session.context = { ...session.context, ...context };

      // Update session
      this.sessions.set(sessionId, session);

      this.logger.debug(
        `Updated context for session ${sessionId}`,
        'SessionManagementService',
      );
    } catch (error) {
      this.logger.error(
        `Failed to update session context ${sessionId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionManagementService',
      );
      throw error;
    }
  }

  async updateSessionState(
    sessionId: string,
    state: Record<string, any>,
  ): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);

      if (!session) {
        this.logger.warn(
          `Attempted to update state for non-existent session: ${sessionId}`,
          'SessionManagementService',
        );
        return;
      }

      // Merge state
      session.state = { ...session.state, ...state };

      // Update session
      this.sessions.set(sessionId, session);

      this.logger.debug(
        `Updated state for session ${sessionId}`,
        'SessionManagementService',
      );
    } catch (error) {
      this.logger.error(
        `Failed to update session state ${sessionId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionManagementService',
      );
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<IUserSession | undefined> {
    const session = this.sessions.get(sessionId);

    if (session && this.isSessionExpired(session)) {
      await this.endSession(sessionId);
      return undefined;
    }

    return session;
  }

  async endSession(sessionId: string): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);

      if (session) {
        // Remove from user sessions
        const userSessionSet = this.userSessions.get(session.userId);
        if (userSessionSet) {
          userSessionSet.delete(sessionId);
          if (userSessionSet.size === 0) {
            this.userSessions.delete(session.userId);
          }
        }

        // Remove session
        this.sessions.delete(sessionId);

        this.logger.log(
          `Ended session ${sessionId} for user ${session.userId}`,
          'SessionManagementService',
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to end session ${sessionId}`,
        error instanceof Error ? error.stack : undefined,
        'SessionManagementService',
      );
      throw error;
    }
  }

  async getUserSessions(userId: string): Promise<IUserSession[]> {
    const sessionIds = this.userSessions.get(userId) || new Set();
    const sessions: IUserSession[] = [];

    for (const sessionId of sessionIds) {
      const session = await this.getSession(sessionId);
      if (session) {
        sessions.push(session);
      }
    }

    return sessions;
  }

  getSessionStatistics() {
    const totalSessions = this.sessions.size;
    const totalUsers = this.userSessions.size;
    const expiredSessions = Array.from(this.sessions.values()).filter(
      (session) => this.isSessionExpired(session),
    ).length;

    return {
      totalSessions,
      totalUsers,
      expiredSessions,
      activeSessions: totalSessions - expiredSessions,
    };
  }

  private findActiveSession(userId: string): IUserSession | undefined {
    const sessionIds = this.userSessions.get(userId);

    if (!sessionIds) {
      return undefined;
    }

    // Find the most recent active session
    let mostRecentSession: IUserSession | undefined;
    let mostRecentTime = 0;

    for (const sessionId of sessionIds) {
      const session = this.sessions.get(sessionId);

      if (session && !this.isSessionExpired(session)) {
        const sessionTime = session.createdAt.getTime();
        if (sessionTime > mostRecentTime) {
          mostRecentTime = sessionTime;
          mostRecentSession = session;
        }
      }
    }

    return mostRecentSession;
  }

  private async createNewSession(user: IUser): Promise<IUserSession> {
    const sessionId = uuidv4();

    // Clean up old sessions if user has too many
    await this.cleanupUserSessions(user.id);

    const session: IUserSession = {
      userId: user.id,
      sessionId,
      context: {},
      state: {},
      conversationHistory: [],
      createdAt: new Date(),
      expiresAt: this.calculateExpirationTime(),
    };

    // Store session
    this.sessions.set(sessionId, session);

    // Add to user sessions
    let userSessionSet = this.userSessions.get(user.id);
    if (!userSessionSet) {
      userSessionSet = new Set();
      this.userSessions.set(user.id, userSessionSet);
    }
    userSessionSet.add(sessionId);

    return session;
  }

  private async cleanupUserSessions(userId: string): Promise<void> {
    const sessionIds = this.userSessions.get(userId);

    if (!sessionIds || sessionIds.size < this.config.maxConcurrentSessions) {
      return;
    }

    // Get all sessions for the user
    const userSessions: Array<{ sessionId: string; session: IUserSession }> =
      [];

    for (const sessionId of sessionIds) {
      const session = this.sessions.get(sessionId);
      if (session) {
        userSessions.push({ sessionId, session });
      }
    }

    // Sort by creation time (oldest first)
    userSessions.sort(
      (a, b) => a.session.createdAt.getTime() - b.session.createdAt.getTime(),
    );

    // Remove oldest sessions
    const sessionsToRemove =
      userSessions.length - this.config.maxConcurrentSessions + 1;

    for (let i = 0; i < sessionsToRemove; i++) {
      await this.endSession(userSessions[i].sessionId);
    }
  }

  private cleanupExpiredSessions(): void {
    const expiredSessionIds: string[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      if (this.isSessionExpired(session)) {
        expiredSessionIds.push(sessionId);
      }
    }

    // Remove expired sessions
    expiredSessionIds.forEach((sessionId) => {
      this.endSession(sessionId).catch((error) => {
        this.logger.error(
          `Failed to cleanup expired session ${sessionId}`,
          error instanceof Error ? error.stack : undefined,
          'SessionManagementService',
        );
      });
    });

    if (expiredSessionIds.length > 0) {
      this.logger.log(
        `Cleaned up ${expiredSessionIds.length} expired sessions`,
        'SessionManagementService',
      );
    }
  }

  private isSessionExpired(session: IUserSession): boolean {
    return new Date() > session.expiresAt;
  }

  private calculateExpirationTime(): Date {
    return new Date(Date.now() + this.config.sessionTimeoutMinutes * 60 * 1000);
  }
}

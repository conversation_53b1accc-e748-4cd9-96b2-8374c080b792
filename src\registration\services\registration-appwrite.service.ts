import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { Query } from 'node-appwrite';
import { AppwriteService } from '../../appwrite/appwrite.service';
import { EmailService } from './email.service';
import { WhitelistService } from './whitelist.service';
import {
  IRegistrationRequest,
  IEmailConfirmation,
  CreateRegistrationRequestDto,
  RegistrationStatus,
} from '../../interfaces/registration.interface';

@Injectable()
export class RegistrationAppwriteService {
  private readonly logger = new Logger(RegistrationAppwriteService.name);

  constructor(
    private readonly appwriteService: AppwriteService,
    private readonly emailService: EmailService,
    private readonly whitelistService: WhitelistService,
  ) {}

  async createRegistrationRequest(
    dto: CreateRegistrationRequestDto,
  ): Promise<IRegistrationRequest> {
    try {
      const id = uuidv4();
      const confirmationToken = uuidv4();
      const now = new Date();

      const registrationRequest: IRegistrationRequest = {
        id,
        email: dto.email.toLowerCase().trim(),
        firstName: dto.firstName.trim(),
        lastName: dto.lastName.trim(),
        phoneNumber: dto.phoneNumber?.trim(),
        reason: dto.reason?.trim(),
        status: RegistrationStatus.PENDING,
        confirmationToken,
        emailConfirmed: false,
        isEarlyAdopter: false,
        createdAt: now,
        updatedAt: now,
      };

      // Store in Appwrite database
      await this.appwriteService.createDocument(
        this.appwriteService.getCollectionId('registrations'),
        id,
        {
          ...registrationRequest,
          createdAt: now.toISOString(),
          updatedAt: now.toISOString(),
        },
      );

      // Create email confirmation record
      const emailConfirmation: IEmailConfirmation = {
        id: uuidv4(),
        email: registrationRequest.email,
        token: confirmationToken,
        registrationRequestId: id,
        expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000), // 24 hours
        createdAt: now,
      };

      await this.appwriteService.createDocument(
        this.appwriteService.getCollectionId('emailConfirmations'),
        emailConfirmation.id,
        {
          ...emailConfirmation,
          createdAt: now.toISOString(),
          expiresAt: emailConfirmation.expiresAt.toISOString(),
        },
      );

      this.logger.log(`Created registration request: ${id} for ${dto.email}`);
      return registrationRequest;
    } catch (error) {
      this.logger.error(
        `Failed to create registration request for ${dto.email}`,
        error instanceof Error ? error.stack : undefined,
        'RegistrationAppwriteService',
      );
      throw error;
    }
  }

  async findByEmail(email: string): Promise<IRegistrationRequest | null> {
    try {
      const result = await this.appwriteService.listDocuments(
        this.appwriteService.getCollectionId('registrations'),
        [Query.equal('email', email.toLowerCase().trim())],
      );

      if (result.documents.length === 0) {
        return null;
      }

      const doc = result.documents[0];
      return this.mapDocumentToRegistrationRequest(doc);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to find registration by email: ${errorMessage}`,
      );
      return null;
    }
  }

  async findById(id: string): Promise<IRegistrationRequest | null> {
    try {
      const doc = await this.appwriteService.getDocument(
        this.appwriteService.getCollectionId('registrations'),
        id,
      );
      return this.mapDocumentToRegistrationRequest(doc);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to find registration by ID: ${errorMessage}`);
      return null;
    }
  }

  async confirmEmail(token: string): Promise<{
    success: boolean;
    message?: string;
    registrationRequest?: IRegistrationRequest;
  }> {
    try {
      // Find email confirmation by token
      const confirmationResult = await this.appwriteService.listDocuments(
        'email_confirmations',
        [Query.equal('token', token)],
      );

      if (confirmationResult.documents.length === 0) {
        return { success: false, message: 'Invalid confirmation token' };
      }

      const emailConfirmation = confirmationResult.documents[0];

      if (emailConfirmation.confirmedAt) {
        return { success: false, message: 'Email has already been confirmed' };
      }

      if (new Date() > new Date(emailConfirmation.expiresAt)) {
        return { success: false, message: 'Confirmation token has expired' };
      }

      // Update email confirmation
      await this.appwriteService.updateDocument(
        'email_confirmations',
        emailConfirmation.$id,
        {
          confirmedAt: new Date().toISOString(),
        },
      );

      // Update registration request
      const registrationRequest = await this.findById(
        emailConfirmation.registrationRequestId,
      );
      if (!registrationRequest) {
        return { success: false, message: 'Registration request not found' };
      }

      const updatedRequest = {
        ...registrationRequest,
        emailConfirmed: true,
        emailConfirmedAt: new Date(),
        status: RegistrationStatus.EMAIL_CONFIRMED,
        updatedAt: new Date(),
      };

      await this.appwriteService.updateDocument(
        this.appwriteService.getCollectionId('registrations'),
        registrationRequest.id,
        {
          emailConfirmed: true,
          emailConfirmedAt: new Date().toISOString(),
          status: RegistrationStatus.EMAIL_CONFIRMED,
          updatedAt: new Date().toISOString(),
        },
      );

      // Add to whitelist
      await this.whitelistService.addToWhitelist(updatedRequest);

      this.logger.log(`Email confirmed for: ${registrationRequest.email}`);

      return { success: true, registrationRequest: updatedRequest };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to confirm email: ${errorMessage}`);
      return {
        success: false,
        message: 'An error occurred while confirming email',
      };
    }
  }

  async selectAsEarlyAdopter(
    registrationRequestId: string,
    adminUserId: string,
  ): Promise<{
    success: boolean;
    message?: string;
    registrationRequest?: IRegistrationRequest;
  }> {
    try {
      const registrationRequest = await this.findById(registrationRequestId);

      if (!registrationRequest) {
        return { success: false, message: 'Registration request not found' };
      }

      if (!registrationRequest.emailConfirmed) {
        return {
          success: false,
          message: 'Email must be confirmed before selecting as early adopter',
        };
      }

      if (registrationRequest.isEarlyAdopter) {
        return { success: false, message: 'User is already an early adopter' };
      }

      // Update registration request
      const updatedData = {
        isEarlyAdopter: true,
        earlyAdopterSelectedAt: new Date().toISOString(),
        earlyAdopterSelectedBy: adminUserId,
        status: RegistrationStatus.EARLY_ADOPTER,
        updatedAt: new Date().toISOString(),
      };

      await this.appwriteService.updateDocument(
        this.appwriteService.getCollectionId('registrations'),
        registrationRequestId,
        updatedData,
      );

      const updatedRequest = {
        ...registrationRequest,
        ...updatedData,
        earlyAdopterSelectedAt: new Date(updatedData.earlyAdopterSelectedAt),
        updatedAt: new Date(updatedData.updatedAt),
      };

      // Update whitelist
      await this.whitelistService.markAsEarlyAdopter(
        registrationRequest.email,
        adminUserId,
      );

      // Send early adopter notification email
      await this.emailService.sendEarlyAdopterNotification(
        registrationRequest.email,
        registrationRequest.firstName,
      );

      this.logger.log(
        `Selected early adopter: ${registrationRequest.email} by admin: ${adminUserId}`,
      );

      return { success: true, registrationRequest: updatedRequest };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to select early adopter: ${errorMessage}`);
      return {
        success: false,
        message: 'An error occurred while selecting early adopter',
      };
    }
  }

  async getAllRegistrationRequests(): Promise<IRegistrationRequest[]> {
    try {
      const result = await this.appwriteService.listDocuments(
        this.appwriteService.getCollectionId('registrations'),
      );
      return result.documents.map((doc) =>
        this.mapDocumentToRegistrationRequest(doc),
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to get all registrations: ${errorMessage}`);
      return [];
    }
  }

  async getRegistrationsByStatus(
    status: RegistrationStatus,
  ): Promise<IRegistrationRequest[]> {
    try {
      const result = await this.appwriteService.listDocuments(
        this.appwriteService.getCollectionId('registrations'),
        [Query.equal('status', status)],
      );
      return result.documents.map((doc) =>
        this.mapDocumentToRegistrationRequest(doc),
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to get registrations by status: ${errorMessage}`,
      );
      return [];
    }
  }

  private mapDocumentToRegistrationRequest(doc: any): IRegistrationRequest {
    return {
      id: doc.$id,
      email: doc.email,
      firstName: doc.firstName,
      lastName: doc.lastName,
      phoneNumber: doc.phoneNumber,
      reason: doc.reason,
      status: doc.status,
      confirmationToken: doc.confirmationToken,
      emailConfirmed: doc.emailConfirmed,
      emailConfirmedAt: doc.emailConfirmedAt
        ? new Date(doc.emailConfirmedAt)
        : undefined,
      isEarlyAdopter: doc.isEarlyAdopter,
      earlyAdopterSelectedAt: doc.earlyAdopterSelectedAt
        ? new Date(doc.earlyAdopterSelectedAt)
        : undefined,
      earlyAdopterSelectedBy: doc.earlyAdopterSelectedBy,
      createdAt: new Date(doc.createdAt),
      updatedAt: new Date(doc.updatedAt),
    };
  }
}

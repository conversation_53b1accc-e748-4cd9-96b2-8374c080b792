import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppwriteService } from '../../appwrite/appwrite.service';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly appwriteService: AppwriteService,
  ) {}

  async sendConfirmationEmail(
    email: string,
    firstName: string,
    confirmationUrl: string,
  ): Promise<void> {
    const emailProvider = this.configService.get<string>(
      'EMAIL_SERVICE_PROVIDER',
    );

    if (emailProvider === 'appwrite') {
      await this.sendWithAppwrite(
        email,
        'Confirm your Sanad registration',
        this.getConfirmationEmailTemplate(firstName, confirmationUrl),
      );
    } else if (!emailProvider || emailProvider === 'sendgrid') {
      await this.sendWithSendGrid(
        email,
        'Confirm your Sanad registration',
        this.getConfirmationEmailTemplate(firstName, confirmationUrl),
      );
    } else {
      // Fallback: just log the email
      this.logger.log(`Would send confirmation email to: ${email}`);
      this.logger.log(`Confirmation URL: ${confirmationUrl}`);
    }
  }

  async sendEarlyAdopterNotification(
    email: string,
    firstName: string,
  ): Promise<void> {
    const emailProvider = this.configService.get<string>(
      'EMAIL_SERVICE_PROVIDER',
    );

    if (emailProvider === 'appwrite') {
      await this.sendWithAppwrite(
        email,
        'Welcome to Sanad Early Access!',
        this.getEarlyAdopterEmailTemplate(firstName),
      );
    } else if (!emailProvider || emailProvider === 'sendgrid') {
      await this.sendWithSendGrid(
        email,
        'Welcome to Sanad Early Access!',
        this.getEarlyAdopterEmailTemplate(firstName),
      );
    } else {
      // Fallback: just log the email
      this.logger.log(`Would send early adopter notification to: ${email}`);
    }
  }

  private async sendWithSendGrid(
    to: string,
    subject: string,
    htmlContent: string,
  ): Promise<void> {
    const apiKey = this.configService.get<string>('SENDGRID_API_KEY');

    if (!apiKey) {
      this.logger.warn(
        'SendGrid API key not configured, logging email instead',
      );
      this.logger.log(`To: ${to}`);
      this.logger.log(`Subject: ${subject}`);
      this.logger.log(`Content: ${htmlContent}`);
      return;
    }

    try {
      // In a real implementation, you would use the SendGrid SDK here
      // For now, we'll just log the email details
      this.logger.log(`Sending email via SendGrid to: ${to}`);
      this.logger.log(`Subject: ${subject}`);

      // TODO: Implement actual SendGrid integration
      // const sgMail = require('@sendgrid/mail');
      // sgMail.setApiKey(apiKey);
      // await sgMail.send({
      //   to,
      //   from: fromEmail,
      //   subject,
      //   html: htmlContent,
      // });
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}:`, error);
      throw error;
    }
  }

  private async sendWithAppwrite(
    to: string,
    subject: string,
    htmlContent: string,
  ): Promise<void> {
    try {
      // Appwrite doesn't have a direct email service in the current version
      // We'll use a workaround by storing the email in a collection and
      // potentially using Appwrite Functions to send emails

      const fromEmail = this.configService.get<string>(
        'FROM_EMAIL',
        '<EMAIL>',
      );

      // Store email in Appwrite database for processing
      const emailRecord = {
        to,
        from: fromEmail,
        subject,
        htmlContent,
        status: 'pending',
        createdAt: new Date().toISOString(),
        attempts: 0,
      };

      // Store in emails collection (you'll need to create this collection in Appwrite)
      await this.appwriteService.createDocument(
        'emails', // Collection ID for emails
        this.appwriteService.generateId(),
        emailRecord,
      );

      this.logger.log(`Email queued in Appwrite for: ${to}`);
      this.logger.log(`Subject: ${subject}`);

      // For immediate functionality, also log the email content
      this.logger.log(`Email content stored in Appwrite database`);

      // TODO: Implement Appwrite Function to process emails from the queue
      // The function would read from the emails collection and send via SMTP or email service
    } catch (error) {
      this.logger.error(`Failed to queue email in Appwrite for ${to}:`, error);

      // Fallback: log the email details
      this.logger.warn(`Falling back to logging email for: ${to}`);
      this.logger.log(`Subject: ${subject}`);
      this.logger.log(`Content: ${htmlContent.substring(0, 200)}...`);
    }
  }

  private getConfirmationEmailTemplate(
    firstName: string,
    confirmationUrl: string,
  ): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm your Sanad registration</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 2.5em; font-weight: 300; }
        .header p { color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 1.1em; }
        .content { padding: 40px 20px; }
        .content h2 { color: #333; margin-bottom: 20px; }
        .content p { color: #666; line-height: 1.6; margin-bottom: 20px; }
        .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 20px 0; }
        .button:hover { transform: translateY(-2px); }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
        .footer a { color: #667eea; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sanad</h1>
            <p>Your Personal AI Manager</p>
        </div>
        
        <div class="content">
            <h2>Hi ${firstName}!</h2>
            
            <p>Thank you for your interest in Sanad! We're excited to have you join our community of early users who want to experience the future of personal AI assistance.</p>
            
            <p>To complete your registration and join our whitelist, please confirm your email address by clicking the button below:</p>
            
            <a href="${confirmationUrl}" class="button">Confirm Email Address</a>
            
            <p>Once confirmed, you'll be added to our whitelist and we'll notify you as soon as early access becomes available.</p>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #667eea;">${confirmationUrl}</p>
            
            <p>This confirmation link will expire in 24 hours.</p>
            
            <p>Best regards,<br>The Sanad Team</p>
        </div>
        
        <div class="footer">
            <p>If you didn't request this email, you can safely ignore it.</p>
            <p>Questions? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>
    `;
  }

  private getEarlyAdopterEmailTemplate(firstName: string): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Sanad Early Access!</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 2.5em; font-weight: 300; }
        .header p { color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 1.1em; }
        .content { padding: 40px 20px; }
        .content h2 { color: #333; margin-bottom: 20px; }
        .content p { color: #666; line-height: 1.6; margin-bottom: 20px; }
        .highlight { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
        .footer a { color: #667eea; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Sanad</h1>
            <p>Your Personal AI Manager</p>
        </div>
        
        <div class="content">
            <h2>🎉 Congratulations, ${firstName}!</h2>
            
            <p>You've been selected for early access to Sanad! We're thrilled to have you as one of our first users.</p>
            
            <div class="highlight">
                <h3 style="margin-top: 0; color: white;">What's Next?</h3>
                <p style="margin-bottom: 0; color: rgba(255,255,255,0.9);">We'll be in touch soon with your WhatsApp setup instructions and exclusive early access details. Get ready to experience your personal AI manager!</p>
            </div>
            
            <p>As an early adopter, you'll get:</p>
            <ul>
                <li>First access to all new features</li>
                <li>Direct line to our development team for feedback</li>
                <li>Exclusive early adopter benefits</li>
                <li>Priority support</li>
            </ul>
            
            <p>We can't wait to see how Sanad transforms your daily productivity and helps you stay organized!</p>
            
            <p>Welcome to the future of personal AI assistance!</p>
            
            <p>Best regards,<br>The Sanad Team</p>
        </div>
        
        <div class="footer">
            <p>Questions? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>
    `;
  }
}

name: sanad-paim
region: blr1

services:
  - name: api
    source_dir: /
    github:
      repo: HDickenson/sanad
      branch: master
      deploy_on_push: true
    
    # Build configuration
    build_command: npm run build
    run_command: node dist/main
    
    # Environment configuration
    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs
    
    # Health check configuration
    health_check:
      http_path: /health
      initial_delay_seconds: 30
      period_seconds: 10
      timeout_seconds: 5
      success_threshold: 1
      failure_threshold: 3
    
    # HTTP configuration
    http_port: 3000
    
    # Environment variables
    envs:
      # Application Configuration
      - key: NODE_ENV
        value: production
      - key: PORT
        value: "3000"
      
      # OpenAI Configuration
      - key: OPENAI_API_KEY
        value: ${OPENAI_API_KEY}
      - key: OPENAI_MODEL
        value: gpt-4
      - key: OPENAI_MAX_TOKENS
        value: "1000"
      - key: OPENAI_TEMPERATURE
        value: "0.7"
      
      # Twilio WhatsApp Configuration
      - key: TWILIO_ACCOUNT_SID
        value: ${TWILIO_ACCOUNT_SID}
      - key: TWILIO_AUTH_TOKEN
        value: ${TWILIO_AUTH_TOKEN}
      - key: TWILIO_WHATSAPP_NUMBER
        value: ${TWILIO_WHATSAPP_NUMBER}
      - key: TWILIO_WEBHOOK_URL
        value: ${TWILIO_WEBHOOK_URL}
      
      # Security Configuration
      - key: JWT_SECRET
        value: ${JWT_SECRET}
      - key: ENCRYPTION_KEY
        value: ${ENCRYPTION_KEY}
      
      # Storage Configuration
      - key: STORAGE_PROVIDER
        value: digitalocean
      - key: STORAGE_PATH
        value: ./storage
      - key: MAX_FILE_SIZE
        value: "********"
      
      # Digital Ocean Spaces Configuration
      - key: DO_SPACES_ACCESS_KEY_ID
        value: ${DO_SPACES_ACCESS_KEY_ID}
      - key: DO_SPACES_SECRET_ACCESS_KEY
        value: ${DO_SPACES_SECRET_ACCESS_KEY}
      - key: DO_SPACES_ENDPOINT
        value: ${DO_SPACES_ENDPOINT}
      - key: DO_SPACES_REGION
        value: ${DO_SPACES_REGION}
      - key: DO_SPACES_BUCKET
        value: ${DO_SPACES_BUCKET}
      - key: DO_SPACES_CDN_ENDPOINT
        value: ${DO_SPACES_CDN_ENDPOINT}
      
      # Email & Registration Configuration (Using Appwrite)
      - key: EMAIL_SERVICE_PROVIDER
        value: appwrite
      - key: FROM_EMAIL
        value: ${FROM_EMAIL}
      - key: ADMIN_EMAIL
        value: ${ADMIN_EMAIL}
      - key: REGISTRATION_ENABLED
        value: "true"
      - key: REQUIRE_EMAIL_CONFIRMATION
        value: "true"
      - key: AUTO_APPROVE_WHITELIST
        value: "false"
      - key: ADMIN_API_KEY
        value: ${ADMIN_API_KEY}
      
      # Rate Limiting Configuration
      - key: THROTTLE_TTL
        value: "60"
      - key: THROTTLE_LIMIT
        value: "10"
      
      # CORS Configuration
      - key: CORS_ORIGIN
        value: "*"
      
      # Logging Configuration
      - key: LOG_LEVEL
        value: info
      - key: LOG_FILE_PATH
        value: /var/log/paim
      - key: DETAILED_ERRORS
        value: "false"
      
      # Health Check Configuration
      - key: HEALTH_CHECK_ENABLED
        value: "true"
      - key: HEALTH_CHECK_TIMEOUT
        value: "5000"
      
      # Feature Flags
      - key: FEATURE_VOICE_ENABLED
        value: "true"
      - key: FEATURE_MEDIA_ENABLED
        value: "true"
      - key: FEATURE_REMINDERS_ENABLED
        value: "true"
      - key: FEATURE_ANALYTICS_ENABLED
        value: "true"
      
      # Appwrite Configuration (Self-Hosted)
      - key: APPWRITE_ENDPOINT
        value: https://appwrite.sanad.kanousai.com/v1
      - key: APPWRITE_PROJECT_ID
        value: ${APPWRITE_PROJECT_ID}
      - key: APPWRITE_API_KEY
        value: ${APPWRITE_API_KEY}
      - key: APPWRITE_DATABASE_ID
        value: ${APPWRITE_DATABASE_ID}
      - key: APPWRITE_STORAGE_BUCKET_ID
        value: ${APPWRITE_STORAGE_BUCKET_ID}

      # Appwrite Collection IDs
      - key: APPWRITE_USERS_COLLECTION_ID
        value: ${APPWRITE_USERS_COLLECTION_ID}
      - key: APPWRITE_REGISTRATIONS_COLLECTION_ID
        value: ${APPWRITE_REGISTRATIONS_COLLECTION_ID}
      - key: APPWRITE_WHITELIST_COLLECTION_ID
        value: ${APPWRITE_WHITELIST_COLLECTION_ID}
      - key: APPWRITE_SESSIONS_COLLECTION_ID
        value: ${APPWRITE_SESSIONS_COLLECTION_ID}
      - key: APPWRITE_MEMORIES_COLLECTION_ID
        value: ${APPWRITE_MEMORIES_COLLECTION_ID}
      - key: APPWRITE_REMINDERS_COLLECTION_ID
        value: ${APPWRITE_REMINDERS_COLLECTION_ID}
      - key: APPWRITE_CONVERSATIONS_COLLECTION_ID
        value: ${APPWRITE_CONVERSATIONS_COLLECTION_ID}
      - key: APPWRITE_EMAILS_COLLECTION_ID
        value: ${APPWRITE_EMAILS_COLLECTION_ID}
      - key: APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID
        value: ${APPWRITE_EMAIL_CONFIRMATIONS_COLLECTION_ID}

# Optional: Static site for documentation or admin interface
# static_sites:
#   - name: admin-dashboard
#     source_dir: /admin
#     build_command: npm run build:admin
#     output_dir: /admin/dist
#     routes:
#       - path: /admin

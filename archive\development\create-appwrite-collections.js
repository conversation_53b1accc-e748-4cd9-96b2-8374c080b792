#!/usr/bin/env node

/**
 * Create Appwrite Collections Script
 */

const { Client, Databases } = require('node-appwrite');
const fs = require('fs');

// Load environment variables
const envContent = fs.readFileSync('.env.digitalocean', 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  line = line.trim();
  if (line && !line.startsWith('#') && line.includes('=')) {
    const [key, ...valueParts] = line.split('=');
    envVars[key] = valueParts.join('=');
  }
});

const client = new Client();
const databases = new Databases(client);

client
  .setEndpoint(envVars.APPWRITE_ENDPOINT)
  .setProject(envVars.APPWRITE_PROJECT_ID)
  .setKey(envVars.APPWRITE_API_KEY);

const databaseId = envVars.APPWRITE_DATABASE_ID;

// Collection definitions
const collections = [
  {
    id: 'users',
    name: 'Users',
    attributes: [
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'name', type: 'string', size: 100, required: true },
      { key: 'tone', type: 'string', size: 50, required: false, default: 'friendly' },
      { key: 'isOnboarded', type: 'boolean', required: false, default: false },
      { key: 'isWhitelisted', type: 'boolean', required: false, default: false },
      { key: 'isEarlyAdopter', type: 'boolean', required: false, default: false },
      { key: 'registrationSource', type: 'string', size: 20, required: false, default: 'whatsapp' },
      { key: 'preferences', type: 'string', size: 2000, required: false },
      { key: 'lastActiveAt', type: 'datetime', required: false }
    ],
    indexes: [
      { key: 'phoneNumber', type: 'unique', attributes: ['phoneNumber'] },
      { key: 'isWhitelisted', type: 'key', attributes: ['isWhitelisted'] },
      { key: 'isEarlyAdopter', type: 'key', attributes: ['isEarlyAdopter'] }
    ]
  },
  {
    id: 'registrations',
    name: 'Registrations',
    attributes: [
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'name', type: 'string', size: 100, required: true },
      { key: 'phoneNumber', type: 'string', size: 20, required: false },
      { key: 'status', type: 'string', size: 20, required: false, default: 'pending' },
      { key: 'isEarlyAdopterSelected', type: 'boolean', required: false, default: false },
      { key: 'emailConfirmed', type: 'boolean', required: false, default: false },
      { key: 'submittedAt', type: 'datetime', required: true },
      { key: 'notes', type: 'string', size: 500, required: false }
    ],
    indexes: [
      { key: 'email', type: 'unique', attributes: ['email'] },
      { key: 'status', type: 'key', attributes: ['status'] },
      { key: 'emailConfirmed', type: 'key', attributes: ['emailConfirmed'] }
    ]
  },
  {
    id: 'whitelist',
    name: 'Whitelist',
    attributes: [
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'email', type: 'string', size: 255, required: false },
      { key: 'name', type: 'string', size: 100, required: false },
      { key: 'isActive', type: 'boolean', required: false, default: true },
      { key: 'addedBy', type: 'string', size: 50, required: false, default: 'admin' },
      { key: 'addedAt', type: 'datetime', required: true },
      { key: 'notes', type: 'string', size: 500, required: false }
    ],
    indexes: [
      { key: 'phoneNumber', type: 'unique', attributes: ['phoneNumber'] },
      { key: 'email', type: 'key', attributes: ['email'] },
      { key: 'isActive', type: 'key', attributes: ['isActive'] }
    ]
  },
  {
    id: 'sessions',
    name: 'Sessions',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'sessionId', type: 'string', size: 100, required: true },
      { key: 'isActive', type: 'boolean', required: false, default: true },
      { key: 'startedAt', type: 'datetime', required: true },
      { key: 'lastMessageAt', type: 'datetime', required: false },
      { key: 'messageCount', type: 'integer', required: false, default: 0 },
      { key: 'context', type: 'string', size: 5000, required: false }
    ],
    indexes: [
      { key: 'userId', type: 'key', attributes: ['userId'] },
      { key: 'sessionId', type: 'unique', attributes: ['sessionId'] },
      { key: 'isActive', type: 'key', attributes: ['isActive'] }
    ]
  },
  {
    id: 'memories',
    name: 'Memories',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'content', type: 'string', size: 5000, required: true },
      { key: 'type', type: 'string', size: 50, required: false, default: 'general' },
      { key: 'importance', type: 'integer', required: false, default: 1 },
      { key: 'tags', type: 'string', size: 500, required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'lastAccessedAt', type: 'datetime', required: false }
    ],
    indexes: [
      { key: 'userId', type: 'key', attributes: ['userId'] },
      { key: 'type', type: 'key', attributes: ['type'] },
      { key: 'importance', type: 'key', attributes: ['importance'] }
    ]
  },
  {
    id: 'reminders',
    name: 'Reminders',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'phoneNumber', type: 'string', size: 20, required: true },
      { key: 'title', type: 'string', size: 200, required: true },
      { key: 'description', type: 'string', size: 1000, required: false },
      { key: 'scheduledFor', type: 'datetime', required: true },
      { key: 'isCompleted', type: 'boolean', required: false, default: false },
      { key: 'isSent', type: 'boolean', required: false, default: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'completedAt', type: 'datetime', required: false }
    ],
    indexes: [
      { key: 'userId', type: 'key', attributes: ['userId'] },
      { key: 'scheduledFor', type: 'key', attributes: ['scheduledFor'] },
      { key: 'isCompleted', type: 'key', attributes: ['isCompleted'] }
    ]
  },
  {
    id: 'conversations',
    name: 'Conversations',
    attributes: [
      { key: 'userId', type: 'string', size: 50, required: true },
      { key: 'sessionId', type: 'string', size: 100, required: true },
      { key: 'messageId', type: 'string', size: 100, required: true },
      { key: 'role', type: 'string', size: 20, required: true },
      { key: 'content', type: 'string', size: 5000, required: true },
      { key: 'timestamp', type: 'datetime', required: true },
      { key: 'messageType', type: 'string', size: 50, required: false, default: 'text' },
      { key: 'metadata', type: 'string', size: 1000, required: false }
    ],
    indexes: [
      { key: 'userId', type: 'key', attributes: ['userId'] },
      { key: 'sessionId', type: 'key', attributes: ['sessionId'] },
      { key: 'timestamp', type: 'key', attributes: ['timestamp'] }
    ]
  },
  {
    id: 'emails',
    name: 'Emails',
    attributes: [
      { key: 'to', type: 'string', size: 255, required: true },
      { key: 'from', type: 'string', size: 255, required: true },
      { key: 'subject', type: 'string', size: 500, required: true },
      { key: 'body', type: 'string', size: 10000, required: true },
      { key: 'type', type: 'string', size: 50, required: true },
      { key: 'status', type: 'string', size: 20, required: false, default: 'pending' },
      { key: 'sentAt', type: 'datetime', required: false },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'errorMessage', type: 'string', size: 1000, required: false }
    ],
    indexes: [
      { key: 'to', type: 'key', attributes: ['to'] },
      { key: 'type', type: 'key', attributes: ['type'] },
      { key: 'status', type: 'key', attributes: ['status'] }
    ]
  },
  {
    id: 'email_confirmations',
    name: 'Email Confirmations',
    attributes: [
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'token', type: 'string', size: 255, required: true },
      { key: 'isUsed', type: 'boolean', required: false, default: false },
      { key: 'expiresAt', type: 'datetime', required: true },
      { key: 'createdAt', type: 'datetime', required: true },
      { key: 'usedAt', type: 'datetime', required: false }
    ],
    indexes: [
      { key: 'token', type: 'unique', attributes: ['token'] },
      { key: 'email', type: 'key', attributes: ['email'] },
      { key: 'expiresAt', type: 'key', attributes: ['expiresAt'] }
    ]
  }
];

async function createCollections() {
  console.log('🏗️ Creating Appwrite Collections...\n');
  
  try {
    for (const collection of collections) {
      console.log(`📊 Creating collection: ${collection.name} (${collection.id})`);
      
      // Create collection
      const createdCollection = await databases.createCollection(
        databaseId,
        collection.id,
        collection.name
      );
      
      console.log(`✅ Collection created: ${createdCollection.name}`);
      
      // Add attributes
      for (const attr of collection.attributes) {
        console.log(`   Adding attribute: ${attr.key}`);
        
        if (attr.type === 'string') {
          await databases.createStringAttribute(
            databaseId,
            collection.id,
            attr.key,
            attr.size,
            attr.required,
            attr.default
          );
        } else if (attr.type === 'boolean') {
          await databases.createBooleanAttribute(
            databaseId,
            collection.id,
            attr.key,
            attr.required,
            attr.default
          );
        } else if (attr.type === 'datetime') {
          await databases.createDatetimeAttribute(
            databaseId,
            collection.id,
            attr.key,
            attr.required,
            attr.default
          );
        } else if (attr.type === 'integer') {
          await databases.createIntegerAttribute(
            databaseId,
            collection.id,
            attr.key,
            attr.required,
            attr.min,
            attr.max,
            attr.default
          );
        }
        
        // Wait a bit between attribute creation
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      console.log(`✅ All attributes added for ${collection.name}\n`);
    }
    
    console.log('🎉 All collections created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating collections:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code || 'Unknown'}`);
    
    if (error.code === 409) {
      console.log('💡 Collection might already exist, continuing...');
    } else {
      process.exit(1);
    }
  }
}

createCollections();

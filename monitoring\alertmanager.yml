global:
  # Global configuration
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

# Templates for notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route tree for alerts
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
      
    # Warning alerts - grouped notifications
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 30m
      
    # Info alerts - daily digest
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 5m
      repeat_interval: 24h

# Inhibition rules
inhibit_rules:
  # Inhibit warning alerts if critical alert is firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']

# Notification receivers
receivers:
  # Default webhook receiver
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://localhost:5001/webhook'
        send_resolved: true
        http_config:
          basic_auth:
            username: 'webhook_user'
            password: 'webhook_password'

  # Critical alerts - multiple channels
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          Started: {{ .StartsAt }}
          {{ end }}
        headers:
          Priority: 'high'
    
    webhook_configs:
      - url: 'http://localhost:5001/webhook/critical'
        send_resolved: true
        title: '🚨 Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          **Alert:** {{ .Annotations.summary }}
          **Description:** {{ .Annotations.description }}
          **Severity:** {{ .Labels.severity }}
          **Started:** {{ .StartsAt }}
          {{ end }}

  # Warning alerts
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ WARNING: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          Started: {{ .StartsAt }}
          {{ end }}
    
    webhook_configs:
      - url: 'http://localhost:5001/webhook/warning'
        send_resolved: true

  # Info alerts
  - name: 'info-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: 'ℹ️ INFO: Daily Alert Digest'
        body: |
          Daily alert summary:
          {{ range .Alerts }}
          - {{ .Annotations.summary }}
          {{ end }}

# Silence configuration
# Silences can be configured via the AlertManager web UI or API

#!/usr/bin/env node

/**
 * Debug server for PAIM - with extensive logging
 */

const http = require('http');
const PORT = process.env.PORT || 3000;

console.log('🚀 Starting PAIM Debug Server...');
console.log(`📍 Port: ${PORT}`);
console.log(`🕐 Time: ${new Date().toISOString()}`);

const server = http.createServer((req, res) => {
  console.log(`\n📥 Request received:`);
  console.log(`   Method: ${req.method}`);
  console.log(`   URL: ${req.url}`);
  console.log(`   Headers:`, req.headers);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');
  
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    console.log('   🔄 CORS preflight request');
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Simple routing
  if (req.url === '/health') {
    console.log('   ❤️  Health check request');
    const response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      server: 'debug-server'
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    console.log('   ✅ Health check response sent');
    
  } else if (req.url === '/') {
    console.log('   🏠 Root request');
    const response = {
      message: 'PAIM Debug Server is running',
      timestamp: new Date().toISOString(),
      endpoints: ['/health', '/api/status']
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    console.log('   ✅ Root response sent');
    
  } else if (req.url === '/api/status') {
    console.log('   📊 Status request');
    const response = {
      status: 'running',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    console.log('   ✅ Status response sent');
    
  } else {
    console.log('   ❌ 404 - Not found');
    const response = {
      error: 'Not Found',
      path: req.url,
      timestamp: new Date().toISOString()
    };
    res.writeHead(404);
    res.end(JSON.stringify(response, null, 2));
    console.log('   ✅ 404 response sent');
  }
});

server.on('listening', () => {
  console.log(`\n✅ Server is listening on port ${PORT}`);
  console.log(`🌐 Access URLs:`);
  console.log(`   http://localhost:${PORT}/`);
  console.log(`   http://localhost:${PORT}/health`);
  console.log(`   http://localhost:${PORT}/api/status`);
  console.log(`\n🧪 Test commands:`);
  console.log(`   curl http://localhost:${PORT}/health`);
  console.log(`   curl http://localhost:${PORT}/api/status`);
  console.log(`\n📡 Waiting for requests...\n`);
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

server.on('close', () => {
  console.log('🛑 Server closed');
});

// Start the server
server.listen(PORT);

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

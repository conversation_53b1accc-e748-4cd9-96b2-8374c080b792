# 🛠️ PAIM Development Guide

This guide covers the development workflow, architecture decisions, and best practices for contributing to PAIM.

## 📋 Project Status

### ✅ Completed Components (Phase 1 MVP)

1. **Project Foundation & Setup**
   - ✅ NestJS project structure with TypeScript
   - ✅ Comprehensive environment configuration
   - ✅ Development tooling (ESLint, Prettier, Jest)
   - ✅ Git setup with proper .gitignore

2. **Core Architecture Implementation**
   - ✅ Dependency injection container
   - ✅ Global services (logging, validation, error handling)
   - ✅ Middleware pipeline (logging, response transformation, rate limiting)
   - ✅ Comprehensive TypeScript interfaces

3. **Basic Skill System**
   - ✅ Skill interface and base classes
   - ✅ Skill registry service with dynamic registration
   - ✅ Skill router and dispatcher with fuzzy matching
   - ✅ Rich skill context system
   - ✅ 4 core skills migrated (onboarding, memory capture, reminders, roadmap)
   - ✅ Skill testing framework

4. **WhatsApp Integration**
   - ✅ Twilio WhatsApp API integration
   - ✅ Webhook controller with validation
   - ✅ Message parsing service
   - ✅ Message sending service with retry logic
   - ✅ Session management with automatic cleanup
   - ✅ Security validation and rate limiting
   - ✅ Async message queue processing

## 🏗️ Architecture Overview

### Core Services

**LoggerService**: Structured logging with context and performance metrics
- Skill execution logging
- API call tracking
- Memory operation logging
- User action tracking

**ErrorHandlerService**: Comprehensive error management
- Custom error types (PaimError, SkillExecutionError, etc.)
- User-friendly error messages
- Retry logic for external services
- Error context preservation

**ValidationService**: Input validation and sanitization
- Phone number validation
- Memory content validation
- Tag validation
- Input sanitization

**AppConfigService**: Type-safe configuration management
- Environment variable validation
- Service-specific configuration
- Feature flags
- Security configuration

### Skill System

**BaseSkill**: Abstract base class for all skills
- Common functionality (validation, logging, entity extraction)
- Response creation helpers
- Error handling
- Performance tracking

**SkillRegistryService**: Dynamic skill management
- Skill registration and discovery
- Configuration management
- Performance metrics
- Priority-based routing

**SkillRouterService**: Intelligent message routing
- Trigger-based matching
- Fuzzy matching for similar inputs
- Priority-based skill selection
- Timeout handling

**SkillContextService**: Rich execution environment
- User and session state management
- Memory and reminder operations
- State persistence
- Helper methods for common operations

### WhatsApp Integration

**TwilioService**: WhatsApp API integration
- Message sending with retry logic
- Media handling
- Webhook signature validation
- Connection testing

**MessageParsingService**: Intelligent message processing
- Message type detection (text, voice, image, document)
- Content extraction and sanitization
- Intent extraction
- Entity recognition

**SessionManagementService**: Conversation state management
- Session creation and cleanup
- Conversation history management
- Context and state persistence
- Multi-session support per user

**MessageProcessingService**: Async message processing
- Queue-based processing
- Priority handling
- Retry logic
- Error recovery

## 🔧 Development Workflow

### Setting Up Development Environment

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd paim-core
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Start Development Server**
   ```bash
   npm run start:dev
   ```

### Code Quality

**Linting and Formatting**
```bash
npm run lint          # Check code style
npm run format        # Format code
npm run typecheck     # Type checking
```

**Testing**
```bash
npm test              # Unit tests
npm run test:watch    # Watch mode
npm run test:cov      # Coverage report
npm run test:e2e      # End-to-end tests
```

### Building and Deployment

```bash
npm run build         # Production build
npm run start:prod    # Start production server
```

## 🧪 Testing Strategy

### Unit Testing

Each service and skill should have comprehensive unit tests:

```typescript
describe('MemoryCaptureSkill', () => {
  let skill: MemoryCaptureSkill;
  
  beforeEach(async () => {
    const module = await SkillTestHelper.createTestingModule(MemoryCaptureSkill);
    skill = module.get<MemoryCaptureSkill>(MemoryCaptureSkill);
  });

  it('should capture memory correctly', async () => {
    const { response, context } = await SkillTestHelper.testSkillExecution(
      skill,
      'remember that I love coffee'
    );
    
    SkillTestHelper.expectValidResponse(response);
    SkillTestHelper.expectMemorySaved(context, 'love coffee');
  });
});
```

### Integration Testing

Test complete workflows:

```typescript
describe('WhatsApp Integration', () => {
  it('should process incoming message end-to-end', async () => {
    const whatsappMessage = {
      From: 'whatsapp:+**********',
      To: 'whatsapp:+***********',
      Body: 'remember that I have a meeting tomorrow',
      MessageSid: 'test-message-id',
      AccountSid: 'test-account-id',
      NumMedia: '0'
    };
    
    const result = await messageProcessingService.processIncomingMessage(whatsappMessage);
    expect(result.success).toBe(true);
  });
});
```

## 📝 Adding New Features

### Adding a New Skill

1. **Create the skill class**
   ```typescript
   // src/skills/implementations/my-new-skill.ts
   @Injectable()
   export class MyNewSkill extends BaseSkill {
     readonly id = 'my-new-skill';
     readonly name = 'My New Skill';
     readonly description = 'What this skill does';
     readonly triggers = ['trigger1', 'trigger2'];
     readonly category = SkillCategory.UTILITY;
     
     async execute(input: string, context: ISkillContext): Promise<ISkillResponse> {
       // Implementation
     }
   }
   ```

2. **Register in skills module**
   ```typescript
   // src/skills/skills.module.ts
   @Module({
     providers: [
       // ... existing skills
       MyNewSkill,
     ],
     exports: [
       // ... existing skills
       MyNewSkill,
     ],
   })
   ```

3. **Add comprehensive tests**
   ```typescript
   // src/skills/implementations/my-new-skill.spec.ts
   describe('MyNewSkill', () => {
     // Test cases
   });
   ```

### Adding a New Service

1. **Create the service**
   ```typescript
   @Injectable()
   export class MyNewService {
     constructor(private logger: LoggerService) {}
     
     async doSomething(): Promise<void> {
       // Implementation
     }
   }
   ```

2. **Add to appropriate module**
3. **Add tests**
4. **Update interfaces if needed**

## 🔍 Debugging

### Logging

Use structured logging throughout:

```typescript
this.logger.log('Operation started', 'ServiceName');
this.logger.error('Operation failed', error.stack, 'ServiceName');
this.logger.logSkillExecution(skillId, userId, input, output, duration);
```

### Development Tools

- **Swagger UI**: http://localhost:3000/api/docs
- **Logs**: Check `logs/` directory
- **Debug Mode**: `npm run start:debug`

## 🚀 Next Steps

### Immediate Priorities

1. **Memory & Storage System** - Implement persistent storage
2. **AI Integration** - Add OpenAI GPT-4 integration
3. **Voice Processing** - Implement speech-to-text/text-to-speech
4. **Testing & Documentation** - Expand test coverage

### Future Enhancements

1. **Database Integration** - Replace JSON storage with proper database
2. **MCP Protocol** - Implement external service integrations
3. **Multi-user Support** - Add user management and permissions
4. **Arabic Language** - Full Arabic language support

## 📚 Resources

- [NestJS Documentation](https://docs.nestjs.com/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Twilio WhatsApp API](https://www.twilio.com/docs/whatsapp)
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Ensure all tests pass
5. Submit a pull request

For questions or support, please open an issue or contact the development team.

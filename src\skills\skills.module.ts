import { Module } from '@nestjs/common';
import { CoreModule } from '../core/core.module';

// Services
import { SkillRegistryService } from './services/skill-registry.service';
import { SkillRouterService } from './services/skill-router.service';
import { SkillContextService } from './services/skill-context.service';

// Skill implementations
import { OnboardingSkill } from './implementations/onboarding.skill';
import { MemoryCaptureSkill } from './implementations/memory-capture.skill';
import { ReminderSkill } from './implementations/reminder.skill';
import { RoadmapSkill } from './implementations/roadmap.skill';

@Module({
  imports: [CoreModule],
  providers: [
    // Core skill services
    SkillRegistryService,
    SkillRouterService,
    SkillContextService,

    // Skill implementations
    OnboardingSkill,
    MemoryCaptureSkill,
    ReminderSkill,
    RoadmapSkill,
  ],
  exports: [
    SkillRegistryService,
    SkillRouterService,
    SkillContextService,

    // Export skills for potential direct access
    OnboardingSkill,
    MemoryCaptureSkill,
    ReminderSkill,
    RoadmapSkill,
  ],
})
export class SkillsModule {}

import { Injectable } from '@nestjs/common';
import { IWhatsAppMessage, IUser } from '../../interfaces';
import { LoggerService, ErrorHandlerService } from '../../core';
import { MessageParsingService } from './message-parsing.service';
import { MessageSendingService } from './message-sending.service';
import { SessionManagementService } from './session-management.service';
import { SkillRouterService, SkillContextService } from '../../skills';
import { WhitelistService } from '../../registration/services/whitelist.service';
import { MemoryAppwriteService } from '../../skills/services/memory-appwrite.service';

export interface MessageProcessingResult {
  success: boolean;
  messageId?: string;
  error?: string;
  processingTime: number;
}

export interface QueuedMessage {
  id: string;
  whatsappMessage: IWhatsAppMessage;
  timestamp: Date;
  retryCount: number;
  priority: 'low' | 'normal' | 'high';
}

@Injectable()
export class MessageProcessingService {
  private messageQueue: QueuedMessage[] = [];
  private processing = false;
  private readonly MAX_QUEUE_SIZE = 1000;
  private readonly MAX_RETRIES = 3;
  // private readonly PROCESSING_TIMEOUT = 30000; // 30 seconds

  constructor(
    private messageParsingService: MessageParsingService,
    private messageSendingService: MessageSendingService,
    private sessionManagementService: SessionManagementService,
    private skillRouterService: SkillRouterService,
    private skillContextService: SkillContextService,
    private logger: LoggerService,
    private errorHandler: ErrorHandlerService,
    private whitelistService: WhitelistService,
    private memoryService: MemoryAppwriteService,
  ) {
    // Start processing queue
    this.startQueueProcessor();
  }

  async processIncomingMessage(
    whatsappMessage: IWhatsAppMessage,
  ): Promise<MessageProcessingResult> {
    const startTime = Date.now();

    try {
      // Validate message
      if (!this.messageParsingService.isValidMessage(whatsappMessage)) {
        return {
          success: false,
          error: 'Invalid message format',
          processingTime: Date.now() - startTime,
        };
      }

      // Add to queue for processing
      const queuedMessage = this.addToQueue(whatsappMessage);

      this.logger.log(
        `Message ${queuedMessage.id} added to processing queue`,
        'MessageProcessingService',
      );

      return {
        success: true,
        messageId: queuedMessage.id,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      const handledError = this.errorHandler.handleError(
        error instanceof Error ? error : new Error(String(error)),
        'MessageProcessingService',
      );

      return {
        success: false,
        error: handledError.message,
        processingTime: Date.now() - startTime,
      };
    }
  }

  async processStatusUpdate(statusData: any): Promise<void> {
    try {
      this.logger.log(
        `Processing status update for message ${statusData.MessageSid}: ${statusData.MessageStatus}`,
        'MessageProcessingService',
      );

      // Handle different status types
      switch (statusData.MessageStatus) {
        case 'delivered':
          await this.handleMessageDelivered(statusData);
          break;
        case 'failed':
        case 'undelivered':
          await this.handleMessageFailed(statusData);
          break;
        case 'read':
          await this.handleMessageRead(statusData);
          break;
        default:
          this.logger.debug(
            `Unhandled status: ${statusData.MessageStatus}`,
            'MessageProcessingService',
          );
      }
    } catch (error) {
      this.logger.error(
        'Failed to process status update',
        error instanceof Error ? error.stack : undefined,
        'MessageProcessingService',
      );
    }
  }

  private addToQueue(
    whatsappMessage: IWhatsAppMessage,
    priority: 'low' | 'normal' | 'high' = 'normal',
  ): QueuedMessage {
    if (this.messageQueue.length >= this.MAX_QUEUE_SIZE) {
      // Remove oldest low priority message
      const lowPriorityIndex = this.messageQueue.findIndex(
        (msg) => msg.priority === 'low',
      );
      if (lowPriorityIndex !== -1) {
        this.messageQueue.splice(lowPriorityIndex, 1);
      } else {
        // Remove oldest message
        this.messageQueue.shift();
      }
    }

    const queuedMessage: QueuedMessage = {
      id: `queue-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      whatsappMessage,
      timestamp: new Date(),
      retryCount: 0,
      priority,
    };

    // Insert based on priority
    if (priority === 'high') {
      this.messageQueue.unshift(queuedMessage);
    } else {
      this.messageQueue.push(queuedMessage);
    }

    return queuedMessage;
  }

  private startQueueProcessor(): void {
    setInterval(async () => {
      if (!this.processing && this.messageQueue.length > 0) {
        await this.processNextMessage();
      }
    }, 100); // Check every 100ms
  }

  private async processNextMessage(): Promise<void> {
    if (this.processing || this.messageQueue.length === 0) {
      return;
    }

    this.processing = true;
    const queuedMessage = this.messageQueue.shift();

    if (!queuedMessage) {
      this.processing = false;
      return;
    }

    try {
      await this.processQueuedMessage(queuedMessage);
    } catch (error) {
      await this.handleProcessingError(queuedMessage, error);
    } finally {
      this.processing = false;
    }
  }

  private async processQueuedMessage(
    queuedMessage: QueuedMessage,
  ): Promise<void> {
    const startTime = Date.now();

    try {
      const { whatsappMessage } = queuedMessage;

      // Get or create user
      const user = await this.getOrCreateUser(whatsappMessage);

      // If user is not whitelisted, send access denied message
      if (!user) {
        await this.sendAccessDeniedMessage(whatsappMessage.From);
        return;
      }

      // Get or create session
      const session =
        await this.sessionManagementService.getOrCreateSession(user);

      // Parse message
      const message = this.messageParsingService.parseIncomingMessage(
        whatsappMessage,
        user.id,
        session.sessionId,
      );

      // Add message to session
      await this.sessionManagementService.addMessageToSession(
        session.sessionId,
        message,
      );

      // Create skill context
      const context = this.skillContextService.createContext(
        user,
        session,
        message,
        {
          getUserConfig: async () => this.getUserConfig(user),
          setState: async (key, value) =>
            this.sessionManagementService.updateSessionState(
              session.sessionId,
              { [key]: value },
            ),
          getState: async (key) => session.state[key],
          saveMemory: async (memory) => this.saveMemory(memory, user.id),
          saveReminder: async (reminder) =>
            this.saveReminder(reminder, user.id),
          reply: (msg, tone) => this.formatReply(msg, tone || user.tone),
        },
      );

      // Route message to appropriate skill
      const skillResult = await this.skillRouterService.routeMessage(
        message.content,
        context,
      );

      // Send response
      if (skillResult.response) {
        await this.messageSendingService.sendResponse(
          skillResult.response,
          whatsappMessage.From,
        );

        // Add response to session
        const responseMessage =
          this.messageParsingService.createOutgoingMessage(
            skillResult.response.content,
            user.id,
            session.sessionId,
            skillResult.response.type,
          );

        await this.sessionManagementService.addMessageToSession(
          session.sessionId,
          responseMessage,
        );
      }

      const processingTime = Date.now() - startTime;

      this.logger.log(
        `Successfully processed message ${queuedMessage.id} in ${processingTime}ms`,
        'MessageProcessingService',
      );
    } catch (error) {
      throw error;
    }
  }

  private async handleProcessingError(
    queuedMessage: QueuedMessage,
    error: any,
  ): Promise<void> {
    queuedMessage.retryCount++;

    this.logger.error(
      `Processing failed for message ${queuedMessage.id} (attempt ${queuedMessage.retryCount}/${this.MAX_RETRIES})`,
      error instanceof Error ? error.stack : undefined,
      'MessageProcessingService',
    );

    if (queuedMessage.retryCount < this.MAX_RETRIES) {
      // Re-queue with lower priority
      queuedMessage.priority = 'low';
      this.messageQueue.push(queuedMessage);
    } else {
      // Send error message to user
      try {
        await this.messageSendingService.sendTextMessage(
          queuedMessage.whatsappMessage.From,
          "I'm sorry, I encountered an issue processing your message. Please try again later.",
        );
      } catch (sendError) {
        this.logger.error(
          'Failed to send error message to user',
          sendError instanceof Error ? sendError.stack : undefined,
          'MessageProcessingService',
        );
      }
    }
  }

  private async getOrCreateUser(
    whatsappMessage: IWhatsAppMessage,
  ): Promise<IUser | null> {
    const phoneNumber = whatsappMessage.From;
    const userId = `user-${phoneNumber.replace(/[^0-9]/g, '')}`;

    // Check if phone number is whitelisted
    const isWhitelisted =
      await this.whitelistService.isPhoneNumberWhitelisted(phoneNumber);
    const isEarlyAdopter =
      await this.whitelistService.isPhoneNumberEarlyAdopter(phoneNumber);

    // Get whitelist user info if available
    const whitelistUser =
      await this.whitelistService.findByPhoneNumber(phoneNumber);

    // If not whitelisted and registration is required, return null
    // This will trigger a "not authorized" message
    if (!isWhitelisted) {
      this.logger.log(`Non-whitelisted user attempted access: ${phoneNumber}`);
      return null;
    }

    // Link the whitelist user to WhatsApp user if not already linked
    if (whitelistUser && !whitelistUser.whatsappUserId) {
      await this.whitelistService.linkWhatsAppUser(whitelistUser.email, userId);
    }

    return {
      id: userId,
      phoneNumber,
      name: whitelistUser?.firstName,
      email: whitelistUser?.email,
      tone: 'friendly',
      isOnboarded: false,
      isWhitelisted: true,
      isEarlyAdopter,
      whitelistUserId: whitelistUser?.id,
      registrationSource: 'whatsapp',
      preferences: {
        language: 'en',
        timezone: 'UTC',
        voiceEnabled: true,
        notificationsEnabled: true,
        maxMemories: 1000,
        defaultSkills: [],
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActiveAt: new Date(),
    };
  }

  private async getUserConfig(user: IUser) {
    return {
      name: user.name,
      tone: user.tone,
      language: user.preferences.language,
      timezone: user.preferences.timezone,
      preferences: user.preferences,
    };
  }

  private async sendAccessDeniedMessage(phoneNumber: string): Promise<void> {
    const accessDeniedMessage = `
🚫 Access Not Available

Hi! Thanks for your interest in Sanad, your Personal AI Manager.

Currently, Sanad is in early access and available only to whitelisted users.

To join our waitlist:
1. Visit: https://sanad.kanousai.com/register
2. Fill out the registration form
3. Confirm your email
4. We'll notify you when access becomes available!

We're excited to have you join our community soon! 🚀

Best regards,
The Sanad Team
    `.trim();

    try {
      await this.messageSendingService.sendTextMessage(
        phoneNumber,
        accessDeniedMessage,
      );
      this.logger.log(`Sent access denied message to: ${phoneNumber}`);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to send access denied message to ${phoneNumber}:`,
        errorMessage,
      );
    }
  }

  private async saveMemory(memory: any, userId: string): Promise<void> {
    try {
      // Use the MemoryAppwriteService to save memory
      await this.memoryService.saveMemory({
        ...memory,
        userId,
      });
      this.logger.log(
        `Saved memory for user ${userId}: ${memory.content}`,
        'MessageProcessingService',
      );
    } catch (error) {
      this.logger.error(
        `Failed to save memory for user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'MessageProcessingService',
      );
      throw error;
    }
  }

  private async saveReminder(reminder: any, userId: string): Promise<void> {
    try {
      // Use the MemoryAppwriteService to save reminder
      await this.memoryService.saveReminder({
        ...reminder,
        userId,
      });
      this.logger.log(
        `Saved reminder for user ${userId}: ${reminder.content}`,
        'MessageProcessingService',
      );
    } catch (error) {
      this.logger.error(
        `Failed to save reminder for user ${userId}`,
        error instanceof Error ? error.stack : undefined,
        'MessageProcessingService',
      );
      throw error;
    }
  }

  private formatReply(message: string, tone: string): string {
    // Simple tone formatting
    switch (tone) {
      case 'friendly':
        return `😊 ${message}`;
      case 'pro':
        return message;
      case 'witty':
        return `😄 ${message}`;
      default:
        return message;
    }
  }

  private async handleMessageDelivered(statusData: any): Promise<void> {
    this.logger.log(
      `Message ${statusData.MessageSid} delivered successfully`,
      'MessageProcessingService',
    );
  }

  private async handleMessageFailed(statusData: any): Promise<void> {
    this.logger.error(
      `Message ${statusData.MessageSid} failed: ${statusData.ErrorMessage || 'Unknown error'}`,
      undefined,
      'MessageProcessingService',
    );
  }

  private async handleMessageRead(statusData: any): Promise<void> {
    this.logger.log(
      `Message ${statusData.MessageSid} read by user`,
      'MessageProcessingService',
    );
  }

  getQueueStatistics() {
    return {
      queueLength: this.messageQueue.length,
      processing: this.processing,
      priorityBreakdown: {
        high: this.messageQueue.filter((msg) => msg.priority === 'high').length,
        normal: this.messageQueue.filter((msg) => msg.priority === 'normal')
          .length,
        low: this.messageQueue.filter((msg) => msg.priority === 'low').length,
      },
    };
  }
}

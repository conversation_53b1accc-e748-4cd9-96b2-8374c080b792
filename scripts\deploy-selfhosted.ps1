# =============================================================================
# Complete Self-Hosted Deployment Script for Sanad (PowerShell)
# =============================================================================

Write-Host "🚀 Starting complete self-hosted deployment for Sanad..." -ForegroundColor Green

# Configuration
$APPWRITE_DROPLET_NAME = "sanad-appwrite"
$APPWRITE_DOMAIN = "appwrite.sanad.kanousai.com"
$APP_NAME = "sanad-paim"
$DROPLET_SIZE = "s-2vcpu-4gb"
$DROPLET_REGION = "nyc1"
$DROPLET_IMAGE = "docker-20-04"

# Functions
function Write-Log {
    param($Message, $Color = "Green")
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $Message" -ForegroundColor $Color
}

function Write-Error-Log {
    param($Message)
    Write-Host "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] ERROR: $Message" -ForegroundColor Red
    exit 1
}

# Check prerequisites
function Check-Prerequisites {
    Write-Log "Checking prerequisites..." "Blue"
    
    # Check if doctl is installed
    try {
        doctl version | Out-Null
        Write-Log "✅ doctl CLI found" "Green"
    } catch {
        Write-Error-Log "doctl CLI not found. Please install it first."
    }
    
    # Check if doctl is authenticated
    try {
        doctl account get | Out-Null
        Write-Log "✅ doctl authenticated" "Green"
    } catch {
        Write-Error-Log "doctl not authenticated. Please run: doctl auth init"
    }
    
    # Check if Node.js is installed
    try {
        node --version | Out-Null
        Write-Log "✅ Node.js found" "Green"
    } catch {
        Write-Error-Log "Node.js not found. Please install Node.js first."
    }
}

# Create Appwrite droplet
function Create-AppwriteDroplet {
    Write-Log "Creating Appwrite droplet..." "Blue"
    
    # Check if droplet already exists
    $existingDroplet = doctl compute droplet list --format Name --no-header | Where-Object { $_ -eq $APPWRITE_DROPLET_NAME }
    
    if ($existingDroplet) {
        Write-Log "✅ Droplet $APPWRITE_DROPLET_NAME already exists" "Yellow"
        return
    }
    
    # Get SSH keys
    $sshKeys = (doctl compute ssh-key list --format ID --no-header) -join ","
    
    # Create droplet
    Write-Log "Creating new droplet..." "Blue"
    doctl compute droplet create $APPWRITE_DROPLET_NAME --size $DROPLET_SIZE --image $DROPLET_IMAGE --region $DROPLET_REGION --ssh-keys $sshKeys --wait
    
    if ($LASTEXITCODE -eq 0) {
        Write-Log "✅ Droplet created successfully" "Green"
        
        # Get droplet IP
        $dropletInfo = doctl compute droplet list --format Name,PublicIPv4 --no-header | Where-Object { $_.StartsWith($APPWRITE_DROPLET_NAME) }
        $dropletIP = ($dropletInfo -split '\s+')[1]
        
        Write-Log "📍 Droplet IP: $dropletIP" "Cyan"
        Write-Log "📝 Please update your DNS records:" "Yellow"
        Write-Log "   A record: $APPWRITE_DOMAIN -> $dropletIP" "Yellow"
        Write-Log "   A record: functions.sanad.kanousai.com -> $dropletIP" "Yellow"
    } else {
        Write-Error-Log "Failed to create droplet"
    }
}

# Generate secure keys for Appwrite
function Generate-AppwriteKeys {
    Write-Log "Generating secure keys..." "Blue"
    
    # Generate random keys
    $opensslKey = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString() + (New-Guid).ToString()))
    $executorSecret = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString() + (New-Guid).ToString()))
    $dbRootPass = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString()))
    $dbPass = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((New-Guid).ToString()))
    
    # Update .env.appwrite file
    if (Test-Path ".env.appwrite") {
        $envContent = Get-Content ".env.appwrite"
        $envContent = $envContent -replace "your-32-character-secret-key-here", $opensslKey
        $envContent = $envContent -replace "your-executor-secret-key-here", $executorSecret
        $envContent = $envContent -replace "your-strong-root-password-here", $dbRootPass
        $envContent = $envContent -replace "your-strong-db-password-here", $dbPass
        $envContent | Set-Content ".env.appwrite"
        
        Write-Log "✅ Secure keys generated and updated in .env.appwrite" "Green"
    } else {
        Write-Log "⚠️ .env.appwrite file not found, skipping key generation" "Yellow"
    }
}

# Deploy existing app to Digital Ocean
function Deploy-Application {
    Write-Log "Deploying application to Digital Ocean..." "Blue"
    
    # Build the application
    Write-Log "Building application..." "Blue"
    npm run build
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error-Log "Build failed"
    }
    
    # Check if app exists
    $existingApp = doctl apps list --format ID,Spec.Name --no-header | Where-Object { $_.Contains($APP_NAME) }
    
    if ($existingApp) {
        $appId = ($existingApp -split '\s+')[0]
        Write-Log "Found existing app with ID: $appId" "Green"
        
        # Update existing app
        Write-Log "Updating existing app..." "Blue"
        doctl apps update $appId --spec .do/app.yaml
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "✅ App updated successfully!" "Green"
        } else {
            Write-Error-Log "App update failed"
        }
    } else {
        # Create new app
        Write-Log "Creating new app..." "Blue"
        doctl apps create --spec .do/app.yaml
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "✅ App created successfully!" "Green"
        } else {
            Write-Error-Log "App creation failed"
        }
    }
}

# Verify deployment
function Verify-Deployment {
    Write-Log "Verifying deployment..." "Blue"
    
    # Get app URL
    $appInfo = doctl apps list --format Spec.Name,DefaultIngress --no-header | Where-Object { $_.Contains($APP_NAME) }
    
    if ($appInfo) {
        $appUrl = ($appInfo -split '\s+')[1]
        Write-Log "🌐 Application URL: https://$appUrl" "Cyan"
        
        # Test health endpoint
        try {
            $response = Invoke-WebRequest -Uri "https://$appUrl/health" -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Log "✅ Health check passed" "Green"
            } else {
                Write-Log "⚠️ Health check returned status: $($response.StatusCode)" "Yellow"
            }
        } catch {
            Write-Log "⚠️ Health check failed - app may still be starting" "Yellow"
        }
    } else {
        Write-Log "⚠️ Could not determine app URL" "Yellow"
    }
}

# Main execution
function Main {
    Write-Log "Starting complete self-hosted deployment..." "Magenta"
    
    # Show deployment plan
    Write-Host ""
    Write-Host "Deployment Plan:" -ForegroundColor Blue
    Write-Host "1. Check prerequisites and authentication" -ForegroundColor Cyan
    Write-Host "2. Create Digital Ocean droplet for Appwrite (if needed)" -ForegroundColor Cyan
    Write-Host "3. Generate secure keys for Appwrite" -ForegroundColor Cyan
    Write-Host "4. Deploy/update application to Digital Ocean App Platform" -ForegroundColor Cyan
    Write-Host "5. Verify deployment" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Estimated time: 10-15 minutes" -ForegroundColor Yellow
    Write-Host "Estimated cost: ~$24/month for Appwrite droplet" -ForegroundColor Yellow
    Write-Host ""
    
    $continue = Read-Host "Do you want to continue? (y/N)"
    
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Log "Deployment cancelled by user" "Yellow"
        return
    }
    
    # Execute deployment steps
    Check-Prerequisites
    Create-AppwriteDroplet
    Generate-AppwriteKeys
    Deploy-Application
    Verify-Deployment
    
    Write-Log "🎉 Deployment completed!" "Green"
    Write-Host ""
    Write-Host "📋 Next Steps:" -ForegroundColor Blue
    Write-Host "1. Update DNS records for Appwrite domain" -ForegroundColor Cyan
    Write-Host "2. SSH to Appwrite droplet and set up Appwrite services" -ForegroundColor Cyan
    Write-Host "3. Configure Appwrite project and database schema" -ForegroundColor Cyan
    Write-Host "4. Update application environment variables" -ForegroundColor Cyan
    Write-Host ""
    
    # Get droplet IP for reference
    $dropletInfo = doctl compute droplet list --format Name,PublicIPv4 --no-header | Where-Object { $_.StartsWith($APPWRITE_DROPLET_NAME) }
    if ($dropletInfo) {
        $dropletIP = ($dropletInfo -split '\s+')[1]
        Write-Host "Appwrite Droplet IP: $dropletIP" -ForegroundColor Cyan
        Write-Host "SSH Command: ssh root@$dropletIP" -ForegroundColor Cyan
    }
}

# Run main function
Main

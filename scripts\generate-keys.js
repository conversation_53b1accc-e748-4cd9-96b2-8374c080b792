#!/usr/bin/env node

/**
 * Security Key Generator for Sanad Production Deployment
 * Generates secure JWT and encryption keys for production use
 */

const crypto = require('crypto');

console.log('🔐 Generating Secure Keys for Sanad Production Deployment\n');

// Generate JWT Secret (64 characters)
const jwtSecret = crypto.randomBytes(32).toString('hex');

// Generate Encryption Key (64 characters)
const encryptionKey = crypto.randomBytes(32).toString('hex');

// Generate Admin API Key (32 characters)
const adminApiKey = crypto.randomBytes(16).toString('hex');

console.log('✅ Generated Secure Keys:\n');

console.log('📋 Copy these to your Digital Ocean App Platform Environment Variables:\n');

console.log('JWT_SECRET=' + jwtSecret);
console.log('ENCRYPTION_KEY=' + encryptionKey);
console.log('ADMIN_API_KEY=' + adminApiKey);

console.log('\n🔒 Security Notes:');
console.log('- These keys are cryptographically secure');
console.log('- Store them securely in your environment variables');
console.log('- Never commit these keys to version control');
console.log('- Regenerate keys if compromised');

console.log('\n📝 Next Steps:');
console.log('1. Copy the keys above to Digital Ocean App Platform');
console.log('2. Go to: Apps → sanad-paim → Settings → Environment Variables');
console.log('3. Add each key as a new environment variable');
console.log('4. Redeploy the application');

console.log('\n🎯 Key Specifications:');
console.log('- JWT_SECRET: 64 characters (256-bit security)');
console.log('- ENCRYPTION_KEY: 64 characters (256-bit security)');
console.log('- ADMIN_API_KEY: 32 characters (128-bit security)');

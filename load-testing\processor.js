/**
 * Artillery Processor Functions
 * Custom functions for load testing scenarios
 */

const crypto = require('crypto');

module.exports = {
  // Generate random string
  randomString: (context, events, done) => {
    context.vars.randomString = () => {
      return crypto.randomBytes(8).toString('hex');
    };
    return done();
  },

  // Generate random integer
  randomInt: (context, events, done) => {
    context.vars.randomInt = (min, max) => {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    };
    return done();
  },

  // Generate random phone number
  randomPhoneNumber: (context, events, done) => {
    context.vars.phoneNumber = `1${Math.floor(Math.random() * 9000000000) + 1000000000}`;
    return done();
  },

  // Generate random user ID
  randomUserId: (context, events, done) => {
    context.vars.userId = `user_${crypto.randomBytes(4).toString('hex')}`;
    return done();
  },

  // Generate random message
  randomMessage: (context, events, done) => {
    const messages = [
      'Hello PAIM',
      'How are you today?',
      'What\'s the weather like?',
      'Tell me a joke',
      'Help me with my schedule',
      'What time is it?',
      'Set a reminder for tomorrow',
      'What\'s my next appointment?',
      'Send a message to John',
      'What\'s the latest news?',
      'Book a meeting for next week',
      'Cancel my 3pm appointment',
      'What\'s my calendar for today?',
      'Add groceries to my shopping list',
      'What\'s the traffic like?',
      'Find a good restaurant nearby',
      'What\'s the exchange rate?',
      'Translate hello to Arabic',
      'What\'s the capital of France?',
      'How do I cook pasta?',
    ];
    
    context.vars.message = messages[Math.floor(Math.random() * messages.length)];
    return done();
  },

  // Log response details
  logResponse: (requestParams, response, context, ee, next) => {
    console.log(`Response: ${response.statusCode} - ${requestParams.url}`);
    return next();
  },

  // Validate response
  validateResponse: (requestParams, response, context, ee, next) => {
    if (response.statusCode >= 400) {
      console.error(`Error response: ${response.statusCode} - ${requestParams.url}`);
      console.error(`Response body: ${response.body}`);
    }
    return next();
  },

  // Set authentication headers
  setAuthHeaders: (requestParams, context, ee, next) => {
    if (context.vars.authToken) {
      requestParams.headers = requestParams.headers || {};
      requestParams.headers['Authorization'] = `Bearer ${context.vars.authToken}`;
    }
    return next();
  },

  // Generate Twilio signature (simplified for testing)
  generateTwilioSignature: (requestParams, context, ee, next) => {
    const authToken = 'test_auth_token';
    const url = `http://localhost:3000${requestParams.url}`;
    const body = JSON.stringify(requestParams.json || {});
    
    // Simplified signature generation for testing
    const signature = crypto
      .createHmac('sha1', authToken)
      .update(url + body)
      .digest('base64');
    
    requestParams.headers = requestParams.headers || {};
    requestParams.headers['X-Twilio-Signature'] = signature;
    
    return next();
  },

  // Performance tracking
  trackPerformance: (requestParams, response, context, ee, next) => {
    const responseTime = response.timings?.response || 0;
    
    // Emit custom metrics
    ee.emit('customStat', 'response_time', responseTime);
    ee.emit('customStat', 'status_code', response.statusCode);
    
    // Track slow responses
    if (responseTime > 1000) {
      ee.emit('customStat', 'slow_responses', 1);
      console.warn(`Slow response: ${responseTime}ms - ${requestParams.url}`);
    }
    
    // Track errors
    if (response.statusCode >= 400) {
      ee.emit('customStat', 'error_responses', 1);
    }
    
    return next();
  },

  // Setup test context
  setupContext: (context, events, done) => {
    // Set default values
    context.vars.baseUrl = 'http://localhost:3000';
    context.vars.apiVersion = 'v1';
    
    // Generate test session ID
    context.vars.sessionId = crypto.randomBytes(16).toString('hex');
    
    return done();
  },

  // Cleanup after test
  cleanup: (context, events, done) => {
    // Perform any cleanup operations
    console.log(`Test session ${context.vars.sessionId} completed`);
    return done();
  }
};
